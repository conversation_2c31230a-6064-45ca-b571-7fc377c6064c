from django.db import models
from users.models import CustomUser
from consultations.models import Consultation
import uuid


class PaymentMethod(models.Model):
    """Available payment methods"""
    name = models.CharField(max_length=100)
    provider = models.CharField(max_length=100)  # e.g., 'chapa', 'stripe'
    is_active = models.BooleanField(default=True)
    configuration = models.JSONField(default=dict, blank=True)  # Store API keys, etc.

    def __str__(self):
        return f"{self.name} ({self.provider})"


class Payment(models.Model):
    """Payment records for consultations"""
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    # Unique payment identifier
    payment_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)

    # Related objects
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='payments')
    consultation = models.OneToOneField(Consultation, on_delete=models.CASCADE, related_name='payment')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)

    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='ETB')  # Ethiopian Birr
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')

    # External payment gateway details
    external_transaction_id = models.CharField(max_length=200, blank=True)
    gateway_response = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Additional metadata
    description = models.TextField(blank=True)
    failure_reason = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment {self.payment_id} - {self.amount} {self.currency} ({self.status})"


class PaymentHistory(models.Model):
    """Track payment status changes"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='history')
    previous_status = models.CharField(max_length=20)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Payment {self.payment.payment_id}: {self.previous_status} → {self.new_status}"


class Refund(models.Model):
    """Refund records"""
    REFUND_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    refund_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='refunds')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=REFUND_STATUS_CHOICES, default='pending')

    # External refund details
    external_refund_id = models.CharField(max_length=200, blank=True)
    gateway_response = models.JSONField(default=dict, blank=True)

    # Who initiated the refund
    initiated_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Refund {self.refund_id} - {self.amount} {self.payment.currency}"


class ConsultationPricing(models.Model):
    """Pricing for different types of consultations"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='ETB')
    duration_minutes = models.PositiveIntegerField(default=60)
    is_active = models.BooleanField(default=True)

    # Optional: psychologist-specific pricing
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   null=True, blank=True,
                                   limit_choices_to={'role': 'psychologist'})

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        psychologist_name = f" ({self.psychologist.username})" if self.psychologist else ""
        return f"{self.name}{psychologist_name} - {self.price} {self.currency}"
