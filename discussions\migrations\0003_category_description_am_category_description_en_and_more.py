# Generated by Django 5.2.1 on 2025-07-20 19:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('discussions', '0002_alter_category_options_alter_post_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='category',
            name='description_am',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='description_en',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='name_am',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='name_en',
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='content_am',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='content_en',
            field=models.Text<PERSON>ield(null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='post',
            name='title_am',
            field=models.Char<PERSON>ield(max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='title_en',
            field=models.Char<PERSON>ield(max_length=200, null=True),
        ),
    ]
