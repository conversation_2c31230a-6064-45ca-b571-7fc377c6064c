from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.db.models import Q, Avg
from django.core.paginator import Paginator

from .models import Resource, ResourceCategory, ResourceRating, ResourceDownload, ResourceBookmark
from users.models import CustomUser


def resource_list(request):
    """List all approved resources"""
    resources = Resource.objects.filter(is_approved=True, is_public=True)
    categories = ResourceCategory.objects.filter(is_active=True)

    # Search and filtering
    query = request.GET.get('q')
    category_id = request.GET.get('category')
    resource_type = request.GET.get('type')

    if query:
        resources = resources.filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(tags__icontains=query)
        )

    if category_id:
        resources = resources.filter(category_id=category_id)

    if resource_type:
        resources = resources.filter(resource_type=resource_type)

    # Pagination
    paginator = Paginator(resources.order_by('-created_at'), 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'resources/resource_list.html', {
        'page_obj': page_obj,
        'categories': categories,
        'selected_category': category_id,
        'selected_type': resource_type,
        'query': query,
        'resource_types': Resource.RESOURCE_TYPES
    })


def resource_by_category(request, category_id):
    """List resources by category"""
    category = get_object_or_404(ResourceCategory, id=category_id, is_active=True)
    resources = Resource.objects.filter(
        category=category,
        is_approved=True,
        is_public=True
    ).order_by('-created_at')

    paginator = Paginator(resources, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'resources/resource_by_category.html', {
        'category': category,
        'page_obj': page_obj
    })


def resource_detail(request, resource_id):
    """View resource details"""
    resource = get_object_or_404(Resource, id=resource_id, is_approved=True)

    # Check if user can view this resource
    if not resource.is_public and not request.user.is_authenticated:
        messages.error(request, "You need to be logged in to view this resource.")
        return redirect('login')

    # Increment view count
    resource.view_count += 1
    resource.save(update_fields=['view_count'])

    # Get ratings
    ratings = resource.ratings.all()
    avg_rating = ratings.aggregate(Avg('rating'))['rating__avg']
    user_rating = None

    if request.user.is_authenticated:
        user_rating = ratings.filter(user=request.user).first()

    # Check if bookmarked
    is_bookmarked = False
    if request.user.is_authenticated:
        is_bookmarked = ResourceBookmark.objects.filter(
            user=request.user, resource=resource
        ).exists()

    return render(request, 'resources/resource_detail.html', {
        'resource': resource,
        'avg_rating': avg_rating,
        'user_rating': user_rating,
        'is_bookmarked': is_bookmarked,
        'ratings_count': ratings.count()
    })


@login_required
def download_resource(request, resource_id):
    """Download a resource file"""
    resource = get_object_or_404(Resource, id=resource_id, is_approved=True)

    if not resource.file:
        raise Http404("File not found")

    # Log download
    ResourceDownload.objects.create(
        resource=resource,
        user=request.user,
        ip_address=request.META.get('REMOTE_ADDR', '127.0.0.1')
    )

    # Increment download count
    resource.download_count += 1
    resource.save(update_fields=['download_count'])

    # Serve file
    response = HttpResponse(resource.file.read(), content_type='application/octet-stream')
    response['Content-Disposition'] = f'attachment; filename="{resource.file.name}"'
    return response


# Psychologist Resource Management
@login_required
def upload_resource(request):
    """Upload a new resource (psychologists only)"""
    if request.user.role != 'psychologist' or not request.user.psychologist_approved:
        messages.error(request, "Only approved psychologists can upload resources.")
        return redirect('resource_list')

    categories = ResourceCategory.objects.filter(is_active=True)

    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        category_id = request.POST.get('category')
        resource_type = request.POST.get('resource_type')
        is_public = request.POST.get('is_public') == 'on'
        tags = request.POST.get('tags')
        file = request.FILES.get('file')
        external_url = request.POST.get('external_url')

        if title and description and category_id and resource_type:
            try:
                category = ResourceCategory.objects.get(id=category_id)

                resource = Resource.objects.create(
                    title=title,
                    description=description,
                    psychologist=request.user,
                    category=category,
                    resource_type=resource_type,
                    file=file,
                    external_url=external_url,
                    is_public=is_public,
                    tags=tags,
                    is_approved=False  # Requires admin approval
                )

                messages.success(request, "Resource uploaded successfully! It will be reviewed by our admin team.")
                return redirect('my_resources')

            except ResourceCategory.DoesNotExist:
                messages.error(request, "Invalid category selected.")
        else:
            messages.error(request, "Please fill in all required fields.")

    return render(request, 'resources/upload_resource.html', {
        'categories': categories,
        'resource_types': Resource.RESOURCE_TYPES
    })


@login_required
def my_resources(request):
    """List psychologist's own resources"""
    if request.user.role != 'psychologist':
        messages.error(request, "Only psychologists can access this page.")
        return redirect('resource_list')

    resources = Resource.objects.filter(psychologist=request.user).order_by('-created_at')

    return render(request, 'resources/my_resources.html', {
        'resources': resources
    })


@login_required
def edit_resource(request, resource_id):
    """Edit a resource"""
    resource = get_object_or_404(Resource, id=resource_id, psychologist=request.user)
    categories = ResourceCategory.objects.filter(is_active=True)

    if request.method == 'POST':
        resource.title = request.POST.get('title', resource.title)
        resource.description = request.POST.get('description', resource.description)

        category_id = request.POST.get('category')
        if category_id:
            try:
                resource.category = ResourceCategory.objects.get(id=category_id)
            except ResourceCategory.DoesNotExist:
                pass

        resource.resource_type = request.POST.get('resource_type', resource.resource_type)
        resource.is_public = request.POST.get('is_public') == 'on'
        resource.tags = request.POST.get('tags', resource.tags)
        resource.external_url = request.POST.get('external_url', resource.external_url)

        # Handle file upload
        if 'file' in request.FILES:
            resource.file = request.FILES['file']

        # If content changed, require re-approval
        if any([
            request.POST.get('title') != resource.title,
            request.POST.get('description') != resource.description,
            'file' in request.FILES
        ]):
            resource.is_approved = False

        resource.save()
        messages.success(request, "Resource updated successfully!")
        return redirect('my_resources')

    return render(request, 'resources/edit_resource.html', {
        'resource': resource,
        'categories': categories,
        'resource_types': Resource.RESOURCE_TYPES
    })


@login_required
def delete_resource(request, resource_id):
    """Delete a resource"""
    resource = get_object_or_404(Resource, id=resource_id, psychologist=request.user)

    if request.method == 'POST':
        resource.delete()
        messages.success(request, "Resource deleted successfully!")
        return redirect('my_resources')

    return render(request, 'resources/delete_resource.html', {
        'resource': resource
    })


# User Interactions
@login_required
def toggle_bookmark(request, resource_id):
    """Toggle bookmark for a resource"""
    resource = get_object_or_404(Resource, id=resource_id, is_approved=True)

    bookmark, created = ResourceBookmark.objects.get_or_create(
        user=request.user,
        resource=resource
    )

    if not created:
        bookmark.delete()
        messages.success(request, "Bookmark removed.")
    else:
        messages.success(request, "Resource bookmarked!")

    return redirect('resource_detail', resource_id=resource.id)


@login_required
def rate_resource(request, resource_id):
    """Rate a resource"""
    resource = get_object_or_404(Resource, id=resource_id, is_approved=True)

    if request.method == 'POST':
        rating_value = request.POST.get('rating')
        review = request.POST.get('review', '')

        if rating_value:
            try:
                rating_value = int(rating_value)
                if 1 <= rating_value <= 5:
                    rating, created = ResourceRating.objects.update_or_create(
                        resource=resource,
                        user=request.user,
                        defaults={
                            'rating': rating_value,
                            'review': review
                        }
                    )

                    action = "added" if created else "updated"
                    messages.success(request, f"Rating {action} successfully!")
                else:
                    messages.error(request, "Rating must be between 1 and 5.")
            except ValueError:
                messages.error(request, "Invalid rating value.")
        else:
            messages.error(request, "Please provide a rating.")

    return redirect('resource_detail', resource_id=resource.id)


@login_required
def my_bookmarks(request):
    """List user's bookmarked resources"""
    bookmarks = ResourceBookmark.objects.filter(user=request.user).order_by('-created_at')

    return render(request, 'resources/my_bookmarks.html', {
        'bookmarks': bookmarks
    })


# Admin Views
@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_pending_resources(request):
    """Admin view for pending resource approvals"""
    pending_resources = Resource.objects.filter(is_approved=False).order_by('-created_at')

    return render(request, 'resources/admin_pending_resources.html', {
        'pending_resources': pending_resources
    })


@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_approve_resource(request, resource_id):
    """Approve or reject a resource"""
    resource = get_object_or_404(Resource, id=resource_id)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'approve':
            resource.is_approved = True
            resource.approved_by = request.user
            resource.save()

            # Notify psychologist
            messages.success(request, f"Resource '{resource.title}' approved.")

        elif action == 'reject':
            resource.delete()
            messages.success(request, f"Resource '{resource.title}' rejected and deleted.")

    return redirect('admin_pending_resources')
