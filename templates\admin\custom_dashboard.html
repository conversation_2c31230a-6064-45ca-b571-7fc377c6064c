{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Admin Dashboard" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .admin-hero {
        background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .admin-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .admin-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(30, 64, 175, 0.1);
    }
    
    .card-header-admin {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #1e40af;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1e40af;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #64748b;
        font-weight: 500;
        font-size: 1rem;
    }
    
    .stat-change {
        font-size: 0.875rem;
        margin-top: 0.5rem;
        color: #10b981;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .action-btn {
        background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        color: var(--white);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border: none;
        text-align: center;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(30, 64, 175, 0.3);
        color: var(--white);
        text-decoration: none;
    }
    
    .action-btn.danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .action-btn.danger:hover {
        box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
    }
    
    .action-btn.success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .action-btn.success:hover {
        box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
    }
    
    .recent-activity {
        background: var(--white);
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .activity-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-info {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .activity-details {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .activity-time {
        color: #9ca3af;
        font-size: 0.8rem;
        text-align: right;
    }
    
    .welcome-admin {
        background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .admin-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="admin-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-cogs me-3"></i>
                        {% trans "Admin Dashboard" %}
                    </h1>
                    <p class="lead mb-0">{% trans "Manage the Ethiopian Psychological Consultation Platform" %}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Welcome Card -->
    <div class="welcome-admin">
        <div class="admin-avatar">
            <i class="fas fa-user-shield"></i>
        </div>
        <h3 class="mb-2">{% trans "Welcome, Administrator" %}</h3>
        <p class="mb-0">{% trans "You have full control over the platform. Monitor activity and manage users responsibly." %}</p>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_users|default:0 }}</div>
            <div class="stat-label">{% trans "Total Users" %}</div>
            <div class="stat-change">
                <i class="fas fa-arrow-up me-1"></i>+{{ new_users_this_week|default:0 }} {% trans "this week" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_psychologists|default:0 }}</div>
            <div class="stat-label">{% trans "Psychologists" %}</div>
            <div class="stat-change">
                <i class="fas fa-user-md me-1"></i>{% trans "Verified professionals" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_consultations|default:0 }}</div>
            <div class="stat-label">{% trans "Total Consultations" %}</div>
            <div class="stat-change">
                <i class="fas fa-calendar me-1"></i>+{{ new_consultations_this_week|default:0 }} {% trans "this week" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ pending_consultations|default:0 }}</div>
            <div class="stat-label">{% trans "Pending Requests" %}</div>
            <div class="stat-change">
                <i class="fas fa-clock me-1"></i>{% trans "Needs attention" %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="admin-card">
        <div class="card-header-admin">
            <h3 class="text-primary mb-0">
                <i class="fas fa-bolt me-2"></i>
                {% trans "Quick Actions" %}
            </h3>
        </div>
        <div class="p-4">
            <div class="quick-actions">
                <a href="/admin/users/customuser/" class="action-btn">
                    <i class="fas fa-users"></i>
                    {% trans "Manage Users" %}
                </a>
                <a href="/admin/consultations/consultation/" class="action-btn">
                    <i class="fas fa-calendar-check"></i>
                    {% trans "View Consultations" %}
                </a>
                <a href="/admin/messaging/chatbotinteraction/" class="action-btn success">
                    <i class="fas fa-comments"></i>
                    {% trans "Chat Logs" %}
                </a>
                <a href="/admin/payments/payment/" class="action-btn">
                    <i class="fas fa-credit-card"></i>
                    {% trans "Payments" %}
                </a>
                <a href="/admin/" class="action-btn danger">
                    <i class="fas fa-cog"></i>
                    {% trans "Django Admin" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Users -->
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="card-header-admin">
                    <h4 class="text-primary mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        {% trans "Recent Users" %}
                    </h4>
                </div>
                <div class="recent-activity">
                    {% if recent_users %}
                        {% for user in recent_users %}
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="activity-title">{{ user.get_full_name|default:user.username }}</div>
                                <div class="activity-details">
                                    {{ user.get_role_display }} • {{ user.email }}
                                </div>
                            </div>
                            <div class="activity-time">
                                {{ user.date_joined|timesince }} ago
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="activity-title">{% trans "No recent users" %}</div>
                                <div class="activity-details">{% trans "No new registrations" %}</div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Consultations -->
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="card-header-admin">
                    <h4 class="text-primary mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {% trans "Recent Consultations" %}
                    </h4>
                </div>
                <div class="recent-activity">
                    {% if recent_consultations %}
                        {% for consultation in recent_consultations %}
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="activity-title">{{ consultation.topic }}</div>
                                <div class="activity-details">
                                    {{ consultation.user.get_full_name|default:consultation.user.username }} • {{ consultation.get_status_display }}
                                </div>
                            </div>
                            <div class="activity-time">
                                {{ consultation.created_at|timesince }} ago
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="activity-title">{% trans "No recent consultations" %}</div>
                                <div class="activity-details">{% trans "No new consultation requests" %}</div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any admin dashboard specific JavaScript here
    console.log('Admin dashboard loaded');
    
    // Auto-refresh stats every 5 minutes
    setInterval(function() {
        // You could add AJAX calls here to update stats
        console.log('Stats refresh interval');
    }, 300000);
});
</script>
{% endblock %}
