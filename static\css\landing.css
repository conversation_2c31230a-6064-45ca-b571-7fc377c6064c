/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: #333;
    overflow-x: hidden;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    padding: 10rem 0 6rem;
    margin-top: 56px; /* Account for fixed navbar */
}

/* Features Section */
.icon-box {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.hover-effect {
    transition: all 0.3s ease;
}

.hover-effect:hover {
    transform: translateY(-10px);
}

/* Steps */
.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 99;
}

/* About Page Specific */
.about-hero {
    background: linear-gradient(rgba(13, 110, 253, 0.9), rgba(13, 110, 253, 0.9)), 
                url('../images/about-bg.jpg') no-repeat center center;
    background-size: cover;
    padding: 8rem 0 4rem;
    margin-top: 56px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 8rem 0 4rem;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
}