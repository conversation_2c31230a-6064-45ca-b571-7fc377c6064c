{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Rate Consultation" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .rating-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 80vh;
        padding: 2rem 0;
    }

    .rating-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        max-width: 800px;
        margin: 0 auto;
    }

    .rating-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 2rem;
        text-align: center;
    }

    .rating-form {
        padding: 2rem;
    }

    .star-rating {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin: 1rem 0;
    }

    .star {
        font-size: 2rem;
        color: var(--gray-300);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .star:hover,
    .star.active {
        color: #fbbf24;
        transform: scale(1.1);
    }

    .rating-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        border: 1px solid var(--gray-200);
    }

    .consultation-info {
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .psychologist-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .psychologist-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
    }

    .submit-btn {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        border: none;
        padding: 1rem 2rem;
        font-weight: 600;
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;
        width: 100%;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
</style>
{% endblock %}

{% block content %}
<div class="rating-container">
    <div class="container">
        <div class="rating-card">
            <!-- Header -->
            <div class="rating-header">
                <h2 class="mb-2">
                    <i class="fas fa-star me-2"></i>
                    {% trans "Rate Your Consultation" %}
                </h2>
                <p class="mb-0 opacity-90">{% trans "Your feedback helps us improve our services" %}</p>
            </div>

            <!-- Consultation Info -->
            <div class="rating-form">
                {% if consultation %}
                <div class="consultation-info">
                    <div class="psychologist-info">
                        <div class="psychologist-avatar">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Dr. {{ consultation.psychologist.get_full_name|default:consultation.psychologist.username }}</h5>
                            <p class="text-muted mb-1">{{ consultation.date|date:"F d, Y" }} at {{ consultation.time|time:"H:i" }}</p>
                            <span class="badge bg-success">{{ consultation.get_status_display }}</span>
                        </div>
                    </div>
                </div>
                {% endif %}

                <form method="post" id="ratingForm">
                    {% csrf_token %}

                    <!-- Overall Rating -->
                    <div class="rating-section">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-star text-warning me-2"></i>
                            {% trans "Overall Rating" %}
                        </h5>
                        <div class="text-center">
                            <div class="star-rating" data-rating="overall_rating">
                                <span class="star" data-value="1">★</span>
                                <span class="star" data-value="2">★</span>
                                <span class="star" data-value="3">★</span>
                                <span class="star" data-value="4">★</span>
                                <span class="star" data-value="5">★</span>
                            </div>
                            <input type="hidden" name="overall_rating" id="overall_rating" required>
                            <p class="text-muted mt-2" id="overall_rating_text">{% trans "Click to rate" %}</p>
                        </div>
                    </div>

                    <!-- Written Feedback -->
                    <div class="rating-section">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-comment me-2"></i>
                            {% trans "Your Feedback" %}
                        </h5>

                        <div class="mb-3">
                            <label for="positive_feedback" class="form-label">{% trans "What did you like about this consultation?" %}</label>
                            <textarea class="form-control" id="positive_feedback" name="positive_feedback" rows="3"
                                     placeholder="{% trans 'Share what went well...' %}"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="improvement_feedback" class="form-label">{% trans "What could be improved?" %}</label>
                            <textarea class="form-control" id="improvement_feedback" name="improvement_feedback" rows="3"
                                     placeholder="{% trans 'Suggest areas for improvement...' %}"></textarea>
                        </div>
                    </div>

                    <!-- Privacy Options -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                            <label class="form-check-label" for="is_public">
                                {% trans "Make this review public (helps other users)" %}
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary submit-btn">
                            <i class="fas fa-paper-plane me-2"></i>
                            {% trans "Submit Rating" %}
                        </button>
                    </div>
                </form>

                <div class="text-center mt-3">
                    <a href="{% url 'my_consultations' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to My Consultations" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle star ratings
    const stars = document.querySelectorAll('.star');
    const hiddenInput = document.getElementById('overall_rating');
    const ratingText = document.getElementById('overall_rating_text');

    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.value);
            hiddenInput.value = rating;

            // Update visual state
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });

            // Update text
            const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
            ratingText.textContent = ratingTexts[rating];
        });

        star.addEventListener('mouseenter', function() {
            const hoverRating = parseInt(this.dataset.value);
            stars.forEach((s, i) => {
                if (i < hoverRating) {
                    s.style.color = '#fbbf24';
                } else {
                    s.style.color = '';
                }
            });
        });
    });

    document.querySelector('.star-rating').addEventListener('mouseleave', function() {
        const currentRating = parseInt(hiddenInput.value) || 0;
        stars.forEach((s, i) => {
            if (i < currentRating) {
                s.style.color = '#fbbf24';
            } else {
                s.style.color = '';
            }
        });
    });

    // Form validation
    document.getElementById('ratingForm').addEventListener('submit', function(e) {
        const overallRating = document.getElementById('overall_rating').value;
        if (!overallRating) {
            e.preventDefault();
            alert('{% trans "Please provide an overall rating." %}');
            document.querySelector('.star-rating').scrollIntoView({ behavior: 'smooth' });
        }
    });
});
</script>
{% endblock %}
