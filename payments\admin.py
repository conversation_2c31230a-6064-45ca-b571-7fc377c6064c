from django.contrib import admin
from .models import (PaymentMethod, Payment, PaymentHistory,
                    Refund, ConsultationPricing)


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'provider', 'is_active')
    list_filter = ('provider', 'is_active')
    search_fields = ('name', 'provider')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_id', 'user', 'consultation', 'amount', 'currency', 'status', 'created_at')
    list_filter = ('status', 'currency', 'created_at')
    search_fields = ('payment_id', 'user__username', 'external_transaction_id')
    readonly_fields = ('payment_id', 'created_at', 'updated_at')


@admin.register(PaymentHistory)
class PaymentHistoryAdmin(admin.ModelAdmin):
    list_display = ('payment', 'previous_status', 'new_status', 'changed_by', 'created_at')
    list_filter = ('previous_status', 'new_status', 'created_at')
    readonly_fields = ('created_at',)


@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    list_display = ('refund_id', 'payment', 'amount', 'status', 'initiated_by', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('refund_id', 'payment__payment_id')
    readonly_fields = ('refund_id', 'created_at')


@admin.register(ConsultationPricing)
class ConsultationPricingAdmin(admin.ModelAdmin):
    list_display = ('name', 'psychologist', 'price', 'currency', 'duration_minutes', 'is_active')
    list_filter = ('currency', 'is_active', 'psychologist')
    search_fields = ('name', 'description', 'psychologist__username')
