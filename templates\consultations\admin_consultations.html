{% extends "base.html" %}
{% block content %}
<h2>All Consultation Requests</h2>

<table border="1" cellpadding="6">
  <tr>
    <th>User</th>
    <th>Psychologist</th>
    <th>Type</th>
    <th>Topic</th>
    <th>Status</th>
    <th>Scheduled</th>
    <th>Submitted</th>
    <th>Action</th>

  </tr>
  {% for c in consultations %}
  <tr>
    <td>{{ c.user.first_name }}</td>
    <td>{{ c.psychologist.first_name|default:"Unassigned" }}</td>
    <td><strong style="color: green;">{% if c.type == 'free' %}FREE{% else %}PAID{% endif %}</strong></td>
    <td>{{ c.topic }}</td>
    <td>{{ c.status }}</td>
    <td>{{ c.scheduled_date|default:"TBD" }}</td>
    <td>{{ c.created_at }}</td>
    <td><a href="{% url 'assign_psychologist' c.id %}">Reassign</a></td>
  </tr>
  {% empty %}
  <tr><td colspan="7">No consultations found.</td></tr>
  {% endfor %}
</table>

{% endblock %}
