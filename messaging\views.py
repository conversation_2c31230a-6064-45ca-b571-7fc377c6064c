from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
import json
import uuid

from .models import (Conversation, Message, FAQ, ChatbotInteraction,
                    PsychologistNotification, NotificationSubscription)
from users.models import CustomUser


@login_required
def conversation_list(request):
    """List all conversations for the current user"""
    conversations = request.user.conversations.all().order_by('-updated_at')
    return render(request, 'messaging/conversation_list.html', {
        'conversations': conversations
    })


@login_required
def conversation_detail(request, conversation_id):
    """View a specific conversation"""
    conversation = get_object_or_404(Conversation, id=conversation_id, participants=request.user)
    messages_list = conversation.messages.all()

    # Mark messages as read
    unread_messages = messages_list.filter(is_read=False).exclude(sender=request.user)
    unread_messages.update(is_read=True)

    if request.method == 'POST':
        content = request.POST.get('content')
        if content:
            Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content=content
            )
            conversation.save()  # Update timestamp
            return redirect('conversation_detail', conversation_id=conversation.id)

    return render(request, 'messaging/conversation_detail.html', {
        'conversation': conversation,
        'messages': messages_list,
        'other_participant': conversation.get_other_participant(request.user)
    })


@login_required
def start_conversation(request, user_id):
    """Start a new conversation with a user"""
    other_user = get_object_or_404(CustomUser, id=user_id)

    if other_user == request.user:
        messages.error(request, "You cannot start a conversation with yourself.")
        return redirect('conversation_list')

    # Check if conversation already exists
    existing_conversation = Conversation.objects.filter(
        participants=request.user
    ).filter(participants=other_user).first()

    if existing_conversation:
        return redirect('conversation_detail', conversation_id=existing_conversation.id)

    # Create new conversation
    conversation = Conversation.objects.create()
    conversation.participants.add(request.user, other_user)

    return redirect('conversation_detail', conversation_id=conversation.id)


def chatbot_interface(request):
    """Chatbot interface - accessible to all users including guests"""
    session_id = request.session.get('chatbot_session_id')
    if not session_id:
        session_id = str(uuid.uuid4())
        request.session['chatbot_session_id'] = session_id

    # Get recent interactions for this session
    recent_interactions = ChatbotInteraction.objects.filter(
        Q(user=request.user) if request.user.is_authenticated else Q(session_id=session_id)
    ).order_by('-created_at')[:10]

    return render(request, 'messaging/chatbot.html', {
        'recent_interactions': recent_interactions
    })


@csrf_exempt
def chatbot_api(request):
    """API endpoint for chatbot responses"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        user_message = data.get('message', '').strip()

        if not user_message:
            return JsonResponse({'error': 'Message is required'}, status=400)

        # Simple keyword matching for FAQ
        bot_response = get_bot_response(user_message)

        # Log interaction
        session_id = request.session.get('chatbot_session_id', str(uuid.uuid4()))
        ChatbotInteraction.objects.create(
            user=request.user if request.user.is_authenticated else None,
            session_id=session_id,
            user_message=user_message,
            bot_response=bot_response
        )

        return JsonResponse({
            'response': bot_response,
            'timestamp': timezone.now().isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)


def get_bot_response(user_message):
    """Generate bot response based on FAQ matching"""
    user_message_lower = user_message.lower()

    # Try to find matching FAQ
    faqs = FAQ.objects.filter(is_active=True)

    for faq in faqs:
        keywords = [kw.strip().lower() for kw in faq.keywords.split(',')]
        if any(keyword in user_message_lower for keyword in keywords):
            return faq.answer

    # Default responses for common patterns
    if any(word in user_message_lower for word in ['hello', 'hi', 'hey']):
        return "Hello! I'm here to help you with questions about our psychology platform. How can I assist you today?"

    if any(word in user_message_lower for word in ['help', 'support']):
        return "I can help you with information about consultations, finding psychologists, and using our platform. What specific topic would you like to know about?"

    if any(word in user_message_lower for word in ['consultation', 'appointment']):
        return "To book a consultation, you need to register as a user, browse our verified psychologists, and select an available time slot. Would you like more details about this process?"

    if any(word in user_message_lower for word in ['payment', 'cost', 'price']):
        return "We offer both free and paid consultations. Pricing varies by psychologist and consultation type. You can view specific pricing when booking a consultation."

    # Default fallback
    return "I'm sorry, I don't have specific information about that topic. You might want to contact our support team or browse our FAQ section for more detailed help."


# Psychologist Notification Views
@login_required
def notification_list(request):
    """List notifications from subscribed psychologists"""
    if request.user.role == 'psychologist':
        # Psychologists see their own notifications
        notifications = PsychologistNotification.objects.filter(
            psychologist=request.user, is_active=True
        ).order_by('-created_at')
    else:
        # Users see notifications from subscribed psychologists
        subscribed_psychologists = request.user.subscriptions.values_list('psychologist', flat=True)
        notifications = PsychologistNotification.objects.filter(
            psychologist__in=subscribed_psychologists, is_active=True
        ).order_by('-created_at')

    return render(request, 'messaging/notification_list.html', {
        'notifications': notifications
    })


@login_required
def create_notification(request):
    """Create a new notification (psychologists only)"""
    if request.user.role != 'psychologist':
        messages.error(request, "Only psychologists can create notifications.")
        return redirect('notification_list')

    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        notification_type = request.POST.get('notification_type')
        scheduled_date = request.POST.get('scheduled_date')
        location = request.POST.get('location')
        is_online = request.POST.get('is_online') == 'on'
        meeting_link = request.POST.get('meeting_link')

        if title and content and notification_type:
            notification = PsychologistNotification.objects.create(
                psychologist=request.user,
                title=title,
                content=content,
                notification_type=notification_type,
                scheduled_date=scheduled_date if scheduled_date else None,
                location=location,
                is_online=is_online,
                meeting_link=meeting_link
            )

            messages.success(request, "Notification created successfully!")
            return redirect('notification_detail', notification_id=notification.id)
        else:
            messages.error(request, "Please fill in all required fields.")

    return render(request, 'messaging/create_notification.html')


@login_required
def notification_detail(request, notification_id):
    """View notification details"""
    notification = get_object_or_404(PsychologistNotification, id=notification_id)

    # Check if user can view this notification
    if request.user.role == 'psychologist' and notification.psychologist != request.user:
        messages.error(request, "You can only view your own notifications.")
        return redirect('notification_list')

    if request.user.role != 'psychologist':
        # Check if user is subscribed to this psychologist
        is_subscribed = NotificationSubscription.objects.filter(
            user=request.user, psychologist=notification.psychologist
        ).exists()
        if not is_subscribed:
            messages.error(request, "You need to follow this psychologist to view their notifications.")
            return redirect('notification_list')

    return render(request, 'messaging/notification_detail.html', {
        'notification': notification
    })


@login_required
def subscribe_to_psychologist(request, psychologist_id):
    """Subscribe to psychologist notifications"""
    psychologist = get_object_or_404(CustomUser, id=psychologist_id, role='psychologist')

    subscription, created = NotificationSubscription.objects.get_or_create(
        user=request.user,
        psychologist=psychologist
    )

    if created:
        messages.success(request, f"You are now following {psychologist.get_full_name()}")
    else:
        messages.info(request, f"You are already following {psychologist.get_full_name()}")

    return redirect('psychologist_detail', user_id=psychologist.id)


@login_required
def unsubscribe_from_psychologist(request, psychologist_id):
    """Unsubscribe from psychologist notifications"""
    psychologist = get_object_or_404(CustomUser, id=psychologist_id, role='psychologist')

    try:
        subscription = NotificationSubscription.objects.get(
            user=request.user,
            psychologist=psychologist
        )
        subscription.delete()
        messages.success(request, f"You have unfollowed {psychologist.get_full_name()}")
    except NotificationSubscription.DoesNotExist:
        messages.info(request, f"You were not following {psychologist.get_full_name()}")

    return redirect('psychologist_detail', user_id=psychologist.id)
