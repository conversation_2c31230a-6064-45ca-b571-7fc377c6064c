from django.urls import path
from . import views

urlpatterns = [
    # Payment processing
    path('initiate/<int:consultation_id>/', views.initiate_payment, name='initiate_payment'),
    path('chapa/<uuid:payment_id>/', views.chapa_payment, name='chapa_payment'),
    path('callback/', views.payment_callback, name='payment_callback'),
    path('success/<uuid:payment_id>/', views.payment_success, name='payment_success'),
    path('failed/<uuid:payment_id>/', views.payment_failed, name='payment_failed'),
    
    # Payment history
    path('history/', views.payment_history, name='payment_history'),
    path('receipt/<uuid:payment_id>/', views.payment_receipt, name='payment_receipt'),
    
    # Refunds
    path('refund/<uuid:payment_id>/', views.request_refund, name='request_refund'),
    
    # Admin
    path('admin/payments/', views.admin_payment_list, name='admin_payment_list'),
    path('admin/refunds/', views.admin_refund_list, name='admin_refund_list'),
    
    # Pricing management
    path('pricing/', views.consultation_pricing, name='consultation_pricing'),
]
