from django.db import models

# Create your models here.
from django.db import models
from users.models import CustomUser

class Consultation(models.Model):
    CONSULTATION_TYPE_CHOICES = [
        ('free', 'Free'),
        ('paid', 'Paid'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='consultations')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_consultations', limit_choices_to={'role': 'psychologist'})
    type = models.CharField(max_length=10, choices=CONSULTATION_TYPE_CHOICES)
    topic = models.CharField(max_length=100)
    description = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    scheduled_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    rating = models.PositiveIntegerField(null=True, blank=True)
    feedback = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user.first_name} - {self.topic} ({self.status})"
    	
class ConsultationTopic(models.Model):
    name = models.CharField(max_length=100)
    is_paid = models.BooleanField(default=True)

    def __str__(self):
        return self.name
