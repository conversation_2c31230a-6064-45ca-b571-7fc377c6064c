from django.db import models
from users.models import CustomUser
from django.utils import timezone
from datetime import datetime, timedelta

class Consultation(models.Model):
    CONSULTATION_TYPE_CHOICES = [
        ('free', 'Free'),
        ('paid', 'Paid'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='consultations')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_consultations', limit_choices_to={'role': 'psychologist'})
    type = models.CharField(max_length=10, choices=CONSULTATION_TYPE_CHOICES)
    topic = models.CharField(max_length=100)
    description = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')

    # Enhanced scheduling
    scheduled_date = models.DateField(null=True, blank=True)
    scheduled_time = models.TimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(default=60)
    timezone = models.CharField(max_length=50, default='UTC')

    # Meeting details
    meeting_link = models.URLField(blank=True)
    meeting_notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Feedback and rating
    rating = models.PositiveIntegerField(null=True, blank=True, choices=[(i, i) for i in range(1, 6)])
    feedback = models.TextField(blank=True)

    # Completion tracking
    completed_at = models.DateTimeField(null=True, blank=True)

    @property
    def scheduled_datetime(self):
        """Combine scheduled date and time"""
        if self.scheduled_date and self.scheduled_time:
            return datetime.combine(self.scheduled_date, self.scheduled_time)
        return None

    @property
    def is_upcoming(self):
        """Check if consultation is upcoming"""
        if self.scheduled_datetime:
            return self.scheduled_datetime > timezone.now()
        return False

    @property
    def can_be_cancelled(self):
        """Check if consultation can be cancelled (24 hours before)"""
        if self.scheduled_datetime:
            return self.scheduled_datetime > timezone.now() + timedelta(hours=24)
        return True

    def __str__(self):
        return f"{self.user.first_name} - {self.topic} ({self.status})"
    	
class ConsultationTopic(models.Model):
    name = models.CharField(max_length=100)
    is_paid = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class TimeSlot(models.Model):
    """Available time slots for consultations"""
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'})
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    consultation = models.OneToOneField(Consultation, on_delete=models.SET_NULL,
                                      null=True, blank=True, related_name='time_slot')

    class Meta:
        unique_together = ['psychologist', 'date', 'start_time']
        ordering = ['date', 'start_time']

    def __str__(self):
        status = "Available" if self.is_available else "Booked"
        return f"{self.psychologist.username} - {self.date} {self.start_time}-{self.end_time} ({status})"

    @property
    def is_past(self):
        """Check if time slot is in the past"""
        slot_datetime = datetime.combine(self.date, self.start_time)
        return slot_datetime < timezone.now()


class ConsultationHistory(models.Model):
    """Track consultation status changes"""
    consultation = models.ForeignKey(Consultation, on_delete=models.CASCADE, related_name='history')
    previous_status = models.CharField(max_length=10)
    new_status = models.CharField(max_length=10)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Consultation {self.consultation.id}: {self.previous_status} → {self.new_status}"
