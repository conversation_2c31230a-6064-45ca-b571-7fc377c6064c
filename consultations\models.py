from django.db import models
from users.models import CustomUser
from django.utils import timezone
from datetime import datetime, timedelta

class Consultation(models.Model):
    CONSULTATION_TYPE_CHOICES = [
        ('free', 'Free'),
        ('paid', 'Paid'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='consultations')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_consultations', limit_choices_to={'role': 'psychologist'})
    type = models.CharField(max_length=10, choices=CONSULTATION_TYPE_CHOICES)
    topic = models.CharField(max_length=100)
    description = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')

    # Enhanced scheduling
    scheduled_date = models.DateField(null=True, blank=True)
    scheduled_time = models.TimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(default=60)
    timezone = models.CharField(max_length=50, default='UTC')

    # Meeting details
    meeting_link = models.URLField(blank=True)
    meeting_notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Feedback and rating
    rating = models.PositiveIntegerField(null=True, blank=True, choices=[(i, i) for i in range(1, 6)])
    feedback = models.TextField(blank=True)

    # Completion tracking
    completed_at = models.DateTimeField(null=True, blank=True)

    @property
    def scheduled_datetime(self):
        """Combine scheduled date and time"""
        if self.scheduled_date and self.scheduled_time:
            return datetime.combine(self.scheduled_date, self.scheduled_time)
        return None

    @property
    def is_upcoming(self):
        """Check if consultation is upcoming"""
        if self.scheduled_datetime:
            return self.scheduled_datetime > timezone.now()
        return False

    @property
    def can_be_cancelled(self):
        """Check if consultation can be cancelled (24 hours before)"""
        if self.scheduled_datetime:
            return self.scheduled_datetime > timezone.now() + timedelta(hours=24)
        return True

    def __str__(self):
        return f"{self.user.first_name} - {self.topic} ({self.status})"
    	
class ConsultationTopic(models.Model):
    name = models.CharField(max_length=100)
    is_paid = models.BooleanField(default=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class TimeSlot(models.Model):
    """Available time slots for consultations"""
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'})
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    consultation = models.OneToOneField(Consultation, on_delete=models.SET_NULL,
                                      null=True, blank=True, related_name='time_slot')

    class Meta:
        unique_together = ['psychologist', 'date', 'start_time']
        ordering = ['date', 'start_time']

    def __str__(self):
        status = "Available" if self.is_available else "Booked"
        return f"{self.psychologist.username} - {self.date} {self.start_time}-{self.end_time} ({status})"

    @property
    def is_past(self):
        """Check if time slot is in the past"""
        slot_datetime = datetime.combine(self.date, self.start_time)
        return slot_datetime < timezone.now()


class ConsultationHistory(models.Model):
    """Track consultation status changes"""
    consultation = models.ForeignKey(Consultation, on_delete=models.CASCADE, related_name='history')
    previous_status = models.CharField(max_length=10)
    new_status = models.CharField(max_length=10)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Consultation {self.consultation.id}: {self.previous_status} → {self.new_status}"


class ConsultationRecommendation(models.Model):
    """Recommendations made by psychologists after consultations"""
    RECOMMENDATION_TYPES = [
        ('follow_up', 'Follow-up Consultation'),
        ('medication', 'Medication Referral'),
        ('specialist', 'Specialist Referral'),
        ('therapy', 'Therapy Sessions'),
        ('lifestyle', 'Lifestyle Changes'),
        ('resources', 'Educational Resources'),
    ]

    consultation = models.ForeignKey(Consultation, on_delete=models.CASCADE, related_name='recommendations')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'},
                                   related_name='given_recommendations')
    client = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                              related_name='received_recommendations')
    recommendation_type = models.CharField(max_length=20, choices=RECOMMENDATION_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    urgency = models.CharField(max_length=10, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], default='medium')

    # For medication referrals
    referred_doctor = models.CharField(max_length=200, blank=True, help_text="Doctor or clinic name for medication referral")
    doctor_contact = models.CharField(max_length=100, blank=True, help_text="Contact information")

    # For follow-up consultations
    suggested_date = models.DateTimeField(null=True, blank=True)
    follow_up_notes = models.TextField(blank=True)

    # Status tracking
    is_acknowledged = models.BooleanField(default=False, help_text="Client has seen the recommendation")
    is_completed = models.BooleanField(default=False, help_text="Client has acted on the recommendation")
    client_notes = models.TextField(blank=True, help_text="Client's notes or feedback")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_recommendation_type_display()} for {self.client.username}"


class MedicationReferral(models.Model):
    """Specific medication referrals with detailed information"""
    recommendation = models.OneToOneField(ConsultationRecommendation, on_delete=models.CASCADE,
                                        related_name='medication_details')
    condition_diagnosed = models.CharField(max_length=200)
    recommended_medication_type = models.CharField(max_length=200, blank=True)
    dosage_notes = models.TextField(blank=True)
    duration_notes = models.TextField(blank=True)
    side_effects_warning = models.TextField(blank=True)
    contraindications = models.TextField(blank=True)

    # Referral details
    hospital_clinic_name = models.CharField(max_length=200)
    doctor_name = models.CharField(max_length=100, blank=True)
    doctor_specialization = models.CharField(max_length=100, blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)
    contact_email = models.EmailField(blank=True)
    address = models.TextField(blank=True)

    # Appointment details
    preferred_appointment_time = models.CharField(max_length=100, blank=True)
    insurance_accepted = models.BooleanField(default=True)
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Follow-up
    follow_up_required = models.BooleanField(default=True)
    follow_up_period = models.CharField(max_length=50, blank=True, help_text="e.g., '2 weeks', '1 month'")

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Medication referral for {self.recommendation.client.username} - {self.condition_diagnosed}"


class ConsultationFollowUp(models.Model):
    """Follow-up consultations recommended by psychologists"""
    original_consultation = models.ForeignKey(Consultation, on_delete=models.CASCADE,
                                            related_name='follow_ups')
    recommendation = models.OneToOneField(ConsultationRecommendation, on_delete=models.CASCADE,
                                        related_name='follow_up_details')

    # Follow-up details
    reason = models.TextField(help_text="Why is follow-up needed?")
    goals = models.TextField(help_text="What should be achieved in follow-up?")
    suggested_frequency = models.CharField(max_length=100, blank=True, help_text="e.g., 'Weekly for 4 weeks'")

    # Scheduling
    preferred_time_slots = models.TextField(blank=True, help_text="Preferred days/times")
    is_urgent = models.BooleanField(default=False)

    # Status
    client_agreed = models.BooleanField(default=False)
    scheduled_consultation = models.ForeignKey(Consultation, on_delete=models.SET_NULL,
                                             null=True, blank=True,
                                             related_name='follow_up_from')

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Follow-up for {self.recommendation.client.username} - {self.reason[:50]}"


class ConsultationRating(models.Model):
    """Rating and feedback for consultations"""
    consultation = models.OneToOneField(Consultation, on_delete=models.CASCADE, related_name='detailed_rating')
    client = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='given_ratings')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   related_name='received_ratings',
                                   limit_choices_to={'role': 'psychologist'})

    # Overall rating (1-5 stars)
    overall_rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)])

    # Detailed ratings
    professionalism = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)
    communication = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)
    helpfulness = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)
    punctuality = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)

    # Written feedback
    positive_feedback = models.TextField(blank=True, help_text="What did you like about this consultation?")
    improvement_feedback = models.TextField(blank=True, help_text="What could be improved?")
    additional_comments = models.TextField(blank=True)

    # Recommendation
    would_recommend = models.BooleanField(null=True, blank=True)
    would_book_again = models.BooleanField(null=True, blank=True)

    # Privacy
    is_anonymous = models.BooleanField(default=False, help_text="Hide your name in public reviews")
    is_public = models.BooleanField(default=True, help_text="Show this review publicly")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['consultation', 'client']

    def __str__(self):
        return f"Rating for {self.psychologist.username} - {self.overall_rating} stars"


class PsychologistRating(models.Model):
    """Aggregated rating for psychologists"""
    psychologist = models.OneToOneField(CustomUser, on_delete=models.CASCADE,
                                      related_name='psychologist_rating',
                                      limit_choices_to={'role': 'psychologist'})

    # Aggregated scores
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    total_ratings = models.IntegerField(default=0)

    # Detailed averages
    avg_professionalism = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    avg_communication = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    avg_helpfulness = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    avg_punctuality = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)

    # Rating distribution
    five_star_count = models.IntegerField(default=0)
    four_star_count = models.IntegerField(default=0)
    three_star_count = models.IntegerField(default=0)
    two_star_count = models.IntegerField(default=0)
    one_star_count = models.IntegerField(default=0)

    # Recommendation stats
    recommendation_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    return_client_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)

    last_updated = models.DateTimeField(auto_now=True)

    def update_ratings(self):
        """Recalculate all rating statistics"""
        ratings = ConsultationRating.objects.filter(psychologist=self.psychologist)

        if ratings.exists():
            from django.db.models import Avg, Count

            # Calculate averages
            avg_data = ratings.aggregate(
                avg_overall=Avg('overall_rating'),
                avg_prof=Avg('professionalism'),
                avg_comm=Avg('communication'),
                avg_help=Avg('helpfulness'),
                avg_punct=Avg('punctuality'),
                total=Count('id')
            )

            self.average_rating = avg_data['avg_overall'] or 0
            self.total_ratings = avg_data['total']
            self.avg_professionalism = avg_data['avg_prof'] or 0
            self.avg_communication = avg_data['avg_comm'] or 0
            self.avg_helpfulness = avg_data['avg_help'] or 0
            self.avg_punctuality = avg_data['avg_punct'] or 0

            # Calculate distribution
            self.five_star_count = ratings.filter(overall_rating=5).count()
            self.four_star_count = ratings.filter(overall_rating=4).count()
            self.three_star_count = ratings.filter(overall_rating=3).count()
            self.two_star_count = ratings.filter(overall_rating=2).count()
            self.one_star_count = ratings.filter(overall_rating=1).count()

            # Calculate recommendation percentages
            recommend_count = ratings.filter(would_recommend=True).count()
            return_count = ratings.filter(would_book_again=True).count()

            if self.total_ratings > 0:
                self.recommendation_percentage = (recommend_count / self.total_ratings) * 100
                self.return_client_percentage = (return_count / self.total_ratings) * 100

        self.save()

    def __str__(self):
        return f"{self.psychologist.username} - {self.average_rating} stars ({self.total_ratings} reviews)"
