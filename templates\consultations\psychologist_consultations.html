{% extends "base.html" %}
{% block content %}
<div class="max-w-6xl mx-auto px-4 py-8">
  <!-- Header -->
  <h2 class="text-3xl font-bold text-blue-700 mb-6">Psychologist Dash<PERSON></h2>

  <!-- Profile Summary -->
  <div class="bg-white rounded shadow p-6 mb-8">
    <h3 class="text-xl font-semibold mb-2 text-gray-700">Welcome, {{ request.user.first_name }}</h3>
    <h2>{{ request.user.first_name }} {{ request.user.last_name }}</h2>
    <p><strong>Email:</strong> {{ request.user.email }}</p>
    <p><strong>Experience:</strong> {{ request.user.experience_years }} years</p>
    <p><strong>Specialties:</strong>
      {% for s in request.user.specialties.all %}
        {{ s.name }}{% if not forloop.last %}, {% endif %}
      {% endfor %}
    </p>
    <p><strong>Qualifications:</strong> {{ request.user.qualifications }}</p>
    <p><strong>Rating:</strong> ⭐ {{ request.user.rating }}</p>
    <p><strong>Serves:</strong> {{ request.user.age_groups_served }}</p>
    <p><strong>Status:</strong>
      {% if request.user.is_verified %}
        <span class="text-green-600 font-medium">Verified</span>
      {% else %}
        <span class="text-red-600 font-medium">Pending Verification</span>
      {% endif %}
    </p>
  </div>

  <!-- Consultation Stats -->
  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-8">
    <div class="bg-blue-100 p-4 rounded shadow">
      <p class="text-2xl font-bold text-blue-800">{{ total }}</p>
      <p class="text-sm text-gray-700">Total Assigned</p>
    </div>
    <div class="bg-yellow-100 p-4 rounded shadow">
      <p class="text-2xl font-bold text-yellow-800">{{ pending }}</p>
      <p class="text-sm text-gray-700">Pending</p>
    </div>
    <div class="bg-green-100 p-4 rounded shadow">
      <p class="text-2xl font-bold text-green-800">{{ completed }}</p>
      <p class="text-sm text-gray-700">Completed</p>
    </div>
    <div class="bg-gray-100 p-4 rounded shadow">
      <p class="text-2xl font-bold text-gray-800">{{ rejected }}</p>
      <p class="text-sm text-gray-700">Rejected</p>
    </div>
  </div>

  <!-- Consultation List -->
  <div class="bg-white rounded shadow p-6">
    <h3 class="text-xl font-semibold mb-4 text-gray-700">Assigned Consultations</h3>
    {% if consultations %}
      <ul class="space-y-4">
        {% for c in consultations %}
          <li class="border p-4 rounded">
            <p><strong>Client:</strong> {{ c.user.first_name }} {{ c.user.last_name }}</p>
            <p><strong>Topic:</strong> {{ c.topic }}</p>
            <p><strong>Type:</strong> {{ c.type|title }}</p>
            <p><strong>Status:</strong> 
              <span class="{% if c.status == 'Completed' %}text-green-600{% elif c.status == 'Pending' %}text-yellow-600{% else %}text-gray-600{% endif %}">
                {{ c.status }}
              </span>
            </p>
            <p><strong>Scheduled:</strong> {{ c.scheduled_date|default:"TBD" }}</p>
            <a href="{% url 'update_consultation_status' c.id %}" class="mt-2 inline-block text-sm text-blue-600 hover:underline">
              ✏️ Update Status
            </a>
          </li>
        {% endfor %}
      </ul>
    {% else %}
      <p class="text-gray-600">No consultations assigned yet.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
