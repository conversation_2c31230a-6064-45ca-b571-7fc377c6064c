from modeltranslation.translator import register, TranslationOptions
from discussions.models import Category, Post
from messaging.models import FAQ
from resources.models import Resource
from consultations.models import ConsultationTopic


@register(Category)
class CategoryTranslationOptions(TranslationOptions):
    fields = ('name', 'description')


@register(Post)
class PostTranslationOptions(TranslationOptions):
    fields = ('title', 'content')


@register(FAQ)
class FAQTranslationOptions(TranslationOptions):
    fields = ('question', 'answer')


@register(Resource)
class ResourceTranslationOptions(TranslationOptions):
    fields = ('title', 'description')


@register(ConsultationTopic)
class ConsultationTopicTranslationOptions(TranslationOptions):
    fields = ('name', 'description')
