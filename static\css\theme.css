/* Ethiopian Psychology Platform - Professional Theme */

:root {
    /* Primary Colors */
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --primary-blue-light: #3b82f6;
    --secondary-blue: #60a5fa;
    --accent-blue: #93c5fd;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Status Colors */
    --success: #10b981;
    --success-light: #34d399;
    --warning: #f59e0b;
    --warning-light: #fbbf24;
    --danger: #ef4444;
    --danger-light: #f87171;
    --info: #3b82f6;
    --info-light: #60a5fa;
    
    /* Spacing Scale */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    --spacing-3xl: 4rem;      /* 64px */
    
    /* Border Radius */
    --radius-sm: 0.25rem;     /* 4px */
    --radius-md: 0.5rem;      /* 8px */
    --radius-lg: 0.75rem;     /* 12px */
    --radius-xl: 1rem;        /* 16px */
    --radius-2xl: 1.5rem;     /* 24px */
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', monospace;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-600);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-blue-dark);
}

/* Buttons */
.btn {
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    color: var(--white);
    padding: 0.75rem 1.5rem;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-outline-primary:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
}

/* Forms */
.form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all var(--transition-fast);
    background: var(--white);
}

.form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Navigation */
.navbar {
    background: var(--white) !important;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue) !important;
    font-size: 1.25rem;
}

.nav-link {
    color: var(--gray-600) !important;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    color: var(--primary-blue) !important;
}

.nav-link.active {
    color: var(--primary-blue) !important;
    font-weight: var(--font-weight-semibold);
}

/* Dropdown */
.dropdown-menu {
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
}

.dropdown-item {
    border-radius: var(--radius-sm);
    padding: 0.5rem 0.75rem;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-blue);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-weight: var(--font-weight-medium);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left-color: var(--success);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left-color: var(--danger);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left-color: var(--warning);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left-color: var(--info);
}

/* Utilities */
.text-primary { color: var(--primary-blue) !important; }
.text-secondary { color: var(--gray-600) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-info { color: var(--info) !important; }
.text-muted { color: var(--gray-500) !important; }

.bg-primary { background-color: var(--primary-blue) !important; }
.bg-secondary { background-color: var(--gray-100) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-info { background-color: var(--info) !important; }

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    
    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .navbar-nav {
        padding-top: var(--spacing-md);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
