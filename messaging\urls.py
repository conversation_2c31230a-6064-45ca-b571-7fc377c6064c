from django.urls import path
from . import views

urlpatterns = [
    # Private messaging
    path('', views.conversation_list, name='conversation_list'),
    path('conversation/<int:conversation_id>/', views.conversation_detail, name='conversation_detail'),
    path('start/<int:user_id>/', views.start_conversation, name='start_conversation'),
    
    # Chatbot
    path('chatbot/', views.chatbot_interface, name='chatbot_interface'),
    path('chatbot/api/', views.chatbot_api, name='chatbot_api'),
    
    # Psychologist notifications
    path('notifications/', views.notification_list, name='notification_list'),
    path('notifications/create/', views.create_notification, name='create_notification'),
    path('notifications/<int:notification_id>/', views.notification_detail, name='notification_detail'),
    path('subscribe/<int:psychologist_id>/', views.subscribe_to_psychologist, name='subscribe_to_psychologist'),
    path('unsubscribe/<int:psychologist_id>/', views.unsubscribe_from_psychologist, name='unsubscribe_from_psychologist'),
]
