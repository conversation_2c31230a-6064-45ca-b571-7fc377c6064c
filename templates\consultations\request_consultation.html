{% extends "base.html" %}
{% block title %}Request Consultation | ECPI{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-calendar-plus me-2"></i> Request Consultation</h3>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="id_type" class="form-label">Consultation Type</label>
                            {{ form.type }}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="id_mode" class="form-label">Mode</label>
                            {{ form.mode }}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_topic" class="form-label">Topic</label>
                        {{ form.topic }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_description" class="form-label">Description</label>
                        {{ form.description }}
                        <div class="form-text">Please describe your issue in detail</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_scheduled_date" class="form-label">Preferred Date & Time</label>
                        {{ form.scheduled_date }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_duration" class="form-label">Duration (minutes)</label>
                        {{ form.duration }}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize datepicker
document.addEventListener('DOMContentLoaded', function() {
    flatpickr("#id_scheduled_date", {
        enableTime: true,
        dateFormat: "Y-m-d H:i",
        minDate: "today",
        time_24hr: true
    });
});
</script>
{% endblock %}