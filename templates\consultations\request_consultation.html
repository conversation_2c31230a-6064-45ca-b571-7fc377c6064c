{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Request Consultation" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .consultation-hero {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: var(--white);
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    .consultation-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .consultation-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(37, 99, 235, 0.1);
    }
    
    .card-header-custom {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .form-section {
        margin-bottom: 2.5rem;
        padding: 2rem;
        background: #f8fafc;
        border-radius: 16px;
        border-left: 4px solid #2563eb;
    }
    
    .form-section h5 {
        color: #2563eb;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 600;
    }
    
    .consultation-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .type-card {
        background: var(--white);
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
    }
    
    .type-card:hover {
        border-color: #2563eb;
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(37, 99, 235, 0.15);
    }
    
    .type-card.selected {
        border-color: #2563eb;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }
    
    .type-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }
    
    .type-card.free .type-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .specialty-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .specialty-chip {
        background: var(--white);
        border: 1px solid #e2e8f0;
        border-radius: 25px;
        padding: 0.75rem 1.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .specialty-chip:hover {
        border-color: #2563eb;
        background: #dbeafe;
    }
    
    .specialty-chip.selected {
        background: #2563eb;
        color: var(--white);
        border-color: #2563eb;
    }
    
    .mode-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .mode-option {
        background: var(--white);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .mode-option:hover {
        border-color: #2563eb;
        background: #f8fafc;
    }
    
    .mode-option.selected {
        border-color: #2563eb;
        background: #dbeafe;
    }
    
    .form-control, .form-select {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        border: none;
        padding: 1rem 3rem;
        font-weight: 600;
        border-radius: 12px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 2rem;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 24px rgba(37, 99, 235, 0.3);
    }
    
    .pricing-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: 16px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .price-display {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 1rem 0;
    }
    
    .info-sidebar {
        background: var(--white);
        border-radius: 16px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #e2e8f0;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 3rem;
        gap: 1rem;
    }
    
    .step {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: #f1f5f9;
        border-radius: 25px;
        color: #64748b;
        font-weight: 500;
    }
    
    .step.active {
        background: #2563eb;
        color: var(--white);
    }
    
    .step-number {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .feature-list {
        list-style: none;
        padding: 0;
    }
    
    .feature-list li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
        color: #475569;
    }
    
    .feature-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #10b981;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 0.75rem;
    }

    .psychologist-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #e2e8f0;
    }

    .psychologist-card:hover {
        border-color: #2563eb;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    }

    .psychologist-card.selected {
        border-color: #2563eb;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }

    .psychologist-card.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .psychologist-card.disabled:hover {
        transform: none;
        box-shadow: none;
        border-color: #e2e8f0;
    }

    .psychologist-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const dateInput = document.getElementById('scheduled_date');
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    dateInput.min = now.toISOString().slice(0, 16);

    // Update duration based on consultation type
    const durationSelect = document.getElementById('duration');
    const pricingCard = document.getElementById('pricing-display');
    const priceAmount = document.getElementById('price-amount');
    const priceDescription = document.getElementById('price-description');

    // Form validation
    const form = document.getElementById('consultationForm');
    form.addEventListener('submit', function(e) {
        const type = document.getElementById('consultation-type').value;
        const specialty = document.getElementById('selected-specialty').value;
        const mode = document.getElementById('session-mode').value;

        if (!type) {
            e.preventDefault();
            alert('{% trans "Please select a consultation type." %}');
            return;
        }

        if (!specialty) {
            e.preventDefault();
            alert('{% trans "Please select an area of focus." %}');
            return;
        }

        if (!mode) {
            e.preventDefault();
            alert('{% trans "Please select a session format." %}');
            return;
        }
    });
});

// Select consultation type
function selectType(type, element) {
    // Remove selected class from all type cards
    document.querySelectorAll('.type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selected class to clicked card
    element.classList.add('selected');

    // Set hidden input value
    document.getElementById('consultation-type').value = type;

    // Update pricing display
    const pricingCard = document.getElementById('pricing-display');
    const priceAmount = document.getElementById('price-amount');
    const priceDescription = document.getElementById('price-description');
    const durationSelect = document.getElementById('duration');

    if (type === 'free') {
        pricingCard.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        priceAmount.textContent = '{% trans "Free" %}';
        priceDescription.textContent = '{% trans "30-minute introductory session" %}';
        durationSelect.value = '30';
        durationSelect.disabled = true;
    } else {
        pricingCard.style.background = 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)';
        priceAmount.textContent = '500 ETB';
        priceDescription.textContent = '{% trans "Professional 50-minute consultation" %}';
        durationSelect.value = '50';
        durationSelect.disabled = false;
    }
}

// Select specialty
function selectSpecialty(specialty, element) {
    // Remove selected class from all specialty chips
    document.querySelectorAll('.specialty-chip').forEach(chip => {
        chip.classList.remove('selected');
    });

    // Add selected class to clicked chip
    element.classList.add('selected');

    // Set hidden input value
    document.getElementById('selected-specialty').value = specialty;
}

// Select session mode
function selectMode(mode, element) {
    // Remove selected class from all mode options
    document.querySelectorAll('.mode-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selected class to clicked option
    element.classList.add('selected');

    // Set hidden input value
    document.getElementById('session-mode').value = mode;
}

// Auto-select default mode
document.addEventListener('DOMContentLoaded', function() {
    // Auto-select meeting as default
    const defaultMode = document.querySelector('.mode-option');
    if (defaultMode) {
        selectMode('meeting', defaultMode);
    }

    // Handle psychologist selection toggle
    const autoAssign = document.getElementById('auto_assign');
    const choosePsychologist = document.getElementById('choose_psychologist');
    const psychologistArea = document.getElementById('psychologist-selection-area');

    choosePsychologist.addEventListener('change', function() {
        if (this.checked) {
            psychologistArea.style.display = 'block';
            loadPsychologists();
        }
    });

    autoAssign.addEventListener('change', function() {
        if (this.checked) {
            psychologistArea.style.display = 'none';
            document.getElementById('selected-psychologist').value = '';
        }
    });

    // Handle psychologist filter
    const psychologistFilter = document.getElementById('psychologist-filter');
    psychologistFilter.addEventListener('change', function() {
        loadPsychologists(this.value);
    });
});

// Load psychologists
function loadPsychologists(specialization = '') {
    const psychologistList = document.getElementById('psychologist-list');

    // Show loading
    psychologistList.innerHTML = `
        <div class="col-12 text-center py-4">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">{% trans "Loading..." %}</span>
            </div>
            <p class="mt-2 text-muted">{% trans "Loading psychologists..." %}</p>
        </div>
    `;

    // Make API call to fetch psychologists
    const url = new URL('{% url "api_psychologists" %}', window.location.origin);
    if (specialization) {
        url.searchParams.append('specialization', specialization);
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            let html = '';

            if (data.psychologists && data.psychologists.length > 0) {
                data.psychologists.forEach(psych => {
                    const availabilityClass = psych.available ? 'text-success' : 'text-danger';
                    const availabilityText = psych.available ? '{% trans "Available" %}' : '{% trans "Busy" %}';
                    const disabledClass = psych.available ? '' : 'disabled';

                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card psychologist-card ${disabledClass}" onclick="${psych.available ? `selectPsychologist(${psych.id}, '${psych.name}', this)` : ''}">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="psychologist-avatar me-3">
                                            <i class="fas fa-user-md"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">${psych.name}</h6>
                                            <small class="text-muted">${psych.specialization}</small>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">${psych.experience}</small><br>
                                            <small class="text-warning">★ ${psych.rating}</small>
                                        </div>
                                        <span class="badge ${availabilityClass}">${availabilityText}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = `
                    <div class="col-12 text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-user-md fa-3x mb-3"></i>
                            <h5>{% trans "No psychologists found" %}</h5>
                            <p>{% trans "Try adjusting your filter or select auto-assign." %}</p>
                        </div>
                    </div>
                `;
            }

            psychologistList.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading psychologists:', error);
            psychologistList.innerHTML = `
                <div class="col-12 text-center py-4">
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>{% trans "Error loading psychologists" %}</h5>
                        <p>{% trans "Please try again or select auto-assign." %}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadPsychologists('${specialization}')">
                            {% trans "Retry" %}
                        </button>
                    </div>
                </div>
            `;
        });
}

// Select psychologist
function selectPsychologist(id, name, element) {
    // Remove selected class from all cards
    document.querySelectorAll('.psychologist-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selected class to clicked card
    element.classList.add('selected');

    // Set hidden input value
    document.getElementById('selected-psychologist').value = id;
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="consultation-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        {% trans "Book Your Consultation" %}
                    </h1>
                    <p class="lead mb-0">
                        {% trans "Connect with licensed psychologists for professional mental health support tailored to your needs" %}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active">
            <div class="step-number">1</div>
            <span>{% trans "Choose Service" %}</span>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <span>{% trans "Schedule" %}</span>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <span>{% trans "Confirm" %}</span>
        </div>
    </div>

    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="consultation-card">
                <div class="card-header-custom">
                    <h3 class="text-primary mb-2">
                        <i class="fas fa-calendar-plus me-2"></i>
                        {% trans "Consultation Request" %}
                    </h3>
                    <p class="text-muted mb-0">{% trans "Please select your preferred consultation type and provide details about your needs" %}</p>
                </div>
                
                <div class="p-4">
                    <form method="POST" id="consultationForm">
                        {% csrf_token %}
                        
                        <!-- Consultation Type Selection -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-heart"></i>
                                {% trans "Choose Your Consultation Type" %}
                            </h5>
                            
                            <div class="consultation-type-grid">
                                <div class="type-card free" onclick="selectType('free', this)">
                                    <div class="type-icon">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                    <h6 class="fw-bold">{% trans "Free Initial Consultation" %}</h6>
                                    <p class="text-muted mb-0">{% trans "30-minute introductory session to understand your needs" %}</p>
                                    <div class="mt-2">
                                        <span class="badge bg-success">{% trans "Free" %}</span>
                                    </div>
                                </div>
                                
                                <div class="type-card" onclick="selectType('paid', this)">
                                    <div class="type-icon">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                    <h6 class="fw-bold">{% trans "Professional Consultation" %}</h6>
                                    <p class="text-muted mb-0">{% trans "Full 50-minute session with licensed psychologist" %}</p>
                                    <div class="mt-2">
                                        <span class="badge bg-primary">{% trans "500 ETB" %}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <input type="hidden" name="type" id="consultation-type" required>
                        </div>
                        
                        <!-- Specialty Selection -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-brain"></i>
                                {% trans "Area of Focus" %}
                            </h5>
                            <p class="text-muted mb-3">{% trans "Select the area you'd like to focus on during your consultation" %}</p>
                            
                            <div class="specialty-grid">
                                <div class="specialty-chip" onclick="selectSpecialty('Anxiety & Stress', this)">
                                    <i class="fas fa-heart-pulse me-2"></i>{% trans "Anxiety & Stress" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Depression', this)">
                                    <i class="fas fa-brain me-2"></i>{% trans "Depression" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Relationships', this)">
                                    <i class="fas fa-heart me-2"></i>{% trans "Relationships" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Family Issues', this)">
                                    <i class="fas fa-home me-2"></i>{% trans "Family Issues" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Work & Career', this)">
                                    <i class="fas fa-briefcase me-2"></i>{% trans "Work & Career" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Self-Esteem', this)">
                                    <i class="fas fa-star me-2"></i>{% trans "Self-Esteem" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Trauma', this)">
                                    <i class="fas fa-shield-alt me-2"></i>{% trans "Trauma" %}
                                </div>
                                <div class="specialty-chip" onclick="selectSpecialty('Other', this)">
                                    <i class="fas fa-plus me-2"></i>{% trans "Other" %}
                                </div>
                            </div>
                            
                            <input type="hidden" name="topic" id="selected-specialty" required>
                        </div>

                        <!-- Psychologist Selection -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-user-md"></i>
                                {% trans "Choose Your Psychologist" %}
                            </h5>
                            <p class="text-muted mb-3">{% trans "Select a psychologist based on their specialization and experience, or let us assign one for you" %}</p>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="psychologist_selection" id="auto_assign" value="auto" checked>
                                        <label class="form-check-label" for="auto_assign">
                                            <strong>{% trans "Auto-assign" %}</strong><br>
                                            <small class="text-muted">{% trans "We'll match you with the best psychologist for your needs" %}</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="psychologist_selection" id="choose_psychologist" value="choose">
                                        <label class="form-check-label" for="choose_psychologist">
                                            <strong>{% trans "Choose Specific Psychologist" %}</strong><br>
                                            <small class="text-muted">{% trans "Browse and select your preferred psychologist" %}</small>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div id="psychologist-selection-area" style="display: none;">
                                <div class="mb-3">
                                    <label for="psychologist-filter" class="form-label">{% trans "Filter by Specialization" %}</label>
                                    <select id="psychologist-filter" class="form-select">
                                        <option value="">{% trans "All Specializations" %}</option>
                                        <option value="anxiety">{% trans "Anxiety & Stress" %}</option>
                                        <option value="depression">{% trans "Depression" %}</option>
                                        <option value="relationships">{% trans "Relationships" %}</option>
                                        <option value="family">{% trans "Family Therapy" %}</option>
                                        <option value="trauma">{% trans "Trauma & PTSD" %}</option>
                                    </select>
                                </div>

                                <div id="psychologist-list" class="row">
                                    <!-- Psychologists will be loaded here via JavaScript -->
                                    <div class="col-12 text-center py-4">
                                        <div class="spinner-border text-success" role="status">
                                            <span class="visually-hidden">{% trans "Loading..." %}</span>
                                        </div>
                                        <p class="mt-2 text-muted">{% trans "Loading psychologists..." %}</p>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="psychologist" id="selected-psychologist">
                        </div>

                        <!-- Session Mode -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-video"></i>
                                {% trans "Preferred Session Format" %}
                            </h5>
                            
                            <div class="mode-selector">
                                <div class="mode-option" onclick="selectMode('messaging', this)">
                                    <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                                    <h6>{% trans "Messaging" %}</h6>
                                    <small class="text-muted">{% trans "Text-based conversation" %}</small>
                                </div>
                                <div class="mode-option" onclick="selectMode('meeting', this)">
                                    <i class="fas fa-video fa-2x text-primary mb-2"></i>
                                    <h6>{% trans "Meeting" %}</h6>
                                    <small class="text-muted">{% trans "Video or audio meeting" %}</small>
                                </div>
                            </div>
                            
                            <input type="hidden" name="mode" id="session-mode" value="meeting">
                        </div>
                        
                        <!-- Description -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-comment-alt"></i>
                                {% trans "Tell Us More" %}
                            </h5>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">{% trans "Describe what you'd like to discuss" %}</label>
                                <textarea name="description" id="description" class="form-control" rows="4" 
                                         placeholder="{% trans 'Please share what brings you here today. This helps us match you with the right psychologist and prepare for your session.' %}" required></textarea>
                                <div class="form-text">
                                    <i class="fas fa-lock me-1"></i>
                                    {% trans "This information is confidential and will only be shared with your assigned psychologist." %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Scheduling -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-calendar-alt"></i>
                                {% trans "Schedule Your Session" %}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="scheduled_date" class="form-label">{% trans "Preferred Date & Time" %}</label>
                                    <input type="datetime-local" name="scheduled_date" id="scheduled_date" class="form-control" required>
                                    <div class="form-text">{% trans "Select your preferred date and time" %}</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">{% trans "Duration" %}</label>
                                    <select name="duration" id="duration" class="form-select">
                                        <option value="30">30 {% trans "minutes" %} ({% trans "Free consultation" %})</option>
                                        <option value="50" selected>50 {% trans "minutes" %} ({% trans "Standard session" %})</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary btn-submit">
                            <i class="fas fa-paper-plane me-2"></i>
                            {% trans "Request Consultation" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pricing Info -->
            <div class="pricing-card" id="pricing-display">
                <h5 class="mb-3">
                    <i class="fas fa-tag me-2"></i>
                    {% trans "Session Pricing" %}
                </h5>
                <div class="price-display" id="price-amount">500 ETB</div>
                <p class="mb-0" id="price-description">{% trans "Professional 50-minute consultation" %}</p>
            </div>
            
            <!-- What to Expect -->
            <div class="info-sidebar">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "What to Expect" %}
                </h5>
                <ul class="feature-list">
                    <li>
                        <div class="feature-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        {% trans "Licensed professional psychologists" %}
                    </li>
                    <li>
                        <div class="feature-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        {% trans "Confidential and secure sessions" %}
                    </li>
                    <li>
                        <div class="feature-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        {% trans "Flexible scheduling options" %}
                    </li>
                    <li>
                        <div class="feature-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        {% trans "Culturally sensitive approach" %}
                    </li>
                </ul>
            </div>
            
            <!-- Payment Info -->
            <div class="info-sidebar">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    {% trans "Payment Information" %}
                </h5>
                <p class="text-muted mb-3">{% trans "Secure payment processing through Chapa" %}</p>
                <div class="d-flex align-items-center gap-2">
                    <img src="https://chapa.co/assets/images/chapa-logo.png" alt="Chapa" style="height: 24px;">
                    <small class="text-muted">{% trans "Powered by Chapa" %}</small>
                </div>
            </div>
            
            <!-- Help -->
            <div class="info-sidebar">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-question-circle me-2"></i>
                    {% trans "Need Help?" %}
                </h5>
                <div class="d-grid gap-2">
                    <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-comments me-2"></i>{% trans "Chat Assistant" %}
                    </a>
                    <a href="{% url 'contact' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
