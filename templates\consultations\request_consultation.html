{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Request Consultation" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .consultation-hero {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }

    .consultation-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .consultation-header {
        background: var(--gray-50);
        padding: 2rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .consultation-form {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        border-left: 4px solid var(--primary-blue);
    }

    .form-section h5 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .btn-submit {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        border: none;
        padding: 1rem 2rem;
        font-weight: 600;
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .psychologist-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid var(--success);
    }

    .psychologist-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .info-card {
        background: var(--info);
        color: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 2rem;
    }

    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        align-items: center;
        margin: 0 1rem;
    }

    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: var(--gray-300);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 0.5rem;
    }

    .step.active .step-number {
        background: var(--primary-blue);
        color: var(--white);
    }

    .datetime-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    @media (max-width: 768px) {
        .datetime-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="consultation-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-calendar-plus me-3"></i>
                    {% trans "Request Consultation" %}
                </h1>
                <p class="lead mb-0">{% trans "Book a session with our professional psychologists for personalized mental health support" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active">
            <div class="step-number">1</div>
            <span>{% trans "Details" %}</span>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <span>{% trans "Payment" %}</span>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <span>{% trans "Confirmation" %}</span>
        </div>
    </div>

    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="consultation-card">
                <div class="consultation-header">
                    <h3 class="text-primary mb-3">
                        <i class="fas fa-clipboard-list me-2"></i>
                        {% trans "Consultation Request Form" %}
                    </h3>
                    <p class="text-muted mb-0">{% trans "Please fill out the form below to request your consultation. All information is kept confidential." %}</p>
                </div>

                <div class="consultation-form">
                    <form method="POST" class="needs-validation" novalidate id="consultationForm">
                        {% csrf_token %}

                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-info-circle"></i>
                                {% trans "Basic Information" %}
                            </h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="id_type" class="form-label">{% trans "Consultation Type" %} *</label>
                                    {{ form.type }}
                                    <div class="form-text">{% trans "Choose the type of consultation you need" %}</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="id_mode" class="form-label">{% trans "Session Mode" %} *</label>
                                    {{ form.mode }}
                                    <div class="form-text">{% trans "Select your preferred session format" %}</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="id_topic" class="form-label">{% trans "Main Topic/Concern" %} *</label>
                                {{ form.topic }}
                                <div class="form-text">{% trans "What would you like to focus on during the session?" %}</div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-comment-alt"></i>
                                {% trans "Tell Us More" %}
                            </h5>

                            <div class="mb-3">
                                <label for="id_description" class="form-label">{% trans "Detailed Description" %}</label>
                                {{ form.description }}
                                <div class="form-text">
                                    {% trans "Please describe your concerns, symptoms, or what you'd like to work on. This helps the psychologist prepare for your session." %}
                                    <br><small class="text-muted">{% trans "This information is confidential and will only be shared with your assigned psychologist." %}</small>
                                </div>
                            </div>
                        </div>

                        <!-- Scheduling -->
                        <div class="form-section">
                            <h5>
                                <i class="fas fa-calendar-alt"></i>
                                {% trans "Schedule Your Session" %}
                            </h5>

                            <div class="datetime-grid">
                                <div class="mb-3">
                                    <label for="id_scheduled_date" class="form-label">{% trans "Preferred Date & Time" %} *</label>
                                    {{ form.scheduled_date }}
                                    <div class="form-text">{% trans "Select your preferred date and time" %}</div>
                                </div>

                                <div class="mb-3">
                                    <label for="id_duration" class="form-label">{% trans "Session Duration" %}</label>
                                    {{ form.duration }}
                                    <div class="form-text">{% trans "Standard session is 50 minutes" %}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-submit">
                                <i class="fas fa-paper-plane me-2"></i>
                                {% trans "Submit Consultation Request" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Selected Psychologist (if any) -->
            {% if selected_psychologist %}
            <div class="psychologist-card">
                <h5 class="text-success mb-3">
                    <i class="fas fa-user-md me-2"></i>
                    {% trans "Selected Psychologist" %}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="psychologist-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Dr. {{ selected_psychologist.get_full_name|default:selected_psychologist.username }}</h6>
                        <p class="text-muted mb-1">{% trans "Licensed Psychologist" %}</p>
                        <small class="text-muted">{{ selected_psychologist.specializations.all.0.name|default:"General Psychology" }}</small>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'psychologist_detail' selected_psychologist.id %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-eye me-1"></i>{% trans "View Profile" %}
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Information Card -->
            <div class="info-card">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "What to Expect" %}
                </h5>
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check me-2"></i>
                        {% trans "Professional, confidential consultation" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check me-2"></i>
                        {% trans "Licensed and experienced psychologists" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check me-2"></i>
                        {% trans "Flexible scheduling options" %}
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check me-2"></i>
                        {% trans "Secure video/audio sessions" %}
                    </li>
                </ul>
            </div>

            <!-- Pricing Info -->
            <div class="consultation-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    {% trans "Session Information" %}
                </h5>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{% trans "Standard Session:" %}</span>
                        <strong>500 ETB</strong>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{% trans "Duration:" %}</span>
                        <span>50 {% trans "minutes" %}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{% trans "Format:" %}</span>
                        <span>{% trans "Video/Audio Call" %}</span>
                    </div>
                </div>
                <div class="alert alert-info mb-0">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "Payment is processed securely after your request is confirmed by the psychologist." %}
                    </small>
                </div>
            </div>

            <!-- Help Section -->
            <div class="consultation-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-question-circle me-2"></i>
                    {% trans "Need Help?" %}
                </h5>
                <p class="text-muted mb-3">{% trans "Have questions about booking a consultation?" %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-robot me-2"></i>{% trans "Chat with AI Assistant" %}
                    </a>
                    <a href="{% url 'contact' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize flatpickr for date/time selection
    flatpickr("#id_scheduled_date", {
        enableTime: true,
        dateFormat: "Y-m-d H:i",
        minDate: "today",
        maxDate: new Date().fp_incr(30), // 30 days from today
        time_24hr: true,
        minuteIncrement: 15,
        disable: [
            // Disable Sundays (0 = Sunday)
            function(date) {
                return date.getDay() === 0;
            }
        ],
        locale: {
            firstDayOfWeek: 1 // Monday
        },
        onReady: function(selectedDates, dateStr, instance) {
            // Add custom styling
            instance.calendarContainer.classList.add('consultation-calendar');
        }
    });

    // Form validation
    const form = document.getElementById('consultationForm');
    const submitBtn = form.querySelector('.btn-submit');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Basic validation
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            alert('{% trans "Please fill in all required fields." %}');
            return;
        }

        // Check if date is selected
        const dateField = document.getElementById('id_scheduled_date');
        if (!dateField.value) {
            alert('{% trans "Please select a preferred date and time." %}');
            dateField.focus();
            return;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Submitting..." %}';
        submitBtn.disabled = true;

        // Submit form
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Real-time validation feedback
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });

    // Character counter for description
    const descriptionField = document.getElementById('id_description');
    if (descriptionField) {
        const maxLength = 1000;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.innerHTML = `<span id="charCount">0</span>/${maxLength} {% trans "characters" %}`;
        descriptionField.parentNode.appendChild(counter);

        descriptionField.addEventListener('input', function() {
            const count = this.value.length;
            document.getElementById('charCount').textContent = count;

            if (count > maxLength * 0.9) {
                counter.classList.add('text-warning');
            } else {
                counter.classList.remove('text-warning');
            }

            if (count > maxLength) {
                counter.classList.add('text-danger');
                this.value = this.value.substring(0, maxLength);
                document.getElementById('charCount').textContent = maxLength;
            } else {
                counter.classList.remove('text-danger');
            }
        });
    }

    // Auto-save form data to localStorage
    const formData = {};
    inputs.forEach(input => {
        const savedValue = localStorage.getItem(`consultation_${input.name}`);
        if (savedValue && input.type !== 'hidden') {
            input.value = savedValue;
        }

        input.addEventListener('change', function() {
            localStorage.setItem(`consultation_${this.name}`, this.value);
        });
    });

    // Clear saved data on successful submission
    form.addEventListener('submit', function() {
        inputs.forEach(input => {
            localStorage.removeItem(`consultation_${input.name}`);
        });
    });
});

// Add custom CSS for flatpickr
const style = document.createElement('style');
style.textContent = `
    .consultation-calendar {
        font-family: var(--font-family);
    }

    .flatpickr-calendar {
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-xl);
    }

    .flatpickr-day.selected {
        background: var(--primary-blue);
        border-color: var(--primary-blue);
    }

    .flatpickr-day:hover {
        background: rgba(37, 99, 235, 0.1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}