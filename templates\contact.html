{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Contact Us" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .contact-hero {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 4rem 0;
        margin-bottom: 3rem;
    }
    
    .contact-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    .contact-info-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        margin-bottom: 1rem;
        border-left: 4px solid var(--primary-blue);
    }
    
    .contact-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        margin-right: 1rem;
        font-size: 1.2rem;
    }
    
    .form-control:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .btn-contact {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        border: none;
        padding: 0.875rem 2rem;
        font-weight: 600;
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;
    }
    
    .btn-contact:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .emergency-alert {
        background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
        color: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .faq-section {
        background: var(--gray-50);
        padding: 2rem;
        border-radius: var(--radius-lg);
        margin-top: 2rem;
    }
    
    .faq-item {
        background: var(--white);
        border-radius: var(--radius-md);
        margin-bottom: 1rem;
        border: 1px solid var(--gray-200);
    }
    
    .faq-question {
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid var(--gray-200);
        font-weight: 600;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .faq-answer {
        padding: 1rem;
        display: none;
        color: var(--gray-700);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="contact-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-envelope me-3"></i>
                    {% trans "Contact Us" %}
                </h1>
                <p class="lead mb-0">{% trans "We're here to help you with any questions or concerns about our mental health services" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Emergency Alert -->
    <div class="emergency-alert">
        <h4 class="mb-2">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% trans "Mental Health Emergency?" %}
        </h4>
        <p class="mb-2">{% trans "If you're experiencing a mental health crisis or having thoughts of self-harm, please seek immediate help:" %}</p>
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <span><strong>{% trans "Emergency:" %}</strong> 991</span>
            <span><strong>{% trans "Suicide Prevention:" %}</strong> 8888</span>
            <span><strong>{% trans "Crisis Text:" %}</strong> 741741</span>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="contact-card">
                <h3 class="mb-4 text-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    {% trans "Send us a Message" %}
                </h3>
                
                <form method="post" id="contactForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">{% trans "Full Name" %} *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">{% trans "Email Address" %} *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">{% trans "Phone Number" %}</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">{% trans "Subject" %} *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">{% trans "Select a topic" %}</option>
                                <option value="general">{% trans "General Inquiry" %}</option>
                                <option value="technical">{% trans "Technical Support" %}</option>
                                <option value="billing">{% trans "Billing Question" %}</option>
                                <option value="psychologist">{% trans "Psychologist Application" %}</option>
                                <option value="complaint">{% trans "Complaint" %}</option>
                                <option value="feedback">{% trans "Feedback" %}</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">{% trans "Message" %} *</label>
                        <textarea class="form-control" id="message" name="message" rows="6" required 
                                 placeholder="{% trans 'Please describe your question or concern in detail...' %}"></textarea>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="privacy" name="privacy" required>
                        <label class="form-check-label" for="privacy">
                            {% trans "I agree to the privacy policy and terms of service" %} *
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-contact">
                        <i class="fas fa-paper-plane me-2"></i>
                        {% trans "Send Message" %}
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="col-lg-4">
            <div class="contact-card">
                <h3 class="mb-4 text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Contact Information" %}
                </h3>
                
                <div class="contact-info-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Address" %}</h6>
                        <p class="mb-0 text-muted">Addis Ababa, Ethiopia<br>Bole Sub-City, Woreda 03</p>
                    </div>
                </div>
                
                <div class="contact-info-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Phone" %}</h6>
                        <p class="mb-0 text-muted">+251 11 123 4567<br>+251 91 234 5678</p>
                    </div>
                </div>
                
                <div class="contact-info-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Email" %}</h6>
                        <p class="mb-0 text-muted"><EMAIL><br><EMAIL></p>
                    </div>
                </div>
                
                <div class="contact-info-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Business Hours" %}</h6>
                        <p class="mb-0 text-muted">{% trans "Mon-Fri: 8:00 AM - 6:00 PM" %}<br>{% trans "Sat: 9:00 AM - 2:00 PM" %}</p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="contact-card">
                <h5 class="mb-3 text-primary">{% trans "Quick Links" %}</h5>
                <div class="d-grid gap-2">
                    <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-primary">
                        <i class="fas fa-robot me-2"></i>{% trans "Chat with AI Assistant" %}
                    </a>
                    <a href="{% url 'psychologist_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user-md me-2"></i>{% trans "Find a Psychologist" %}
                    </a>
                    <a href="{% url 'register' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Create Account" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="faq-section">
        <h3 class="mb-4 text-center text-primary">
            <i class="fas fa-question-circle me-2"></i>
            {% trans "Frequently Asked Questions" %}
        </h3>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(1)">
                        {% trans "How do I book a consultation?" %}
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq1">
                        {% trans "Register for an account, browse our verified psychologists, select one that fits your needs, and choose an available time slot." %}
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(2)">
                        {% trans "Are consultations confidential?" %}
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq2">
                        {% trans "Yes, all consultations are strictly confidential and follow professional ethics standards." %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(3)">
                        {% trans "What payment methods do you accept?" %}
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq3">
                        {% trans "We accept bank transfers, mobile money (Telebirr, M-Birr), and online payments through our secure gateway." %}
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(4)">
                        {% trans "How do I become a psychologist on this platform?" %}
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer" id="faq4">
                        {% trans "Register as a psychologist, provide your credentials and license information, and wait for admin approval." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleFaq(id) {
    const answer = document.getElementById('faq' + id);
    const icon = answer.previousElementSibling.querySelector('i');
    
    if (answer.style.display === 'block') {
        answer.style.display = 'none';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    } else {
        answer.style.display = 'block';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    }
}

document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Simple form validation
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const subject = document.getElementById('subject').value;
    const message = document.getElementById('message').value.trim();
    const privacy = document.getElementById('privacy').checked;
    
    if (!name || !email || !subject || !message || !privacy) {
        alert('{% trans "Please fill in all required fields." %}');
        return;
    }
    
    // Simulate form submission
    alert('{% trans "Thank you for your message! We will get back to you within 24 hours." %}');
    this.reset();
});
</script>
{% endblock %}
