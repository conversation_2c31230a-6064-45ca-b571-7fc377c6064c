from django.shortcuts import render, redirect
from django.utils import translation
from django.conf import settings
from django.http import HttpResponseRedirect

# For Django 5.x compatibility
try:
    from django.utils.translation import LANGUAGE_SESSION_KEY
except ImportError:
    LANGUAGE_SESSION_KEY = 'django_language'


def landing_page(request):
    """Landing page view"""
    return render(request, 'landing.html')


def about_page(request):
    """About page view"""
    return render(request, 'about.html')


def contact_page(request):
    """Contact page view"""
    return render(request, 'contact.html')


def set_language(request):
    """Set user language preference"""
    from django.conf import settings
    from django.utils import translation
    from django.http import HttpResponseRedirect

    language = request.POST.get('language')
    if language and language in dict(settings.LANGUAGES).keys():
        translation.activate(language)
        request.session['django_language'] = language
        # Also set the language cookie for persistence
        response = HttpResponseRedirect(request.POST.get('next', request.META.get('HTTP_REFERER', '/')))
        response.set_cookie(settings.LANGUAGE_COOKIE_NAME, language, max_age=settings.LANGUAGE_COOKIE_AGE)
        return response

    # Redirect back to the previous page
    next_url = request.POST.get('next', request.META.get('HTTP_REFERER', '/'))
    return HttpResponseRedirect(next_url)


def admin_dashboard(request):
    """Custom admin dashboard"""
    from django.contrib.admin.views.decorators import staff_member_required
    from django.shortcuts import render
    from users.models import CustomUser
    from consultations.models import Consultation
    from messaging.models import ChatbotInteraction
    from django.db.models import Count, Q
    from django.utils import timezone
    from datetime import timedelta

    if not request.user.is_staff:
        return redirect('landing')

    # Get statistics
    total_users = CustomUser.objects.count()
    total_psychologists = CustomUser.objects.filter(role='psychologist').count()
    total_consultations = Consultation.objects.count()
    pending_consultations = Consultation.objects.filter(status='pending').count()

    # Recent activity
    recent_users = CustomUser.objects.order_by('-date_joined')[:5]
    recent_consultations = Consultation.objects.order_by('-created_at')[:5]

    # Weekly stats
    week_ago = timezone.now() - timedelta(days=7)
    new_users_this_week = CustomUser.objects.filter(date_joined__gte=week_ago).count()
    new_consultations_this_week = Consultation.objects.filter(created_at__gte=week_ago).count()

    context = {
        'total_users': total_users,
        'total_psychologists': total_psychologists,
        'total_consultations': total_consultations,
        'pending_consultations': pending_consultations,
        'recent_users': recent_users,
        'recent_consultations': recent_consultations,
        'new_users_this_week': new_users_this_week,
        'new_consultations_this_week': new_consultations_this_week,
    }

    return render(request, 'admin/custom_dashboard.html', context)