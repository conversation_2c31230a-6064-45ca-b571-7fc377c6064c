from django.shortcuts import render, redirect
from django.utils import translation
from django.conf import settings
from django.http import HttpResponseRedirect

# For Django 5.x compatibility
try:
    from django.utils.translation import LANGUAGE_SESSION_KEY
except ImportError:
    LANGUAGE_SESSION_KEY = 'django_language'


def landing_page(request):
    """Landing page view"""
    return render(request, 'landing.html')


def about_page(request):
    """About page view"""
    return render(request, 'about.html')


def contact_page(request):
    """Contact page view"""
    return render(request, 'contact.html')


def set_language(request):
    """Set user language preference"""
    language = request.POST.get('language')
    if language and language in dict(settings.LANGUAGES).keys():
        translation.activate(language)
        request.session[LANGUAGE_SESSION_KEY] = language

    # Redirect back to the previous page
    next_url = request.POST.get('next', request.META.get('HTTP_REFERER', '/'))
    return HttpResponseRedirect(next_url)