{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Payment History" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .payment-hero {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .payment-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: 1.5rem;
        overflow: hidden;
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }
    
    .payment-card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }
    
    .payment-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .payment-info {
        flex: 1;
    }
    
    .payment-status {
        flex-shrink: 0;
    }
    
    .payment-body {
        padding: 1.5rem;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: var(--radius-full);
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .status-completed { background: rgba(16, 185, 129, 0.1); color: var(--success); }
    .status-pending { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
    .status-failed { background: rgba(239, 68, 68, 0.1); color: var(--danger); }
    .status-refunded { background: rgba(107, 114, 128, 0.1); color: var(--gray-600); }
    
    .payment-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-blue);
    }
    
    .payment-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--gray-600);
        font-size: 0.875rem;
    }
    
    .filter-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        text-align: center;
        border-left: 4px solid var(--primary-blue);
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .payment-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-payment {
        padding: 0.5rem 1rem;
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="payment-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-credit-card me-3"></i>
                    {% trans "Payment History" %}
                </h1>
                <p class="lead mb-0">{% trans "View and manage your consultation payments and transaction history" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_payments }}</div>
            <div class="text-muted">{% trans "Total Payments" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_amount|floatformat:0 }} ETB</div>
            <div class="text-muted">{% trans "Total Spent" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ completed_payments }}</div>
            <div class="text-muted">{% trans "Completed" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ pending_payments }}</div>
            <div class="text-muted">{% trans "Pending" %}</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="status" class="form-label">{% trans "Status" %}</label>
                <select name="status" class="form-select" id="status">
                    <option value="">{% trans "All Statuses" %}</option>
                    <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                    <option value="failed" {% if request.GET.status == 'failed' %}selected{% endif %}>{% trans "Failed" %}</option>
                    <option value="refunded" {% if request.GET.status == 'refunded' %}selected{% endif %}>{% trans "Refunded" %}</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                <input type="date" name="date_from" class="form-control" id="date_from" value="{{ request.GET.date_from }}">
            </div>
            
            <div class="col-md-3">
                <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                <input type="date" name="date_to" class="form-control" id="date_to" value="{{ request.GET.date_to }}">
            </div>
            
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i>{% trans "Filter" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Payment List -->
    {% if payments %}
    <div class="payments-list">
        {% for payment in payments %}
        <div class="payment-card">
            <div class="payment-header">
                <div class="payment-info">
                    <h5 class="mb-1">{% trans "Consultation Payment" %}</h5>
                    <p class="text-muted mb-0">
                        {% trans "Payment ID:" %} {{ payment.payment_id }}
                    </p>
                </div>
                <div class="payment-status">
                    <div class="payment-amount mb-2">{{ payment.amount }} ETB</div>
                    <span class="status-badge status-{{ payment.status }}">
                        {% if payment.status == 'completed' %}
                            <i class="fas fa-check-circle me-1"></i>{% trans "Completed" %}
                        {% elif payment.status == 'pending' %}
                            <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                        {% elif payment.status == 'failed' %}
                            <i class="fas fa-times-circle me-1"></i>{% trans "Failed" %}
                        {% elif payment.status == 'refunded' %}
                            <i class="fas fa-undo me-1"></i>{% trans "Refunded" %}
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div class="payment-body">
                <div class="payment-meta">
                    <div class="meta-item">
                        <i class="fas fa-user-md"></i>
                        <span>Dr. {{ payment.consultation.psychologist.get_full_name|default:payment.consultation.psychologist.username }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>{{ payment.consultation.date|date:"M d, Y" }} at {{ payment.consultation.time|time:"H:i" }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-credit-card"></i>
                        <span>{{ payment.get_payment_method_display|default:"Online Payment" }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ payment.created_at|date:"M d, Y H:i" }}</span>
                    </div>
                </div>
                
                {% if payment.consultation.topic %}
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-tag me-1"></i>
                        <strong>{% trans "Topic:" %}</strong> {{ payment.consultation.topic.name }}
                    </small>
                </div>
                {% endif %}
                
                <div class="payment-actions">
                    <a href="{% url 'payment_receipt' payment.payment_id %}" class="btn btn-outline-primary btn-payment">
                        <i class="fas fa-receipt me-1"></i>{% trans "Receipt" %}
                    </a>
                    
                    {% if payment.status == 'completed' and payment.consultation.status == 'pending' %}
                    <a href="{% url 'request_refund' payment.payment_id %}" class="btn btn-outline-warning btn-payment">
                        <i class="fas fa-undo me-1"></i>{% trans "Request Refund" %}
                    </a>
                    {% endif %}
                    
                    {% if payment.status == 'failed' %}
                    <a href="{% url 'initiate_payment' payment.consultation.id %}" class="btn btn-success btn-payment">
                        <i class="fas fa-redo me-1"></i>{% trans "Retry Payment" %}
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'consultation_detail' payment.consultation.id %}" class="btn btn-outline-info btn-payment">
                        <i class="fas fa-eye me-1"></i>{% trans "View Consultation" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if payments.has_other_pages %}
    <nav aria-label="Payment history pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if payments.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ payments.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">
                    <i class="fas fa-angle-left"></i>
                </a>
            </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ payments.number }} of {{ payments.paginator.num_pages }}</span>
            </li>
            
            {% if payments.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ payments.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ payments.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">{% trans "No Payment History" %}</h5>
        <p class="text-muted">{% trans "You haven't made any payments yet. Book a consultation to get started!" %}</p>
        <a href="{% url 'psychologist_list' %}" class="btn btn-primary">
            <i class="fas fa-user-md me-2"></i>{% trans "Find a Psychologist" %}
        </a>
    </div>
    {% endif %}
    
    <!-- Help Section -->
    <div class="text-center mt-5 p-4 bg-light rounded">
        <h4 class="mb-3">{% trans "Need Help with Payments?" %}</h4>
        <p class="mb-3">{% trans "If you have questions about your payments or need assistance, we're here to help." %}</p>
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="{% url 'contact' %}" class="btn btn-outline-primary">
                <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
            </a>
            <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-success">
                <i class="fas fa-robot me-2"></i>{% trans "Chat with AI" %}
            </a>
        </div>
    </div>
</div>
{% endblock %}
