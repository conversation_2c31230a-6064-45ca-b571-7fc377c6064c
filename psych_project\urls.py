"""
URL configuration for psych_project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from . import views 


urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.landing_page, name='landing'),
    path('about/', views.about_page, name='about'),
    path('contact/', views.contact_page, name='contact'),

    # App URLs
    path('users/', include('users.urls')),
    path('discussions/', include('discussions.urls')),
    path('consultations/', include('consultations.urls')),
    path('messaging/', include('messaging.urls')),
    path('resources/', include('resources.urls')),
    path('payments/', include('payments.urls')),

    # Auth shortcuts (for convenience)
    path('register/', include('users.urls')),
    path('login/', include('users.urls')),
    path('logout/', include('users.urls')),
]
