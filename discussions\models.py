from django.db import models
from users.models import CustomUser

class Category(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True)
    is_active = models.BooleanField(default=True)
    allow_guest_access = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class Post(models.Model):
    VISIBILITY_CHOICES = [
        ('public', 'Public'),
        ('private', 'Private'),
        ('members_only', 'Members Only'),
    ]

    author = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True)
    title = models.CharField(max_length=200)
    content = models.TextField()
    visibility = models.Char<PERSON>ield(max_length=20, choices=VISIBILITY_CHOICES, default='public')

    # Content moderation
    is_approved = models.BooleanField(default=True)
    is_flagged = models.BooleanField(default=False)
    flagged_reason = models.TextField(blank=True)
    moderated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL,
                                   null=True, blank=True, related_name='moderated_posts')

    # Engagement metrics
    view_count = models.PositiveIntegerField(default=0)
    like_count = models.PositiveIntegerField(default=0)

    # Tags for better organization
    tags = models.CharField(max_length=500, blank=True,
                          help_text="Comma-separated tags")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def can_be_viewed_by(self, user):
        """Check if user can view this post"""
        if not self.is_approved:
            return user == self.author or (user.is_authenticated and user.can_moderate_content())

        if self.visibility == 'public':
            return True
        elif self.visibility == 'members_only':
            return user.is_authenticated
        elif self.visibility == 'private':
            return user == self.author

        return False

class Reply(models.Model):
    REPLY_TYPE_CHOICES = [
        ('public', 'Public Reply'),
        ('private', 'Private Reply'),
    ]

    post = models.ForeignKey(Post, related_name='replies', on_delete=models.CASCADE)
    author = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    content = models.TextField()
    reply_type = models.CharField(max_length=10, choices=REPLY_TYPE_CHOICES, default='public')

    # For private replies - only visible to post author and reply author
    is_private = models.BooleanField(default=False)

    # Content moderation
    is_approved = models.BooleanField(default=True)
    is_flagged = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']

    def can_be_viewed_by(self, user):
        """Check if user can view this reply"""
        if not self.is_approved:
            return user == self.author or (user.is_authenticated and user.can_moderate_content())

        if self.is_private:
            return user == self.author or user == self.post.author

        return True


class PostLike(models.Model):
    """Track post likes"""
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['post', 'user']


class PostReport(models.Model):
    """Reports for inappropriate posts"""
    REPORT_REASONS = [
        ('spam', 'Spam'),
        ('inappropriate', 'Inappropriate Content'),
        ('harassment', 'Harassment'),
        ('misinformation', 'Misinformation'),
        ('other', 'Other'),
    ]

    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='reports')
    reporter = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    reason = models.CharField(max_length=20, choices=REPORT_REASONS)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL,
                                  null=True, blank=True, related_name='resolved_post_reports')

    class Meta:
        unique_together = ['post', 'reporter']

    def __str__(self):
        return f"Report on '{self.post.title}' by {self.reporter.username}"

