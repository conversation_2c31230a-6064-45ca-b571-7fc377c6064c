{% extends 'base.html' %}

{% block title %}Psychologist Registration{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Register as a Psychologist</h3>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Join our platform as a licensed psychologist. Your credentials will be reviewed by our admin team before approval.
                    </p>
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.first_name.id_for_label }}">First Name</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger">{{ form.first_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger">{{ form.last_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.email.id_for_label }}">Email</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger">{{ form.email.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.date_of_birth.id_for_label }}">Date of Birth</label>
                            {{ form.date_of_birth }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger">{{ form.date_of_birth.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.qualifications.id_for_label }}">Qualifications</label>
                            {{ form.qualifications }}
                            {% if form.qualifications.errors %}
                                <div class="text-danger">{{ form.qualifications.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.experience_years.id_for_label }}">Years of Experience</label>
                            {{ form.experience_years }}
                            {% if form.experience_years.errors %}
                                <div class="text-danger">{{ form.experience_years.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="{{ form.psychologist_documents.id_for_label }}">Professional Documents</label>
                            {{ form.psychologist_documents }}
                            {% if form.psychologist_documents.errors %}
                                <div class="text-danger">{{ form.psychologist_documents.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.password1.id_for_label }}">Password</label>
                                    {{ form.password1 }}
                                    {% if form.password1.errors %}
                                        <div class="text-danger">{{ form.password1.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.password2.id_for_label }}">Confirm Password</label>
                                    {{ form.password2 }}
                                    {% if form.password2.errors %}
                                        <div class="text-danger">{{ form.password2.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Register as Psychologist</button>
                        <a href="{% url 'register' %}" class="btn btn-link">Register as Regular User</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
