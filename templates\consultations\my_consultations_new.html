{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Consultations" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .consultations-hero {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .consultations-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .consultation-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: 1px solid rgba(37, 99, 235, 0.1);
        transition: all 0.3s ease;
    }
    
    .consultation-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 32px rgba(0,0,0,0.15);
    }
    
    .consultation-header {
        padding: 1.5rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .consultation-info {
        flex: 1;
    }
    
    .consultation-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending { 
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); 
        color: var(--white); 
    }
    .status-approved { 
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); 
        color: var(--white); 
    }
    .status-completed { 
        background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
        color: var(--white); 
    }
    .status-rejected { 
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); 
        color: var(--white); 
    }
    
    .consultation-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 12px;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #64748b;
        font-size: 0.9rem;
    }
    
    .meta-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #2563eb;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 0.75rem;
    }
    
    .filter-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
        background: var(--white);
        padding: 0.5rem;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .filter-tab {
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        background: transparent;
        border: none;
        color: #64748b;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        flex: 1;
        text-align: center;
    }
    
    .filter-tab.active {
        background: #2563eb;
        color: var(--white);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #2563eb;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2563eb;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #64748b;
        font-weight: 500;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    }
    
    .empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: #64748b;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-action:hover {
        transform: translateY(-1px);
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: var(--white);
    }
    
    .btn-success-action {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
    }
    
    .btn-warning-action {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        color: var(--white);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="consultations-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-between align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-calendar-alt me-3"></i>
                        {% trans "My Consultations" %}
                    </h1>
                    <p class="lead mb-0">{% trans "Manage your mental health consultations and track your progress" %}</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="{% url 'request_consultation' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>{% trans "Book New Session" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ consultations.count }}</div>
            <div class="stat-label">{% trans "Total Sessions" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ completed_count|default:0 }}</div>
            <div class="stat-label">{% trans "Completed" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ upcoming_count|default:0 }}</div>
            <div class="stat-label">{% trans "Upcoming" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ hours_total|default:0 }}</div>
            <div class="stat-label">{% trans "Hours of Support" %}</div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <button class="filter-tab active" onclick="filterConsultations('all')">
            {% trans "All Sessions" %}
        </button>
        <button class="filter-tab" onclick="filterConsultations('upcoming')">
            {% trans "Upcoming" %}
        </button>
        <button class="filter-tab" onclick="filterConsultations('completed')">
            {% trans "Completed" %}
        </button>
        <button class="filter-tab" onclick="filterConsultations('cancelled')">
            {% trans "Cancelled" %}
        </button>
    </div>

    <!-- Consultations List -->
    {% if consultations %}
        <div id="consultations-list">
            {% for consultation in consultations %}
            <div class="consultation-card" data-status="{{ consultation.status }}">
                <div class="consultation-header">
                    <div class="consultation-info">
                        <h5 class="mb-2 text-primary">{{ consultation.topic }}</h5>
                        <div class="d-flex align-items-center gap-3 mb-2">
                            <span class="status-badge status-{{ consultation.status }}">
                                {{ consultation.get_status_display }}
                            </span>
                            <span class="badge bg-secondary">{{ consultation.get_type_display }}</span>
                        </div>
                        
                        {% if consultation.psychologist %}
                        <p class="text-muted mb-0">
                            <i class="fas fa-user-md me-1"></i>
                            Dr. {{ consultation.psychologist.get_full_name|default:consultation.psychologist.username }}
                        </p>
                        {% endif %}
                    </div>
                    
                    <div class="consultation-actions">
                        <a href="{% url 'consultation_detail' consultation.id %}" class="btn btn-action btn-primary-action">
                            <i class="fas fa-eye me-1"></i>{% trans "View" %}
                        </a>
                        
                        {% if consultation.status == 'completed' %}
                        <a href="{% url 'rate_consultation' consultation.id %}" class="btn btn-action btn-success-action">
                            <i class="fas fa-star me-1"></i>{% trans "Rate" %}
                        </a>
                        {% endif %}
                        
                        {% if consultation.status == 'pending' %}
                        <button class="btn btn-action btn-warning-action" onclick="cancelConsultation({{ consultation.id }})">
                            <i class="fas fa-times me-1"></i>{% trans "Cancel" %}
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                <div class="consultation-meta">
                    {% if consultation.scheduled_date %}
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <span>{{ consultation.scheduled_date|date:"M d, Y" }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <span>{{ consultation.duration|default:"50" }} {% trans "minutes" %}</span>
                    </div>
                    
                    <div class="meta-item">
                        <div class="meta-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <span>{% trans "Requested" %} {{ consultation.created_at|date:"M d, Y" }}</span>
                    </div>
                    
                    {% if consultation.type == 'free' %}
                    <div class="meta-item">
                        <div class="meta-icon" style="background: #10b981;">
                            <i class="fas fa-gift"></i>
                        </div>
                        <span>{% trans "Free Session" %}</span>
                    </div>
                    {% else %}
                    <div class="meta-item">
                        <div class="meta-icon" style="background: #2563eb;">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <span>500 ETB</span>
                    </div>
                    {% endif %}
                </div>
                
                {% if consultation.description %}
                <div class="p-3 mt-2 bg-light rounded">
                    <small class="text-muted">
                        <strong>{% trans "Description:" %}</strong> {{ consultation.description|truncatewords:20 }}
                    </small>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <h4 class="mb-3">{% trans "No Consultations Yet" %}</h4>
            <p class="text-muted mb-4">{% trans "You haven't booked any consultations yet. Start your mental health journey today!" %}</p>
            <a href="{% url 'request_consultation' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>{% trans "Book Your First Session" %}
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterConsultations(status) {
    // Update active tab
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Filter consultations
    const consultations = document.querySelectorAll('.consultation-card');
    consultations.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function cancelConsultation(consultationId) {
    if (confirm('{% trans "Are you sure you want to cancel this consultation?" %}')) {
        // Implementation would go here
        alert('{% trans "Consultation cancellation functionality would be implemented here" %}');
    }
}
</script>
{% endblock %}
