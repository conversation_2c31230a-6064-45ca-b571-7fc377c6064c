from django.shortcuts import render

# Create your views here.
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import Post, Reply, Category
from .forms import PostForm, ReplyForm

def discussion_list(request):
    posts = Post.objects.all().order_by('-created_at')
    categories = Category.objects.all()

    query = request.GET.get('q')
    category_id = request.GET.get('category')

    if query:
        posts = posts.filter(title__icontains=query)

    if category_id:
        posts = posts.filter(category__id=category_id)

    context = {
        'posts': posts,
        'categories': categories,
        'selected_category': category_id,
        'query': query
    }
    return render(request, 'discussions/discussion_list.html', context)


def post_detail(request, post_id):
    post = get_object_or_404(Post, id=post_id)
    replies = post.replies.all()
    if request.method == 'POST':
        form = ReplyForm(request.POST)
        if form.is_valid():
            reply = form.save(commit=False)
            reply.post = post
            reply.author = request.user
            reply.save()
            return redirect('post_detail', post_id=post.id)
    else:
        form = ReplyForm()
    return render(request, 'discussions/post_detail.html', {'post': post, 'replies': replies, 'form': form})

@login_required
def create_post(request):
    if request.method == 'POST':
        form = PostForm(request.POST)
        if form.is_valid():
            post = form.save(commit=False)
            post.author = request.user
            post.save()
            return redirect('discussion_list')
    else:
        form = PostForm()
    return render(request, 'discussions/create_post.html', {'form': form})

