{% extends 'base.html' %}

{% block title %}Book Consultation - {{ psychologist.get_full_name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5>{{ psychologist.get_full_name }}</h5>
                    <p class="text-muted">{{ psychologist.specialties.all|join:", " }}</p>
                    <p><strong>Experience:</strong> {{ psychologist.experience_years }} years</p>
                    <p><strong>Rating:</strong> 
                        {% for i in "12345" %}
                            {% if forloop.counter <= psychologist.rating %}
                                <i class="fas fa-star text-warning"></i>
                            {% else %}
                                <i class="far fa-star text-warning"></i>
                            {% endif %}
                        {% endfor %}
                        ({{ psychologist.rating }}/5)
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Select Date & Time</h4>
                </div>
                <div class="card-body">
                    <form method="post" id="booking-form">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="consultation-type" class="form-label">Consultation Type</label>
                                <select class="form-select" id="consultation-type" name="type" required>
                                    <option value="">Select type...</option>
                                    <option value="free">Free Consultation</option>
                                    <option value="paid">Paid Consultation</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="topic" class="form-label">Topic</label>
                                <input type="text" class="form-control" id="topic" name="topic" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Available Time Slots</label>
                            <div id="calendar-container">
                                {% if available_slots %}
                                    <div class="row">
                                        {% regroup available_slots by date as slots_by_date %}
                                        {% for date_group in slots_by_date %}
                                            <div class="col-md-6 mb-3">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <strong>{{ date_group.grouper|date:"F d, Y" }}</strong>
                                                    </div>
                                                    <div class="card-body">
                                                        {% for slot in date_group.list %}
                                                            <div class="form-check mb-2">
                                                                <input class="form-check-input" type="radio" 
                                                                       name="time_slot" value="{{ slot.id }}" 
                                                                       id="slot_{{ slot.id }}" required>
                                                                <label class="form-check-label" for="slot_{{ slot.id }}">
                                                                    {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                                                </label>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No available time slots found for the next 30 days. 
                                        Please contact the psychologist directly or try again later.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if available_slots %}
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-check me-2"></i>Book Consultation
                                </button>
                            </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('booking-form');
    const consultationType = document.getElementById('consultation-type');
    
    // Show pricing information when paid consultation is selected
    consultationType.addEventListener('change', function() {
        if (this.value === 'paid') {
            // You could add an AJAX call here to fetch pricing information
            console.log('Paid consultation selected');
        }
    });
    
    form.addEventListener('submit', function(e) {
        const selectedSlot = document.querySelector('input[name="time_slot"]:checked');
        if (!selectedSlot) {
            e.preventDefault();
            alert('Please select a time slot.');
            return false;
        }
        
        // Confirm booking
        const confirmMessage = `Are you sure you want to book this consultation?\n\nType: ${consultationType.value}\nTopic: ${document.getElementById('topic').value}`;
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    cursor: pointer;
}

.alert {
    border: none;
    border-radius: 10px;
}

#calendar-container .card {
    border: 1px solid #dee2e6;
}

#calendar-container .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}
</style>
{% endblock %}
