import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import PrivateMessage, Conversation

User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'
        
        # Check if user is authenticated
        if self.scope["user"].is_anonymous:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send user status
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_status',
                'user_id': self.scope["user"].id,
                'username': self.scope["user"].username,
                'status': 'online'
            }
        )

    async def disconnect(self, close_code):
        # Send user offline status
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'user_status',
                    'user_id': self.scope["user"].id,
                    'username': self.scope["user"].username,
                    'status': 'offline'
                }
            )
            
            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type', 'message')
        
        if message_type == 'message':
            message = text_data_json['message']
            recipient_id = text_data_json.get('recipient_id')
            
            # Save message to database
            saved_message = await self.save_message(
                self.scope["user"].id,
                recipient_id,
                message
            )
            
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'sender_id': self.scope["user"].id,
                    'sender_username': self.scope["user"].username,
                    'sender_name': self.scope["user"].get_full_name() or self.scope["user"].username,
                    'timestamp': saved_message.created_at.isoformat(),
                    'message_id': saved_message.id
                }
            )
            
        elif message_type == 'typing':
            # Handle typing indicator
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'typing_indicator',
                    'user_id': self.scope["user"].id,
                    'username': self.scope["user"].username,
                    'is_typing': text_data_json.get('is_typing', False)
                }
            )

    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': event['message'],
            'sender_id': event['sender_id'],
            'sender_username': event['sender_username'],
            'sender_name': event['sender_name'],
            'timestamp': event['timestamp'],
            'message_id': event['message_id']
        }))

    async def typing_indicator(self, event):
        # Don't send typing indicator to the sender
        if event['user_id'] != self.scope["user"].id:
            await self.send(text_data=json.dumps({
                'type': 'typing',
                'user_id': event['user_id'],
                'username': event['username'],
                'is_typing': event['is_typing']
            }))

    async def user_status(self, event):
        # Don't send status to the user themselves
        if event['user_id'] != self.scope["user"].id:
            await self.send(text_data=json.dumps({
                'type': 'user_status',
                'user_id': event['user_id'],
                'username': event['username'],
                'status': event['status']
            }))

    @database_sync_to_async
    def save_message(self, sender_id, recipient_id, message):
        sender = User.objects.get(id=sender_id)
        recipient = User.objects.get(id=recipient_id) if recipient_id else None
        
        # Get or create conversation
        if recipient:
            conversation, created = Conversation.objects.get_or_create(
                defaults={'created_by': sender},
                **{
                    f'participant_{1 if sender.id < recipient.id else 2}': sender,
                    f'participant_{2 if sender.id < recipient.id else 1}': recipient,
                }
            )
        else:
            # Group conversation logic would go here
            conversation = None
        
        # Create message
        private_message = PrivateMessage.objects.create(
            conversation=conversation,
            sender=sender,
            recipient=recipient,
            content=message
        )
        
        return private_message
