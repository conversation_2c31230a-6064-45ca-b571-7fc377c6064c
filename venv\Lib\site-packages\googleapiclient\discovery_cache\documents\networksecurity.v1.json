{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://networksecurity.googleapis.com/", "batchPath": "batch", "canonicalName": "NetworkSecurity", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/networking", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "networksecurity:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://networksecurity.mtls.googleapis.com/", "name": "networksecurity", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"resources": {"addressGroups": {"methods": {"addItems": {"description": "Adds items to an address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems", "httpMethod": "POST", "id": "networksecurity.organizations.locations.addressGroups.addItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to add items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:addItems", "request": {"$ref": "AddAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "cloneItems": {"description": "Clones items from one address group to another.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems", "httpMethod": "POST", "id": "networksecurity.organizations.locations.addressGroups.cloneItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:cloneItems", "request": {"$ref": "CloneAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new address group in a given project and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups", "httpMethod": "POST", "id": "networksecurity.organizations.locations.addressGroups.create", "parameterOrder": ["parent"], "parameters": {"addressGroupId": {"description": "Required. Short name of the AddressGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"authz_policy\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AddressGroup. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/addressGroups", "request": {"$ref": "AddressGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.organizations.locations.addressGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AddressGroup to delete. Must be in the format `projects/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "GET", "id": "networksecurity.organizations.locations.addressGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AddressGroup to get. Must be in the format `projects/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AddressGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists address groups in a given project and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups", "httpMethod": "GET", "id": "networksecurity.organizations.locations.addressGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of AddressGroups to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAddressGroupsResponse` Indicates that this is a continuation of a prior `ListAddressGroups` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the AddressGroups should be listed, specified in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/addressGroups", "response": {"$ref": "ListAddressGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listReferences": {"description": "Lists references of an address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences", "httpMethod": "GET", "id": "networksecurity.organizations.locations.addressGroups.listReferences", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of references to return. If unspecified, server will pick an appropriate default. Server may return fewer items than requested. A caller should only rely on response's next_page_token to determine if there are more AddressGroupUsers left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "path": "v1/{+addressGroup}:listReferences", "response": {"$ref": "ListAddressGroupReferencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates parameters of an address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.organizations.locations.addressGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the AddressGroup resource. It matches pattern `projects/*/locations/{location}/addressGroups/`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the AddressGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AddressGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeItems": {"description": "Removes items from an address group.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems", "httpMethod": "POST", "id": "networksecurity.organizations.locations.addressGroups.removeItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to remove items from. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:removeItems", "request": {"$ref": "RemoveAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "firewallEndpoints": {"methods": {"create": {"description": "Creates a new FirewallEndpoint in a given project and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints", "httpMethod": "POST", "id": "networksecurity.organizations.locations.firewallEndpoints.create", "parameterOrder": ["parent"], "parameters": {"firewallEndpointId": {"description": "Required. Id of the requesting object. If auto-generating Id server-side, remove this field and firewall_endpoint_id from the method_signature of Create RPC.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/firewallEndpoints", "request": {"$ref": "FirewallEndpoint"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Endpoint.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}", "httpMethod": "DELETE", "id": "networksecurity.organizations.locations.firewallEndpoints.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/firewallEndpoints/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Endpoint.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}", "httpMethod": "GET", "id": "networksecurity.organizations.locations.firewallEndpoints.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/firewallEndpoints/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "FirewallEndpoint"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists FirewallEndpoints in a given project and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints", "httpMethod": "GET", "id": "networksecurity.organizations.locations.firewallEndpoints.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListEndpointsRequest", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/firewallEndpoints", "response": {"$ref": "ListFirewallEndpointsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single Endpoint.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}", "httpMethod": "PATCH", "id": "networksecurity.organizations.locations.firewallEndpoints.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. name of resource", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/firewallEndpoints/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Endpoint resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "FirewallEndpoint"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networksecurity.organizations.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "networksecurity.organizations.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "networksecurity.organizations.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "networksecurity.organizations.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityProfileGroups": {"methods": {"create": {"description": "Creates a new SecurityProfileGroup in a given organization and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups", "httpMethod": "POST", "id": "networksecurity.organizations.locations.securityProfileGroups.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the SecurityProfileGroup. Must be in the format `projects|organizations/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "securityProfileGroupId": {"description": "Required. Short name of the SecurityProfileGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"security_profile_group1\".", "location": "query", "type": "string"}}, "path": "v1/{+parent}/securityProfileGroups", "request": {"$ref": "SecurityProfileGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single SecurityProfileGroup.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.organizations.locations.securityProfileGroups.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If client provided etag is out of date, delete will return FAILED_PRECONDITION error.", "location": "query", "type": "string"}, "name": {"description": "Required. A name of the SecurityProfileGroup to delete. Must be in the format `projects|organizations/*/locations/{location}/securityProfileGroups/{security_profile_group}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfileGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single SecurityProfileGroup.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}", "httpMethod": "GET", "id": "networksecurity.organizations.locations.securityProfileGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the SecurityProfileGroup to get. Must be in the format `projects|organizations/*/locations/{location}/securityProfileGroups/{security_profile_group}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfileGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "SecurityProfileGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SecurityProfileGroups in a given organization and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups", "httpMethod": "GET", "id": "networksecurity.organizations.locations.securityProfileGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of SecurityProfileGroups to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListSecurityProfileGroupsResponse` Indicates that this is a continuation of a prior `ListSecurityProfileGroups` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project or organization and location from which the SecurityProfileGroups should be listed, specified in the format `projects|organizations/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/securityProfileGroups", "response": {"$ref": "ListSecurityProfileGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single SecurityProfileGroup.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.organizations.locations.securityProfileGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. Name of the SecurityProfileGroup resource. It matches pattern `projects|organizations/*/locations/{location}/securityProfileGroups/{security_profile_group}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfileGroups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the SecurityProfileGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "SecurityProfileGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityProfiles": {"methods": {"create": {"description": "Creates a new SecurityProfile in a given organization and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles", "httpMethod": "POST", "id": "networksecurity.organizations.locations.securityProfiles.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the SecurityProfile. Must be in the format `projects|organizations/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "securityProfileId": {"description": "Required. Short name of the SecurityProfile resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"security_profile1\".", "location": "query", "type": "string"}}, "path": "v1/{+parent}/securityProfiles", "request": {"$ref": "SecurityProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single SecurityProfile.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}", "httpMethod": "DELETE", "id": "networksecurity.organizations.locations.securityProfiles.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If client provided etag is out of date, delete will return FAILED_PRECONDITION error.", "location": "query", "type": "string"}, "name": {"description": "Required. A name of the SecurityProfile to delete. Must be in the format `projects|organizations/*/locations/{location}/securityProfiles/{security_profile_id}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single SecurityProfile.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}", "httpMethod": "GET", "id": "networksecurity.organizations.locations.securityProfiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the SecurityProfile to get. Must be in the format `projects|organizations/*/locations/{location}/securityProfiles/{security_profile_id}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "SecurityProfile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SecurityProfiles in a given organization and location.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles", "httpMethod": "GET", "id": "networksecurity.organizations.locations.securityProfiles.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of SecurityProfiles to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListSecurityProfilesResponse` Indicates that this is a continuation of a prior `ListSecurityProfiles` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project or organization and location from which the SecurityProfiles should be listed, specified in the format `projects|organizations/*/locations/{location}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/securityProfiles", "response": {"$ref": "ListSecurityProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single SecurityProfile.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}", "httpMethod": "PATCH", "id": "networksecurity.organizations.locations.securityProfiles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. Name of the SecurityProfile resource. It matches pattern `projects|organizations/*/locations/{location}/securityProfiles/{security_profile}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/securityProfiles/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the SecurityProfile resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "SecurityProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "networksecurity.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"addressGroups": {"methods": {"addItems": {"description": "Adds items to an address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.addItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to add items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:addItems", "request": {"$ref": "AddAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "cloneItems": {"description": "Clones items from one address group to another.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.cloneItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:cloneItems", "request": {"$ref": "CloneAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new address group in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.create", "parameterOrder": ["parent"], "parameters": {"addressGroupId": {"description": "Required. Short name of the AddressGroup resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"authz_policy\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AddressGroup. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/addressGroups", "request": {"$ref": "AddressGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.addressGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AddressGroup to delete. Must be in the format `projects/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.addressGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AddressGroup to get. Must be in the format `projects/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AddressGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:getIamPolicy", "httpMethod": "GET", "id": "networksecurity.projects.locations.addressGroups.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists address groups in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups", "httpMethod": "GET", "id": "networksecurity.projects.locations.addressGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of AddressGroups to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAddressGroupsResponse` Indicates that this is a continuation of a prior `ListAddressGroups` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the AddressGroups should be listed, specified in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/addressGroups", "response": {"$ref": "ListAddressGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listReferences": {"description": "Lists references of an address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences", "httpMethod": "GET", "id": "networksecurity.projects.locations.addressGroups.listReferences", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to clone items to. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of references to return. If unspecified, server will pick an appropriate default. Server may return fewer items than requested. A caller should only rely on response's next_page_token to determine if there are more AddressGroupUsers left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}}, "path": "v1/{+addressGroup}:listReferences", "response": {"$ref": "ListAddressGroupReferencesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.addressGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the AddressGroup resource. It matches pattern `projects/*/locations/{location}/addressGroups/`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the AddressGroup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AddressGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeItems": {"description": "Removes items from an address group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.removeItems", "parameterOrder": ["addressGroup"], "parameters": {"addressGroup": {"description": "Required. A name of the AddressGroup to remove items from. Must be in the format `projects|organization/*/locations/{location}/addressGroups/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+addressGroup}:removeItems", "request": {"$ref": "RemoveAddressGroupItemsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:setIamPolicy", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:testIamPermissions", "httpMethod": "POST", "id": "networksecurity.projects.locations.addressGroups.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/addressGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "authorizationPolicies": {"methods": {"create": {"description": "Creates a new AuthorizationPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.authorizationPolicies.create", "parameterOrder": ["parent"], "parameters": {"authorizationPolicyId": {"description": "Required. Short name of the AuthorizationPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"authz_policy\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizationPolicy. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizationPolicies", "request": {"$ref": "AuthorizationPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single AuthorizationPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.authorizationPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AuthorizationPolicy to delete. Must be in the format `projects/{project}/locations/{location}/authorizationPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AuthorizationPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.authorizationPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the AuthorizationPolicy to get. Must be in the format `projects/{project}/locations/{location}/authorizationPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AuthorizationPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "networksecurity.projects.locations.authorizationPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AuthorizationPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.authorizationPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of AuthorizationPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAuthorizationPoliciesResponse` Indicates that this is a continuation of a prior `ListAuthorizationPolicies` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the AuthorizationPolicies should be listed, specified in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizationPolicies", "response": {"$ref": "ListAuthorizationPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single AuthorizationPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.authorizationPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the AuthorizationPolicy resource. It matches pattern `projects/{project}/locations/{location}/authorizationPolicies/`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the AuthorizationPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AuthorizationPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "networksecurity.projects.locations.authorizationPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "networksecurity.projects.locations.authorizationPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizationPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "authzPolicies": {"methods": {"create": {"description": "Creates a new AuthzPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.authzPolicies.create", "parameterOrder": ["parent"], "parameters": {"authzPolicyId": {"description": "Required. User-provided ID of the `AuthzPolicy` resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the `AuthzPolicy` resource. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/authzPolicies", "request": {"$ref": "AuthzPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single AuthzPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.authzPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `AuthzPolicy` resource to delete. Must be in the format `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AuthzPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.authzPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `AuthzPolicy` resource to get. Must be in the format `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AuthzPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "networksecurity.projects.locations.authzPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AuthzPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.authzPolicies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results that the server returns.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `AuthzPolicy` resources are listed, specified in the following format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authzPolicies", "response": {"$ref": "ListAuthzPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single AuthzPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.authzPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Name of the `AuthzPolicy` resource in the following format: `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, ignores the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Used to specify the fields to be overwritten in the `AuthzPolicy` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AuthzPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "networksecurity.projects.locations.authzPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authzPolicies/{authzPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "networksecurity.projects.locations.authzPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "backendAuthenticationConfigs": {"methods": {"create": {"description": "Creates a new BackendAuthenticationConfig in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs", "httpMethod": "POST", "id": "networksecurity.projects.locations.backendAuthenticationConfigs.create", "parameterOrder": ["parent"], "parameters": {"backendAuthenticationConfigId": {"description": "Required. Short name of the BackendAuthenticationConfig resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"backend-auth-config\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the BackendAuthenticationConfig. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backendAuthenticationConfigs", "request": {"$ref": "BackendAuthenticationConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single BackendAuthenticationConfig to BackendAuthenticationConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.backendAuthenticationConfigs.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. Etag of the resource. If this is provided, it must match the server's etag.", "location": "query", "type": "string"}, "name": {"description": "Required. A name of the BackendAuthenticationConfig to delete. Must be in the format `projects/*/locations/{location}/backendAuthenticationConfigs/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backendAuthenticationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single BackendAuthenticationConfig to BackendAuthenticationConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.backendAuthenticationConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the BackendAuthenticationConfig to get. Must be in the format `projects/*/locations/{location}/backendAuthenticationConfigs/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backendAuthenticationConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BackendAuthenticationConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists BackendAuthenticationConfigs in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs", "httpMethod": "GET", "id": "networksecurity.projects.locations.backendAuthenticationConfigs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of BackendAuthenticationConfigs to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListBackendAuthenticationConfigsResponse` Indicates that this is a continuation of a prior `ListBackendAuthenticationConfigs` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the BackendAuthenticationConfigs should be listed, specified in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backendAuthenticationConfigs", "response": {"$ref": "ListBackendAuthenticationConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single BackendAuthenticationConfig to BackendAuthenticationConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backendAuthenticationConfigs/{backendAuthenticationConfigsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.backendAuthenticationConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the BackendAuthenticationConfig resource. It matches the pattern `projects/*/locations/{location}/backendAuthenticationConfigs/{backend_authentication_config}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backendAuthenticationConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the BackendAuthenticationConfig resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BackendAuthenticationConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "clientTlsPolicies": {"methods": {"create": {"description": "Creates a new ClientTlsPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.clientTlsPolicies.create", "parameterOrder": ["parent"], "parameters": {"clientTlsPolicyId": {"description": "Required. Short name of the ClientTlsPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"client_mtls_policy\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the ClientTlsPolicy. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/clientTlsPolicies", "request": {"$ref": "ClientTlsPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ClientTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.clientTlsPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ClientTlsPolicy to delete. Must be in the format `projects/*/locations/{location}/clientTlsPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ClientTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.clientTlsPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ClientTlsPolicy to get. Must be in the format `projects/*/locations/{location}/clientTlsPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ClientTlsPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "networksecurity.projects.locations.clientTlsPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ClientTlsPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.clientTlsPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of ClientTlsPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListClientTlsPoliciesResponse` Indicates that this is a continuation of a prior `ListClientTlsPolicies` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the ClientTlsPolicies should be listed, specified in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/clientTlsPolicies", "response": {"$ref": "ListClientTlsPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ClientTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.clientTlsPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ClientTlsPolicy resource. It matches the pattern `projects/*/locations/{location}/clientTlsPolicies/{client_tls_policy}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ClientTlsPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ClientTlsPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "networksecurity.projects.locations.clientTlsPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "networksecurity.projects.locations.clientTlsPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clientTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "firewallEndpointAssociations": {"methods": {"create": {"description": "Creates a new FirewallEndpointAssociation in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations", "httpMethod": "POST", "id": "networksecurity.projects.locations.firewallEndpointAssociations.create", "parameterOrder": ["parent"], "parameters": {"firewallEndpointAssociationId": {"description": "Optional. Id of the requesting object. If auto-generating Id server-side, remove this field and firewall_endpoint_association_id from the method_signature of Create RPC.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/firewallEndpointAssociations", "request": {"$ref": "FirewallEndpointAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single FirewallEndpointAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.firewallEndpointAssociations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/firewallEndpointAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single FirewallEndpointAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.firewallEndpointAssociations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/firewallEndpointAssociations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "FirewallEndpointAssociation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Associations in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations", "httpMethod": "GET", "id": "networksecurity.projects.locations.firewallEndpointAssociations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for ListAssociationsRequest", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/firewallEndpointAssociations", "response": {"$ref": "ListFirewallEndpointAssociationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a single FirewallEndpointAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.firewallEndpointAssociations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. name of resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/firewallEndpointAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Association resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "FirewallEndpointAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "gatewaySecurityPolicies": {"methods": {"create": {"description": "Creates a new GatewaySecurityPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.create", "parameterOrder": ["parent"], "parameters": {"gatewaySecurityPolicyId": {"description": "Required. Short name of the GatewaySecurityPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"gateway_security_policy1\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the GatewaySecurityPolicy. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/gatewaySecurityPolicies", "request": {"$ref": "GatewaySecurityPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single GatewaySecurityPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the GatewaySecurityPolicy to delete. Must be in the format `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single GatewaySecurityPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the GatewaySecurityPolicy to get. Must be in the format `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GatewaySecurityPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists GatewaySecurityPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of GatewaySecurityPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last 'ListGatewaySecurityPoliciesResponse' Indicates that this is a continuation of a prior 'ListGatewaySecurityPolicies' call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the GatewaySecurityPolicies should be listed, specified in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/gatewaySecurityPolicies", "response": {"$ref": "ListGatewaySecurityPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single GatewaySecurityPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource. Name is of the form projects/{project}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy} gateway_security_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the GatewaySecurityPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GatewaySecurityPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"rules": {"methods": {"create": {"description": "Creates a new GatewaySecurityPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules", "httpMethod": "POST", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.rules.create", "parameterOrder": ["parent"], "parameters": {"gatewaySecurityPolicyRuleId": {"description": "The ID to use for the rule, which will become the final component of the rule's resource name. This value should be 4-63 characters, and valid characters are /a-z-/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent where this rule will be created. Format : projects/{project}/location/{location}/gatewaySecurityPolicies/*", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/rules", "request": {"$ref": "GatewaySecurityPolicyRule"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single GatewaySecurityPolicyRule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.rules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the GatewaySecurityPolicyRule to delete. Must be in the format `projects/{project}/locations/{location}/gatewaySecurityPolicies/{gatewaySecurityPolicy}/rules/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+/rules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single GatewaySecurityPolicyRule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.rules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the GatewaySecurityPolicyRule to retrieve. Format: projects/{project}/location/{location}/gatewaySecurityPolicies/*/rules/*", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+/rules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GatewaySecurityPolicyRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists GatewaySecurityPolicyRules in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules", "httpMethod": "GET", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.rules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of GatewaySecurityPolicyRules to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last 'ListGatewaySecurityPolicyRulesResponse' Indicates that this is a continuation of a prior 'ListGatewaySecurityPolicyRules' call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project, location and GatewaySecurityPolicy from which the GatewaySecurityPolicyRules should be listed, specified in the format `projects/{project}/locations/{location}/gatewaySecurityPolicies/{gatewaySecurityPolicy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/rules", "response": {"$ref": "ListGatewaySecurityPolicyRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single GatewaySecurityPolicyRule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.gatewaySecurityPolicies.rules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Immutable. Name of the resource. ame is the full resource name so projects/{project}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy}/rules/{rule} rule should match the pattern: (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gatewaySecurityPolicies/[^/]+/rules/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the GatewaySecurityPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GatewaySecurityPolicyRule"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "interceptDeploymentGroups": {"methods": {"create": {"description": "Creates a deployment group in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups", "httpMethod": "POST", "id": "networksecurity.projects.locations.interceptDeploymentGroups.create", "parameterOrder": ["parent"], "parameters": {"interceptDeploymentGroupId": {"description": "Required. The ID to use for the new deployment group, which will become the final component of the deployment group's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this deployment group will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/interceptDeploymentGroups", "request": {"$ref": "InterceptDeploymentGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a deployment group. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.interceptDeploymentGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The deployment group to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeploymentGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific deployment group. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptDeploymentGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment group to retrieve. Format: projects/{project}/locations/{location}/interceptDeploymentGroups/{intercept_deployment_group}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeploymentGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InterceptDeploymentGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists deployment groups in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptDeploymentGroups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListInterceptDeploymentGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptDeploymentGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deployment groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/interceptDeploymentGroups", "response": {"$ref": "ListInterceptDeploymentGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a deployment group. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeploymentGroups/{interceptDeploymentGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.interceptDeploymentGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeploymentGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the deployment group (e.g. `description`; *not* `intercept_deployment_group.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InterceptDeploymentGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "interceptDeployments": {"methods": {"create": {"description": "Creates a deployment in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeployments", "httpMethod": "POST", "id": "networksecurity.projects.locations.interceptDeployments.create", "parameterOrder": ["parent"], "parameters": {"interceptDeploymentId": {"description": "Required. The ID to use for the new deployment, which will become the final component of the deployment's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this deployment will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/interceptDeployments", "request": {"$ref": "InterceptDeployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a deployment. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.interceptDeployments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific deployment. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptDeployments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment to retrieve. Format: projects/{project}/locations/{location}/interceptDeployments/{intercept_deployment}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InterceptDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists deployments in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeployments", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptDeployments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListInterceptDeployments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptDeployments` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deployments. Example: `projects/123456789/locations/us-central1-a`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/interceptDeployments", "response": {"$ref": "ListInterceptDeploymentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a deployment. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptDeployments/{interceptDeploymentsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.interceptDeployments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptDeployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the deployment (e.g. `description`; *not* `intercept_deployment.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InterceptDeployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "interceptEndpointGroupAssociations": {"methods": {"create": {"description": "Creates an association in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations", "httpMethod": "POST", "id": "networksecurity.projects.locations.interceptEndpointGroupAssociations.create", "parameterOrder": ["parent"], "parameters": {"interceptEndpointGroupAssociationId": {"description": "Optional. The ID to use for the new association, which will become the final component of the endpoint group's resource name. If not provided, the server will generate a unique ID.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this association will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/interceptEndpointGroupAssociations", "request": {"$ref": "InterceptEndpointGroupAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an association. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.interceptEndpointGroupAssociations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The association to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific association. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptEndpointGroupAssociations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the association to retrieve. Format: projects/{project}/locations/{location}/interceptEndpointGroupAssociations/{intercept_endpoint_group_association}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InterceptEndpointGroupAssociation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists associations in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptEndpointGroupAssociations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListInterceptEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of associations. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/interceptEndpointGroupAssociations", "response": {"$ref": "ListInterceptEndpointGroupAssociationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an association. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroupAssociations/{interceptEndpointGroupAssociationsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.interceptEndpointGroupAssociations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the association (e.g. `description`; *not* `intercept_endpoint_group_association.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InterceptEndpointGroupAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "interceptEndpointGroups": {"methods": {"create": {"description": "Creates an endpoint group in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups", "httpMethod": "POST", "id": "networksecurity.projects.locations.interceptEndpointGroups.create", "parameterOrder": ["parent"], "parameters": {"interceptEndpointGroupId": {"description": "Required. The ID to use for the endpoint group, which will become the final component of the endpoint group's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this endpoint group will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/interceptEndpointGroups", "request": {"$ref": "InterceptEndpointGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an endpoint group. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.interceptEndpointGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The endpoint group to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific endpoint group. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptEndpointGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the endpoint group to retrieve. Format: projects/{project}/locations/{location}/interceptEndpointGroups/{intercept_endpoint_group}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InterceptEndpointGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists endpoint groups in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups", "httpMethod": "GET", "id": "networksecurity.projects.locations.interceptEndpointGroups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListInterceptEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListInterceptEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of endpoint groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/interceptEndpointGroups", "response": {"$ref": "ListInterceptEndpointGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an endpoint group. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/interceptEndpointGroups/{interceptEndpointGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.interceptEndpointGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/interceptEndpointGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the endpoint group (e.g. `description`; *not* `intercept_endpoint_group.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "InterceptEndpointGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "mirroringDeploymentGroups": {"methods": {"create": {"description": "Creates a deployment group in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups", "httpMethod": "POST", "id": "networksecurity.projects.locations.mirroringDeploymentGroups.create", "parameterOrder": ["parent"], "parameters": {"mirroringDeploymentGroupId": {"description": "Required. The ID to use for the new deployment group, which will become the final component of the deployment group's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this deployment group will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/mirroringDeploymentGroups", "request": {"$ref": "MirroringDeploymentGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a deployment group. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.mirroringDeploymentGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The deployment group to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeploymentGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific deployment group. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringDeploymentGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment group to retrieve. Format: projects/{project}/locations/{location}/mirroringDeploymentGroups/{mirroring_deployment_group}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeploymentGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MirroringDeploymentGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists deployment groups in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringDeploymentGroups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListMirroringDeploymentGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringDeploymentGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deployment groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mirroringDeploymentGroups", "response": {"$ref": "ListMirroringDeploymentGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a deployment group. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeploymentGroups/{mirroringDeploymentGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.mirroringDeploymentGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeploymentGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the deployment group (e.g. `description`; *not* `mirroring_deployment_group.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MirroringDeploymentGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "mirroringDeployments": {"methods": {"create": {"description": "Creates a deployment in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments", "httpMethod": "POST", "id": "networksecurity.projects.locations.mirroringDeployments.create", "parameterOrder": ["parent"], "parameters": {"mirroringDeploymentId": {"description": "Required. The ID to use for the new deployment, which will become the final component of the deployment's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this deployment will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/mirroringDeployments", "request": {"$ref": "MirroringDeployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a deployment. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.mirroringDeployments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific deployment. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringDeployments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment to retrieve. Format: projects/{project}/locations/{location}/mirroringDeployments/{mirroring_deployment}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MirroringDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists deployments in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringDeployments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListMirroringDeployments` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringDeployments` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deployments. Example: `projects/123456789/locations/us-central1-a`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mirroringDeployments", "response": {"$ref": "ListMirroringDeploymentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a deployment. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringDeployments/{mirroringDeploymentsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.mirroringDeployments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringDeployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the deployment (e.g. `description`; *not* `mirroring_deployment.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MirroringDeployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "mirroringEndpointGroupAssociations": {"methods": {"create": {"description": "Creates an association in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations", "httpMethod": "POST", "id": "networksecurity.projects.locations.mirroringEndpointGroupAssociations.create", "parameterOrder": ["parent"], "parameters": {"mirroringEndpointGroupAssociationId": {"description": "Optional. The ID to use for the new association, which will become the final component of the endpoint group's resource name. If not provided, the server will generate a unique ID.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this association will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/mirroringEndpointGroupAssociations", "request": {"$ref": "MirroringEndpointGroupAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an association. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.mirroringEndpointGroupAssociations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The association to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific association. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringEndpointGroupAssociations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the association to retrieve. Format: projects/{project}/locations/{location}/mirroringEndpointGroupAssociations/{mirroring_endpoint_group_association}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MirroringEndpointGroupAssociation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists associations in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringEndpointGroupAssociations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListMirroringEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of associations. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mirroringEndpointGroupAssociations", "response": {"$ref": "ListMirroringEndpointGroupAssociationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an association. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroupAssociations/{mirroringEndpointGroupAssociationsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.mirroringEndpointGroupAssociations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroupAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the association (e.g. `description`; *not* `mirroring_endpoint_group_association.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MirroringEndpointGroupAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "mirroringEndpointGroups": {"methods": {"create": {"description": "Creates an endpoint group in a given project and location. See https://google.aip.dev/133.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups", "httpMethod": "POST", "id": "networksecurity.projects.locations.mirroringEndpointGroups.create", "parameterOrder": ["parent"], "parameters": {"mirroringEndpointGroupId": {"description": "Required. The ID to use for the endpoint group, which will become the final component of the endpoint group's resource name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this endpoint group will be created. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/mirroringEndpointGroups", "request": {"$ref": "MirroringEndpointGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an endpoint group. See https://google.aip.dev/135.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.mirroringEndpointGroups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The endpoint group to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a specific endpoint group. See https://google.aip.dev/131.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringEndpointGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the endpoint group to retrieve. Format: projects/{project}/locations/{location}/mirroringEndpointGroups/{mirroring_endpoint_group}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MirroringEndpointGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists endpoint groups in a given project and location. See https://google.aip.dev/132.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups", "httpMethod": "GET", "id": "networksecurity.projects.locations.mirroringEndpointGroups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression. See https://google.aip.dev/160#filtering for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Sort expression. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. See https://google.aip.dev/158 for more details.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListMirroringEndpointGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMirroringEndpointGroups` must match the call that provided the page token. See https://google.aip.dev/158 for more details.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of endpoint groups. Example: `projects/123456789/locations/global`. See https://google.aip.dev/132 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mirroringEndpointGroups", "response": {"$ref": "ListMirroringEndpointGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an endpoint group. See https://google.aip.dev/134.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/mirroringEndpointGroups/{mirroringEndpointGroupsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.mirroringEndpointGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/mirroringEndpointGroups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique identifier for this request. Must be a UUID4. This request is only idempotent if a `request_id` is provided. See https://google.aip.dev/155 for more details.", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. The list of fields to update. Fields are specified relative to the endpoint group (e.g. `description`; *not* `mirroring_endpoint_group.description`). See https://google.aip.dev/161 for more details.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MirroringEndpointGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networksecurity.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "networksecurity.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serverTlsPolicies": {"methods": {"create": {"description": "Creates a new ServerTlsPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.serverTlsPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the ServerTlsPolicy. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serverTlsPolicyId": {"description": "Required. Short name of the ServerTlsPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"server_mtls_policy\".", "location": "query", "type": "string"}}, "path": "v1/{+parent}/serverTlsPolicies", "request": {"$ref": "ServerTlsPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServerTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.serverTlsPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServerTlsPolicy to delete. Must be in the format `projects/*/locations/{location}/serverTlsPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServerTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.serverTlsPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServerTlsPolicy to get. Must be in the format `projects/*/locations/{location}/serverTlsPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServerTlsPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "networksecurity.projects.locations.serverTlsPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServerTlsPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.serverTlsPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of ServerTlsPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListServerTlsPoliciesResponse` Indicates that this is a continuation of a prior `ListServerTlsPolicies` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the ServerTlsPolicies should be listed, specified in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. Setting this field to `true` will opt the request into returning the resources that are reachable, and into including the names of those that were unreachable in the [ListServerTlsPoliciesResponse.unreachable] field. This can only be `true` when reading across collections e.g. when `parent` is set to `\"projects/example/locations/-\"`.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/serverTlsPolicies", "response": {"$ref": "ListServerTlsPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServerTlsPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.serverTlsPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ServerTlsPolicy resource. It matches the pattern `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServerTlsPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ServerTlsPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "networksecurity.projects.locations.serverTlsPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "networksecurity.projects.locations.serverTlsPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serverTlsPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "tlsInspectionPolicies": {"methods": {"create": {"description": "Creates a new TlsInspectionPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies", "httpMethod": "POST", "id": "networksecurity.projects.locations.tlsInspectionPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the TlsInspectionPolicy. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "tlsInspectionPolicyId": {"description": "Required. Short name of the TlsInspectionPolicy resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"tls_inspection_policy1\".", "location": "query", "type": "string"}}, "path": "v1/{+parent}/tlsInspectionPolicies", "request": {"$ref": "TlsInspectionPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single TlsInspectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.tlsInspectionPolicies.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "If set to true, any rules for this TlsInspectionPolicy will also be deleted. (Otherwise, the request will only work if the TlsInspectionPolicy has no rules.)", "location": "query", "type": "boolean"}, "name": {"description": "Required. A name of the TlsInspectionPolicy to delete. Must be in the format `projects/{project}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsInspectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single TlsInspectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.tlsInspectionPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the TlsInspectionPolicy to get. Must be in the format `projects/{project}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsInspectionPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "TlsInspectionPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists TlsInspectionPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies", "httpMethod": "GET", "id": "networksecurity.projects.locations.tlsInspectionPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of TlsInspectionPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last 'ListTlsInspectionPoliciesResponse' Indicates that this is a continuation of a prior 'ListTlsInspectionPolicies' call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the TlsInspectionPolicies should be listed, specified in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/tlsInspectionPolicies", "response": {"$ref": "ListTlsInspectionPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single TlsInspectionPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.tlsInspectionPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource. Name is of the form projects/{project}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy} tls_inspection_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsInspectionPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the TlsInspectionPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "TlsInspectionPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "urlLists": {"methods": {"create": {"description": "Creates a new UrlList in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/urlLists", "httpMethod": "POST", "id": "networksecurity.projects.locations.urlLists.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the UrlList. Must be in the format `projects/*/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "urlListId": {"description": "Required. Short name of the UrlList resource to be created. This value should be 1-63 characters long, containing only letters, numbers, hyphens, and underscores, and should not start with a number. E.g. \"url_list\".", "location": "query", "type": "string"}}, "path": "v1/{+parent}/urlLists", "request": {"$ref": "UrlList"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single UrlList.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}", "httpMethod": "DELETE", "id": "networksecurity.projects.locations.urlLists.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the UrlList to delete. Must be in the format `projects/*/locations/{location}/urlLists/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/urlLists/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single UrlList.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}", "httpMethod": "GET", "id": "networksecurity.projects.locations.urlLists.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the UrlList to get. Must be in the format `projects/*/locations/{location}/urlLists/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/urlLists/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "UrlList"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists UrlLists in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/urlLists", "httpMethod": "GET", "id": "networksecurity.projects.locations.urlLists.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of UrlLists to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListUrlListsResponse` Indicates that this is a continuation of a prior `ListUrlLists` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the UrlLists should be listed, specified in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/urlLists", "response": {"$ref": "ListUrlListsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single UrlList.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}", "httpMethod": "PATCH", "id": "networksecurity.projects.locations.urlLists.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource provided by the user. Name is of the form projects/{project}/locations/{location}/urlLists/{url_list} url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/urlLists/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the UrlList resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "UrlList"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250604", "rootUrl": "https://networksecurity.googleapis.com/", "schemas": {"AddAddressGroupItemsRequest": {"description": "Request used by the AddAddressGroupItems method.", "id": "AddAddressGroupItemsRequest", "properties": {"items": {"description": "Required. List of items to add.", "items": {"type": "string"}, "type": "array"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "AddressGroup": {"description": "AddressGroup is a resource that specifies how a collection of IP/DNS used in Firewall Policy.", "id": "AddressGroup", "properties": {"capacity": {"description": "Required. Capacity of the Address Group", "format": "int32", "type": "integer"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "items": {"description": "Optional. List of items.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the AddressGroup resource.", "type": "object"}, "name": {"description": "Required. Name of the AddressGroup resource. It matches pattern `projects/*/locations/{location}/addressGroups/`.", "type": "string"}, "purpose": {"description": "Optional. List of supported purposes of the Address Group.", "items": {"enum": ["PURPOSE_UNSPECIFIED", "DEFAULT", "CLOUD_ARMOR"], "enumDescriptions": ["Default value. Should never happen.", "Address Group is distributed to VMC, and is usable in Firewall Policies and other systems that rely on VMC.", "Address Group is usable in Cloud Armor."], "type": "string"}, "type": "array"}, "selfLink": {"description": "Output only. Server-defined fully-qualified URL for this resource.", "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of the Address Group. Possible values are \"IPv4\" or \"IPV6\".", "enum": ["TYPE_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["Default value.", "IP v4 ranges.", "IP v6 ranges."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AntivirusOverride": {"description": "Defines what action to take for antivirus threats per protocol.", "id": "AntivirusOverride", "properties": {"action": {"description": "Required. Threat action override. For some threat types, only a subset of actions applies.", "enum": ["THREAT_ACTION_UNSPECIFIED", "DEFAULT_ACTION", "ALLOW", "ALERT", "DENY"], "enumDescriptions": ["Threat action not specified.", "The default action (as specified by the vendor) is taken.", "The packet matching this rule will be allowed to transmit.", "The packet matching this rule will be allowed to transmit, but a threat_log entry will be sent to the consumer project.", "The packet matching this rule will be dropped, and a threat_log entry will be sent to the consumer project."], "type": "string"}, "protocol": {"description": "Required. Protocol to match.", "enum": ["PROTOCOL_UNSPECIFIED", "SMTP", "SMB", "POP3", "IMAP", "HTTP2", "HTTP", "FTP"], "enumDescriptions": ["Protocol not specified.", "SMTP protocol", "SMB protocol", "POP3 protocol", "IMAP protocol", "HTTP2 protocol", "HTTP protocol", "FTP protocol"], "type": "string"}}, "type": "object"}, "AuthorizationPolicy": {"description": "AuthorizationPolicy is a resource that specifies how a server should authorize incoming connections. This resource in itself does not change the configuration unless it's attached to a target https proxy or endpoint config selector resource.", "id": "AuthorizationPolicy", "properties": {"action": {"description": "Required. The action to take when a rule match is found. Possible values are \"ALLOW\" or \"DENY\".", "enum": ["ACTION_UNSPECIFIED", "ALLOW", "DENY"], "enumDescriptions": ["Default value.", "Grant access.", "Deny access. Deny rules should be avoided unless they are used to provide a default \"deny all\" fallback."], "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the AuthorizationPolicy resource.", "type": "object"}, "name": {"description": "Required. Name of the AuthorizationPolicy resource. It matches pattern `projects/{project}/locations/{location}/authorizationPolicies/`.", "type": "string"}, "rules": {"description": "Optional. List of rules to match. Note that at least one of the rules must match in order for the action specified in the 'action' field to be taken. A rule is a match if there is a matching source and destination. If left blank, the action specified in the `action` field will be applied on every request.", "items": {"$ref": "Rule"}, "type": "array"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AuthzPolicy": {"description": "`AuthzPolicy` is a resource that allows to forward traffic to a callout backend designed to scan the traffic for security purposes.", "id": "AuthzPolicy", "properties": {"action": {"description": "Required. Can be one of `ALLOW`, `DENY`, `CUSTOM`. When the action is `CUSTOM`, `customProvider` must be specified. When the action is `ALLOW`, only requests matching the policy will be allowed. When the action is `DENY`, only requests matching the policy will be denied. When a request arrives, the policies are evaluated in the following order: 1. If there is a `CUSTOM` policy that matches the request, the `CUSTOM` policy is evaluated using the custom authorization providers and the request is denied if the provider rejects the request. 2. If there are any `DENY` policies that match the request, the request is denied. 3. If there are no `ALLOW` policies for the resource or if any of the `ALLOW` policies match the request, the request is allowed. 4. Else the request is denied by default if none of the configured AuthzPolicies with `ALLOW` action match the request.", "enum": ["AUTHZ_ACTION_UNSPECIFIED", "ALLOW", "DENY", "CUSTOM"], "enumDescriptions": ["Unspecified action.", "Allow request to pass through to the backend.", "Deny the request and return a HTTP 404 to the client.", "Delegate the authorization decision to an external authorization engine."], "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customProvider": {"$ref": "AuthzPolicyCustomProvider", "description": "Optional. Required if the action is `CUSTOM`. Allows delegating authorization decisions to Cloud IAP or to Service Extensions. One of `cloudIap` or `authzExtension` must be specified."}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "httpRules": {"description": "Optional. A list of authorization HTTP rules to match against the incoming request. A policy match occurs when at least one HTTP rule matches the request or when no HTTP rules are specified in the policy. At least one HTTP Rule is required for Allow or Deny Action. Limited to 5 rules.", "items": {"$ref": "AuthzPolicyAuthzRule"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `AuthzPolicy` resource. The format must comply with [the following requirements](/compute/docs/labeling-resources#requirements).", "type": "object"}, "name": {"description": "Required. Identifier. Name of the `AuthzPolicy` resource in the following format: `projects/{project}/locations/{location}/authzPolicies/{authz_policy}`.", "type": "string"}, "target": {"$ref": "AuthzPolicyTarget", "description": "Required. Specifies the set of resources to which this policy should be applied to."}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AuthzPolicyAuthzRule": {"description": "Conditions to match against the incoming request.", "id": "AuthzPolicyAuthzRule", "properties": {"from": {"$ref": "AuthzPolicyAuthzRuleFrom", "description": "Optional. Describes properties of a source of a request."}, "to": {"$ref": "AuthzPolicyAuthzRuleTo", "description": "Optional. Describes properties of a target of a request."}, "when": {"description": "Optional. CEL expression that describes the conditions to be satisfied for the action. The result of the CEL expression is ANDed with the from and to. Refer to the CEL language reference for a list of available attributes.", "type": "string"}}, "type": "object"}, "AuthzPolicyAuthzRuleFrom": {"description": "Describes properties of one or more sources of a request.", "id": "AuthzPolicyAuthzRuleFrom", "properties": {"notSources": {"description": "Optional. Describes the negated properties of request sources. Matches requests from sources that do not match the criteria specified in this field. At least one of sources or notSources must be specified.", "items": {"$ref": "AuthzPolicyAuthzRuleFromRequestSource"}, "type": "array"}, "sources": {"description": "Optional. Describes the properties of a request's sources. At least one of sources or notSources must be specified. Limited to 1 source. A match occurs when ANY source (in sources or notSources) matches the request. Within a single source, the match follows AND semantics across fields and OR semantics within a single field, i.e. a match occurs when ANY principal matches AND ANY ipBlocks match.", "items": {"$ref": "AuthzPolicyAuthzRuleFromRequestSource"}, "type": "array"}}, "type": "object"}, "AuthzPolicyAuthzRuleFromRequestSource": {"description": "Describes the properties of a single source.", "id": "AuthzPolicyAuthzRuleFromRequestSource", "properties": {"ipBlocks": {"description": "Optional. A list of IPs or CIDRs to match against the source IP of a request. Limited to 5 ip_blocks.", "items": {"$ref": "AuthzPolicyAuthzRuleIpBlock"}, "type": "array"}, "resources": {"description": "Optional. A list of resources to match against the resource of the source VM of a request. Limited to 5 resources.", "items": {"$ref": "AuthzPolicyAuthzRuleRequestResource"}, "type": "array"}}, "type": "object"}, "AuthzPolicyAuthzRuleHeaderMatch": {"description": "Determines how a HTTP header should be matched.", "id": "AuthzPolicyAuthzRuleHeaderMatch", "properties": {"name": {"description": "Optional. Specifies the name of the header in the request.", "type": "string"}, "value": {"$ref": "AuthzPolicyAuthzRuleStringMatch", "description": "Optional. Specifies how the header match will be performed."}}, "type": "object"}, "AuthzPolicyAuthzRuleIpBlock": {"description": "Represents a range of IP Addresses.", "id": "AuthzPolicyAuthzRuleIpBlock", "properties": {"length": {"description": "Required. The length of the address range.", "format": "int32", "type": "integer"}, "prefix": {"description": "Required. The address prefix.", "type": "string"}}, "type": "object"}, "AuthzPolicyAuthzRuleRequestResource": {"description": "Describes the properties of a client VM resource accessing the internal application load balancers.", "id": "AuthzPolicyAuthzRuleRequestResource", "properties": {"iamServiceAccount": {"$ref": "AuthzPolicyAuthzRuleStringMatch", "description": "Optional. An IAM service account to match against the source service account of the VM sending the request."}, "tagValueIdSet": {"$ref": "AuthzPolicyAuthzRuleRequestResourceTagValueIdSet", "description": "Optional. A list of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request."}}, "type": "object"}, "AuthzPolicyAuthzRuleRequestResourceTagValueIdSet": {"description": "Describes a set of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request.", "id": "AuthzPolicyAuthzRuleRequestResourceTagValueIdSet", "properties": {"ids": {"description": "Required. A list of resource tag value permanent IDs to match against the resource manager tags value associated with the source VM of a request. The match follows AND semantics which means all the ids must match. Limited to 5 matches.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "AuthzPolicyAuthzRuleStringMatch": {"description": "Determines how a string value should be matched.", "id": "AuthzPolicyAuthzRuleStringMatch", "properties": {"contains": {"description": "The input string must have the substring specified here. Note: empty contains match is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``xyz.abc.def``", "type": "string"}, "exact": {"description": "The input string must match exactly the string specified here. Examples: * ``abc`` only matches the value ``abc``.", "type": "string"}, "ignoreCase": {"description": "If true, indicates the exact/prefix/suffix/contains matching should be case insensitive. For example, the matcher ``data`` will match both input string ``Data`` and ``data`` if set to true.", "type": "boolean"}, "prefix": {"description": "The input string must have the prefix specified here. Note: empty prefix is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``abc.xyz``", "type": "string"}, "suffix": {"description": "The input string must have the suffix specified here. Note: empty prefix is not allowed, please use regex instead. Examples: * ``abc`` matches the value ``xyz.abc``", "type": "string"}}, "type": "object"}, "AuthzPolicyAuthzRuleTo": {"description": "Describes properties of one or more targets of a request.", "id": "AuthzPolicyAuthzRuleTo", "properties": {"notOperations": {"description": "Optional. Describes the negated properties of the targets of a request. Matches requests for operations that do not match the criteria specified in this field. At least one of operations or notOperations must be specified.", "items": {"$ref": "AuthzPolicyAuthzRuleToRequestOperation"}, "type": "array"}, "operations": {"description": "Optional. Describes properties of one or more targets of a request. At least one of operations or notOperations must be specified. Limited to 1 operation. A match occurs when ANY operation (in operations or notOperations) matches. Within an operation, the match follows AND semantics across fields and OR semantics within a field, i.e. a match occurs when ANY path matches AND ANY header matches and ANY method matches.", "items": {"$ref": "AuthzPolicyAuthzRuleToRequestOperation"}, "type": "array"}}, "type": "object"}, "AuthzPolicyAuthzRuleToRequestOperation": {"description": "Describes properties of one or more targets of a request.", "id": "AuthzPolicyAuthzRuleToRequestOperation", "properties": {"headerSet": {"$ref": "AuthzPolicyAuthzRuleToRequestOperationHeaderSet", "description": "Optional. A list of headers to match against in http header."}, "hosts": {"description": "Optional. A list of HTTP Hosts to match against. The match can be one of exact, prefix, suffix, or contains (substring match). Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches.", "items": {"$ref": "AuthzPolicyAuthzRuleStringMatch"}, "type": "array"}, "methods": {"description": "Optional. A list of HTTP methods to match against. Each entry must be a valid HTTP method name (GET, PUT, POST, HEAD, PATCH, DELETE, OPTIONS). It only allows exact match and is always case sensitive.", "items": {"type": "string"}, "type": "array"}, "paths": {"description": "Optional. A list of paths to match against. The match can be one of exact, prefix, suffix, or contains (substring match). Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches. Note that this path match includes the query parameters. For gRPC services, this should be a fully-qualified name of the form /package.service/method.", "items": {"$ref": "AuthzPolicyAuthzRuleStringMatch"}, "type": "array"}}, "type": "object"}, "AuthzPolicyAuthzRuleToRequestOperationHeaderSet": {"description": "Describes a set of HTTP headers to match against.", "id": "AuthzPolicyAuthzRuleToRequestOperationHeaderSet", "properties": {"headers": {"description": "Required. A list of headers to match against in http header. The match can be one of exact, prefix, suffix, or contains (substring match). The match follows AND semantics which means all the headers must match. Matches are always case sensitive unless the ignoreCase is set. Limited to 5 matches.", "items": {"$ref": "AuthzPolicyAuthzRuleHeaderMatch"}, "type": "array"}}, "type": "object"}, "AuthzPolicyCustomProvider": {"description": "Allows delegating authorization decisions to Cloud IAP or to Service Extensions.", "id": "AuthzPolicyCustomProvider", "properties": {"authzExtension": {"$ref": "AuthzPolicyCustomProviderAuthzExtension", "description": "Optional. Delegate authorization decision to user authored Service Extension. Only one of cloudIap or authzExtension can be specified."}, "cloudIap": {"$ref": "AuthzPolicyCustomProviderCloudIap", "description": "Optional. Delegates authorization decisions to Cloud IAP. Applicable only for managed load balancers. Enabling Cloud IAP at the AuthzPolicy level is not compatible with Cloud IAP settings in the BackendService. Enabling IAP in both places will result in request failure. Ensure that IAP is enabled in either the AuthzPolicy or the BackendService but not in both places."}}, "type": "object"}, "AuthzPolicyCustomProviderAuthzExtension": {"description": "Optional. Delegate authorization decision to user authored extension. Only one of cloudIap or authzExtension can be specified.", "id": "AuthzPolicyCustomProviderAuthzExtension", "properties": {"resources": {"description": "Required. A list of references to authorization extensions that will be invoked for requests matching this policy. Limited to 1 custom provider.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AuthzPolicyCustomProviderCloudIap": {"description": "Optional. Delegates authorization decisions to Cloud IAP. Applicable only for managed load balancers. Enabling Cloud IAP at the AuthzPolicy level is not compatible with Cloud IAP settings in the BackendService. Enabling IAP in both places will result in request failure. Ensure that IAP is enabled in either the AuthzPolicy or the BackendService but not in both places.", "id": "AuthzPolicyCustomProviderCloudIap", "properties": {}, "type": "object"}, "AuthzPolicyTarget": {"description": "Specifies the set of targets to which this policy should be applied to.", "id": "AuthzPolicyTarget", "properties": {"loadBalancingScheme": {"description": "Required. All gateways and forwarding rules referenced by this policy and extensions must share the same load balancing scheme. Supported values: `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more information, refer to [Backend services overview](https://cloud.google.com/load-balancing/docs/backend-service).", "enum": ["LOAD_BALANCING_SCHEME_UNSPECIFIED", "INTERNAL_MANAGED", "EXTERNAL_MANAGED", "INTERNAL_SELF_MANAGED"], "enumDescriptions": ["Default value. Do not use.", "Signifies that this is used for Regional internal or Cross-region internal Application Load Balancing.", "Signifies that this is used for Global external or Regional external Application Load Balancing.", "Signifies that this is used for Cloud Service Mesh. Meant for use by CSM GKE controller only."], "type": "string"}, "resources": {"description": "Required. A list of references to the Forwarding Rules on which this policy will be applied.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BackendAuthenticationConfig": {"description": "BackendAuthenticationConfig message groups the TrustConfig together with other settings that control how the load balancer authenticates, and expresses its identity to, the backend: * `trustConfig` is the attached TrustConfig. * `wellKnownRoots` indicates whether the load balance should trust backend server certificates that are issued by public certificate authorities, in addition to certificates trusted by the TrustConfig. * `clientCertificate` is a client certificate that the load balancer uses to express its identity to the backend, if the connection to the backend uses mTLS. You can attach the BackendAuthenticationConfig to the load balancer’s BackendService directly determining how that BackendService negotiates TLS.", "id": "BackendAuthenticationConfig", "properties": {"clientCertificate": {"description": "Optional. A reference to a certificatemanager.googleapis.com.Certificate resource. This is a relative resource path following the form \"projects/{project}/locations/{location}/certificates/{certificate}\". Used by a BackendService to negotiate mTLS when the backend connection uses TLS and the backend requests a client certificate. Must have a CLIENT_AUTH scope.", "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "etag": {"description": "Output only. Etag of the resource.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Set of label tags associated with the resource.", "type": "object"}, "name": {"description": "Required. Name of the BackendAuthenticationConfig resource. It matches the pattern `projects/*/locations/{location}/backendAuthenticationConfigs/{backend_authentication_config}`", "type": "string"}, "trustConfig": {"description": "Optional. A reference to a TrustConfig resource from the certificatemanager.googleapis.com namespace. This is a relative resource path following the form \"projects/{project}/locations/{location}/trustConfigs/{trust_config}\". A BackendService uses the chain of trust represented by this TrustConfig, if specified, to validate the server certificates presented by the backend. Required unless wellKnownRoots is set to PUBLIC_ROOTS.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "wellKnownRoots": {"description": "Well known roots to use for server certificate validation.", "enum": ["WELL_KNOWN_ROOTS_UNSPECIFIED", "NONE", "PUBLIC_ROOTS"], "enumDescriptions": ["Equivalent to NONE.", "The BackendService will only validate server certificates against roots specified in TrustConfig.", "The BackendService uses a set of well-known public roots, in addition to any roots specified in the trustConfig field, when validating the server certificates presented by the backend. Validation with these roots is only considered when the TlsSettings.sni field in the BackendService is set. The well-known roots are a set of root CAs managed by Google. CAs in this set can be added or removed without notice."], "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CertificateProviderInstance": {"description": "Specification of a TLS certificate provider instance. Workloads may have one or more CertificateProvider instances (plugins) and one of them is enabled and configured by specifying this message. Workloads use the values from this message to locate and load the CertificateProvider instance configuration.", "id": "CertificateProviderInstance", "properties": {"pluginInstance": {"description": "Required. Plugin instance name, used to locate and load CertificateProvider instance configuration. Set to \"google_cloud_private_spiffe\" to use Certificate Authority Service certificate provider instance.", "type": "string"}}, "type": "object"}, "ClientTlsPolicy": {"description": "ClientTlsPolicy is a resource that specifies how a client should authenticate connections to backends of a service. This resource itself does not affect configuration unless it is attached to a backend service resource.", "id": "ClientTlsPolicy", "properties": {"clientCertificate": {"$ref": "GoogleCloudNetworksecurityV1CertificateProvider", "description": "Optional. Defines a mechanism to provision client identity (public and private keys) for peer to peer authentication. The presence of this dictates mTLS."}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the resource.", "type": "object"}, "name": {"description": "Required. Name of the ClientTlsPolicy resource. It matches the pattern `projects/*/locations/{location}/clientTlsPolicies/{client_tls_policy}`", "type": "string"}, "serverValidationCa": {"description": "Optional. Defines the mechanism to obtain the Certificate Authority certificate to validate the server certificate. If empty, client does not validate the server certificate.", "items": {"$ref": "ValidationCA"}, "type": "array"}, "sni": {"description": "Optional. Server Name Indication string to present to the server during TLS handshake. E.g: \"secure.example.com\".", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CloneAddressGroupItemsRequest": {"description": "Request used by the CloneAddressGroupItems method.", "id": "CloneAddressGroupItemsRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "sourceAddressGroup": {"description": "Required. Source address group to clone items from.", "type": "string"}}, "type": "object"}, "CustomInterceptProfile": {"description": "CustomInterceptProfile defines in-band integration behavior (intercept). It is used by firewall rules with an APPLY_SECURITY_PROFILE_GROUP action.", "id": "CustomInterceptProfile", "properties": {"interceptEndpointGroup": {"description": "Required. The target InterceptEndpointGroup. When a firewall rule with this security profile attached matches a packet, the packet will be intercepted to the location-local target in this group.", "type": "string"}}, "type": "object"}, "CustomMirroringProfile": {"description": "CustomMirroringProfile defines out-of-band integration behavior (mirroring). It is used by mirroring rules with a MIRROR action.", "id": "CustomMirroringProfile", "properties": {"mirroringEndpointGroup": {"description": "Required. The target MirroringEndpointGroup. When a mirroring rule with this security profile attached matches a packet, a replica will be mirrored to the location-local target in this group.", "type": "string"}}, "type": "object"}, "Destination": {"description": "Specification of traffic destination attributes.", "id": "Destination", "properties": {"hosts": {"description": "Required. List of host names to match. Matched against the \":authority\" header in http requests. At least one host should match. Each host can be an exact match, or a prefix match (example \"mydomain.*\") or a suffix match (example \"*.myorg.com\") or a presence (any) match \"*\".", "items": {"type": "string"}, "type": "array"}, "httpHeaderMatch": {"$ref": "HttpHeaderMatch", "description": "Optional. Match against key:value pair in http header. Provides a flexible match based on HTTP headers, for potentially advanced use cases. At least one header should match. Avoid using header matches to make authorization decisions unless there is a strong guarantee that requests arrive through a trusted client or proxy."}, "methods": {"description": "Optional. A list of HTTP methods to match. At least one method should match. Should not be set for gRPC services.", "items": {"type": "string"}, "type": "array"}, "ports": {"description": "Required. List of destination ports to match. At least one port should match.", "items": {"format": "uint32", "type": "integer"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FirewallEndpoint": {"description": "Message describing Endpoint object", "id": "FirewallEndpoint", "properties": {"associatedNetworks": {"deprecated": true, "description": "Output only. List of networks that are associated with this endpoint in the local zone. This is a projection of the FirewallEndpointAssociations pointing at this endpoint. A network will only appear in this list after traffic routing is fully configured. Format: projects/{project}/global/networks/{name}.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "associations": {"description": "Output only. List of FirewallEndpointAssociations that are associated to this endpoint. An association will only appear in this list after traffic routing is fully configured.", "items": {"$ref": "FirewallEndpointAssociationReference"}, "readOnly": true, "type": "array"}, "billingProjectId": {"description": "Required. Project to bill on endpoint uptime usage.", "type": "string"}, "createTime": {"description": "Output only. Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the firewall endpoint. Max length 2048 characters.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs", "type": "object"}, "name": {"description": "Immutable. Identifier. name of resource", "type": "string"}, "reconciling": {"description": "Output only. Whether reconciling is in progress, recommended per https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "satisfiesPzi": {"description": "Output only. [Output Only] Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. [Output Only] Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. Current state of the endpoint.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "INACTIVE"], "enumDescriptions": ["Not set.", "Being created.", "Processing configuration updates.", "Being deleted.", "Down or in an error state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FirewallEndpointAssociation": {"description": "Message describing Association object", "id": "FirewallEndpointAssociation", "properties": {"createTime": {"description": "Output only. Create time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}, "disabled": {"description": "Optional. Whether the association is disabled. True indicates that traffic won't be intercepted", "type": "boolean"}, "firewallEndpoint": {"description": "Required. The URL of the FirewallEndpoint that is being associated.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs", "type": "object"}, "name": {"description": "Immutable. Identifier. name of resource", "type": "string"}, "network": {"description": "Required. The URL of the network that is being associated.", "type": "string"}, "reconciling": {"description": "Output only. Whether reconciling is in progress, recommended per https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. Current state of the association.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "INACTIVE", "ORPHAN"], "enumDescriptions": ["Not set.", "Being created.", "Active and ready for traffic.", "Being deleted.", "Down or in an error state.", "The project that housed the association has been deleted."], "readOnly": true, "type": "string"}, "tlsInspectionPolicy": {"description": "Optional. The URL of the TlsInspectionPolicy that is being associated.", "type": "string"}, "updateTime": {"description": "Output only. Update time stamp", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FirewallEndpointAssociationReference": {"description": "This is a subset of the FirewallEndpointAssociation message, containing fields to be used by the consumer.", "id": "FirewallEndpointAssociationReference", "properties": {"name": {"description": "Output only. The resource name of the FirewallEndpointAssociation. Format: projects/{project}/locations/{location}/firewallEndpointAssociations/{id}", "readOnly": true, "type": "string"}, "network": {"description": "Output only. The VPC network associated. Format: projects/{project}/global/networks/{name}.", "readOnly": true, "type": "string"}}, "type": "object"}, "GatewaySecurityPolicy": {"description": "The GatewaySecurityPolicy resource contains a collection of GatewaySecurityPolicyRules and associated metadata.", "id": "GatewaySecurityPolicy", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "name": {"description": "Required. Name of the resource. Name is of the form projects/{project}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy} gateway_security_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "type": "string"}, "tlsInspectionPolicy": {"description": "Optional. Name of a TLS Inspection Policy resource that defines how TLS inspection will be performed for any rule(s) which enables it.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GatewaySecurityPolicyRule": {"description": "The GatewaySecurityPolicyRule resource is in a nested collection within a GatewaySecurityPolicy and represents a traffic matching condition and associated action to perform.", "id": "GatewaySecurityPolicyRule", "properties": {"applicationMatcher": {"description": "Optional. CEL expression for matching on L7/application level criteria.", "type": "string"}, "basicProfile": {"description": "Required. Profile which tells what the primitive action should be.", "enum": ["BASIC_PROFILE_UNSPECIFIED", "ALLOW", "DENY"], "enumDescriptions": ["If there is not a mentioned action for the target.", "Allow the matched traffic.", "Deny the matched traffic."], "type": "string"}, "createTime": {"description": "Output only. Time when the rule was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "enabled": {"description": "Required. Whether the rule is enforced.", "type": "boolean"}, "name": {"description": "Required. Immutable. Name of the resource. ame is the full resource name so projects/{project}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy}/rules/{rule} rule should match the pattern: (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "type": "string"}, "priority": {"description": "Required. Priority of the rule. Lower number corresponds to higher precedence.", "format": "int32", "type": "integer"}, "sessionMatcher": {"description": "Required. CEL expression for matching on session criteria.", "type": "string"}, "tlsInspectionEnabled": {"description": "Optional. Flag to enable TLS inspection of traffic matching on , can only be true if the parent GatewaySecurityPolicy references a TLSInspectionConfig.", "type": "boolean"}, "updateTime": {"description": "Output only. Time when the rule was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudNetworksecurityV1CertificateProvider": {"description": "Specification of certificate provider. Defines the mechanism to obtain the certificate and private key for peer to peer authentication.", "id": "GoogleCloudNetworksecurityV1CertificateProvider", "properties": {"certificateProviderInstance": {"$ref": "CertificateProviderInstance", "description": "The certificate provider instance specification that will be passed to the data plane, which will be used to load necessary credential information."}, "grpcEndpoint": {"$ref": "GoogleCloudNetworksecurityV1GrpcEndpoint", "description": "gRPC specific configuration to access the gRPC server to obtain the cert and private key."}}, "type": "object"}, "GoogleCloudNetworksecurityV1GrpcEndpoint": {"description": "Specification of the GRPC Endpoint.", "id": "GoogleCloudNetworksecurityV1GrpcEndpoint", "properties": {"targetUri": {"description": "Required. The target URI of the gRPC endpoint. Only UDS path is supported, and should start with \"unix:\".", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "GoogleIamV1AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "GoogleIamV1AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "GoogleIamV1AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "GoogleIamV1Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "GoogleIamV1Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "GoogleIamV1Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "GoogleIamV1Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "GoogleIamV1AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "GoogleIamV1Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleIamV1SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "GoogleIamV1SetIamPolicyRequest", "properties": {"policy": {"$ref": "GoogleIamV1Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "HttpHeaderMatch": {"description": "Specification of HTTP header match attributes.", "id": "HttpHeaderMatch", "properties": {"headerName": {"description": "Required. The name of the HTTP header to match. For matching against the HTTP request's authority, use a headerMatch with the header name \":authority\". For matching a request's method, use the headerName \":method\".", "type": "string"}, "regexMatch": {"description": "Required. The value of the header must match the regular expression specified in regexMatch. For regular expression grammar, please see: en.cppreference.com/w/cpp/regex/ecmascript For matching against a port specified in the HTTP request, use a headerMatch with headerName set to Host and a regular expression that satisfies the RFC2616 Host header's port specifier.", "type": "string"}}, "type": "object"}, "InterceptDeployment": {"description": "A deployment represents a zonal intercept backend ready to accept GENEVE-encapsulated traffic, e.g. a zonal instance group fronted by an internal passthrough load balancer. Deployments are always part of a global deployment group which represents a global intercept service.", "id": "InterceptDeployment", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the deployment. Used as additional context for the deployment.", "type": "string"}, "forwardingRule": {"description": "Required. Immutable. The regional forwarding rule that fronts the interceptors, for example: `projects/123456789/regions/us-central1/forwardingRules/my-rule`. See https://google.aip.dev/124.", "type": "string"}, "interceptDeploymentGroup": {"description": "Required. Immutable. The deployment group that this deployment is a part of, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "name": {"description": "Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/interceptDeployments/my-dep`. See https://google.aip.dev/122 for more details.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. linking a new association to the parent group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the deployment. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment is ready and in sync with the parent group.", "The deployment is being created.", "The deployment is being deleted.", "The deployment is out of sync with the parent group. In most cases, this is a result of a transient issue within the system (e.g. a delayed data-path config) and the system is expected to recover automatically. See the parent deployment group's state for more details.", "An attempt to delete the deployment has failed. This is a terminal state and the deployment is not expected to recover. The only permitted operation is to retry deleting the deployment."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptDeploymentGroup": {"description": "A deployment group aggregates many zonal intercept backends (deployments) into a single global intercept service. Consumers can connect this service using an endpoint group.", "id": "InterceptDeploymentGroup", "properties": {"connectedEndpointGroups": {"description": "Output only. The list of endpoint groups that are connected to this resource.", "items": {"$ref": "InterceptDeploymentGroupConnectedEndpointGroup"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the deployment group. Used as additional context for the deployment group.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "locations": {"description": "Output only. The list of locations where the deployment group is present.", "items": {"$ref": "InterceptLocation"}, "readOnly": true, "type": "array"}, "name": {"description": "Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.", "type": "string"}, "nestedDeployments": {"deprecated": true, "description": "Output only. The list of Intercept Deployments that belong to this group.", "items": {"$ref": "InterceptDeploymentGroupDeployment"}, "readOnly": true, "type": "array"}, "network": {"description": "Required. Immutable. The network that will be used for all child deployments, for example: `projects/{project}/global/networks/{network}`. See https://google.aip.dev/124.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new deployment to the group) See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the deployment group. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment group is ready.", "The deployment group is being created.", "The deployment group is being deleted."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptDeploymentGroupConnectedEndpointGroup": {"description": "An endpoint group connected to this deployment group.", "id": "InterceptDeploymentGroupConnectedEndpointGroup", "properties": {"name": {"description": "Output only. The connected endpoint group's resource name, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptDeploymentGroupDeployment": {"description": "A deployment belonging to this deployment group.", "id": "InterceptDeploymentGroupDeployment", "properties": {"name": {"description": "Output only. The name of the Intercept Deployment, in the format: `projects/{project}/locations/{location}/interceptDeployments/{intercept_deployment}`.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Most recent known state of the deployment.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment is ready and in sync with the parent group.", "The deployment is being created.", "The deployment is being deleted.", "The deployment is out of sync with the parent group. In most cases, this is a result of a transient issue within the system (e.g. a delayed data-path config) and the system is expected to recover automatically. See the parent deployment group's state for more details.", "An attempt to delete the deployment has failed. This is a terminal state and the deployment is not expected to recover. The only permitted operation is to retry deleting the deployment."], "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptEndpointGroup": {"description": "An endpoint group is a consumer frontend for a deployment group (backend). In order to configure intercept for a network, consumers must create: - An association between their network and the endpoint group. - A security profile that points to the endpoint group. - A firewall rule that references the security profile (group).", "id": "InterceptEndpointGroup", "properties": {"associations": {"description": "Output only. List of associations to this endpoint group.", "items": {"$ref": "InterceptEndpointGroupAssociationDetails"}, "readOnly": true, "type": "array"}, "connectedDeploymentGroup": {"$ref": "InterceptEndpointGroupConnectedDeploymentGroup", "description": "Output only. Details about the connected deployment group to this endpoint group.", "readOnly": true}, "createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the endpoint group. Used as additional context for the endpoint group.", "type": "string"}, "interceptDeploymentGroup": {"description": "Required. Immutable. The deployment group that this endpoint group is connected to, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "name": {"description": "Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new association to the group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the endpoint group. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CLOSED", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The endpoint group is ready and in sync with the target deployment group.", "The deployment group backing this endpoint group has been force-deleted. This endpoint group cannot be used and interception is effectively disabled.", "The endpoint group is being created.", "The endpoint group is being deleted.", "The endpoint group is out of sync with the backing deployment group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. See the associations field for details per network and location.", "An attempt to delete the endpoint group has failed. This is a terminal state and the endpoint group is not expected to recover. The only permitted operation is to retry deleting the endpoint group."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptEndpointGroupAssociation": {"description": "An endpoint group association represents a link between a network and an endpoint group in the organization. Creating an association creates the networking infrastructure linking the network to the endpoint group, but does not enable intercept by itself. To enable intercept, the user must also create a network firewall policy containing intercept rules and associate it with the network.", "id": "InterceptEndpointGroupAssociation", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "interceptEndpointGroup": {"description": "Required. Immutable. The endpoint group that this association is connected to, for example: `projects/123456789/locations/global/interceptEndpointGroups/my-eg`. See https://google.aip.dev/124.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "locations": {"description": "Output only. The list of locations where the association is configured. This information is retrieved from the linked endpoint group.", "items": {"$ref": "InterceptLocation"}, "readOnly": true, "type": "array"}, "locationsDetails": {"deprecated": true, "description": "Output only. The list of locations where the association is present. This information is retrieved from the linked endpoint group, and not configured as part of the association itself.", "items": {"$ref": "InterceptEndpointGroupAssociationLocationDetails"}, "readOnly": true, "type": "array"}, "name": {"description": "Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.", "type": "string"}, "network": {"description": "Required. Immutable. The VPC network that is associated. for example: `projects/123456789/global/networks/my-network`. See https://google.aip.dev/124.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. adding a new location to the target deployment group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. Current state of the endpoint group association.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "CLOSED", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is being created.", "The association is being deleted.", "The association is disabled due to a breaking change in another resource.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. Check the `locations_details` field for more details.", "An attempt to delete the association has failed. This is a terminal state and the association is not expected to be usable as some of its resources have been deleted. The only permitted operation is to retry deleting the association."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptEndpointGroupAssociationDetails": {"description": "The endpoint group's view of a connected association.", "id": "InterceptEndpointGroupAssociationDetails", "properties": {"name": {"description": "Output only. The connected association's resource name, for example: `projects/123456789/locations/global/interceptEndpointGroupAssociations/my-ega`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}, "network": {"description": "Output only. The associated network, for example: projects/123456789/global/networks/my-network. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Most recent known state of the association.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "CLOSED", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is being created.", "The association is being deleted.", "The association is disabled due to a breaking change in another resource.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. Check the `locations_details` field for more details.", "An attempt to delete the association has failed. This is a terminal state and the association is not expected to be usable as some of its resources have been deleted. The only permitted operation is to retry deleting the association."], "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptEndpointGroupAssociationLocationDetails": {"description": "Contains details about the state of an association in a specific cloud location.", "id": "InterceptEndpointGroupAssociationLocationDetails", "properties": {"location": {"description": "Output only. The cloud location, e.g. \"us-central1-a\" or \"asia-south1\".", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the association in this location.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "OUT_OF_SYNC"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically."], "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptEndpointGroupConnectedDeploymentGroup": {"description": "The endpoint group's view of a connected deployment group.", "id": "InterceptEndpointGroupConnectedDeploymentGroup", "properties": {"locations": {"description": "Output only. The list of locations where the deployment group is present.", "items": {"$ref": "InterceptLocation"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. The connected deployment group's resource name, for example: `projects/123456789/locations/global/interceptDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}}, "type": "object"}, "InterceptLocation": {"description": "Details about intercept in a specific cloud location.", "id": "InterceptLocation", "properties": {"location": {"description": "Output only. The cloud location, e.g. \"us-central1-a\" or \"asia-south1\".", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the association in this location.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "OUT_OF_SYNC"], "enumDescriptions": ["State not set (this is not a valid state).", "The resource is ready and in sync in the location.", "The resource is out of sync in the location. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically."], "readOnly": true, "type": "string"}}, "type": "object"}, "ListAddressGroupReferencesResponse": {"description": "Response of the ListAddressGroupReferences method.", "id": "ListAddressGroupReferencesResponse", "properties": {"addressGroupReferences": {"description": "A list of references that matches the specified filter in the request.", "items": {"$ref": "ListAddressGroupReferencesResponseAddressGroupReference"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}}, "type": "object"}, "ListAddressGroupReferencesResponseAddressGroupReference": {"description": "The Reference of AddressGroup.", "id": "ListAddressGroupReferencesResponseAddressGroupReference", "properties": {"firewallPolicy": {"description": "FirewallPolicy that is using the Address Group.", "type": "string"}, "rulePriority": {"description": "Rule priority of the FirewallPolicy that is using the Address Group.", "format": "int32", "type": "integer"}, "securityPolicy": {"description": "Cloud Armor SecurityPolicy that is using the Address Group.", "type": "string"}}, "type": "object"}, "ListAddressGroupsResponse": {"description": "Response returned by the ListAddressGroups method.", "id": "ListAddressGroupsResponse", "properties": {"addressGroups": {"description": "List of AddressGroups resources.", "items": {"$ref": "AddressGroup"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListAuthorizationPoliciesResponse": {"description": "Response returned by the ListAuthorizationPolicies method.", "id": "ListAuthorizationPoliciesResponse", "properties": {"authorizationPolicies": {"description": "List of AuthorizationPolicies resources.", "items": {"$ref": "AuthorizationPolicy"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}}, "type": "object"}, "ListAuthzPoliciesResponse": {"description": "Message for response to listing `AuthzPolicy` resources.", "id": "ListAuthzPoliciesResponse", "properties": {"authzPolicies": {"description": "The list of `AuthzPolicy` resources.", "items": {"$ref": "AuthzPolicy"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results that the server returns.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBackendAuthenticationConfigsResponse": {"description": "Response returned by the ListBackendAuthenticationConfigs method.", "id": "ListBackendAuthenticationConfigsResponse", "properties": {"backendAuthenticationConfigs": {"description": "List of BackendAuthenticationConfig resources.", "items": {"$ref": "BackendAuthenticationConfig"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListClientTlsPoliciesResponse": {"description": "Response returned by the ListClientTlsPolicies method.", "id": "ListClientTlsPoliciesResponse", "properties": {"clientTlsPolicies": {"description": "List of ClientTlsPolicy resources.", "items": {"$ref": "ClientTlsPolicy"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}}, "type": "object"}, "ListFirewallEndpointAssociationsResponse": {"description": "Message for response to listing Associations", "id": "ListFirewallEndpointAssociationsResponse", "properties": {"firewallEndpointAssociations": {"description": "The list of Association", "items": {"$ref": "FirewallEndpointAssociation"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListFirewallEndpointsResponse": {"description": "Message for response to listing Endpoints", "id": "ListFirewallEndpointsResponse", "properties": {"firewallEndpoints": {"description": "The list of Endpoint", "items": {"$ref": "FirewallEndpoint"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGatewaySecurityPoliciesResponse": {"description": "Response returned by the ListGatewaySecurityPolicies method.", "id": "ListGatewaySecurityPoliciesResponse", "properties": {"gatewaySecurityPolicies": {"description": "List of GatewaySecurityPolicies resources.", "items": {"$ref": "GatewaySecurityPolicy"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGatewaySecurityPolicyRulesResponse": {"description": "Response returned by the ListGatewaySecurityPolicyRules method.", "id": "ListGatewaySecurityPolicyRulesResponse", "properties": {"gatewaySecurityPolicyRules": {"description": "List of GatewaySecurityPolicyRule resources.", "items": {"$ref": "GatewaySecurityPolicyRule"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInterceptDeploymentGroupsResponse": {"description": "Response message for ListInterceptDeploymentGroups.", "id": "ListInterceptDeploymentGroupsResponse", "properties": {"interceptDeploymentGroups": {"description": "The deployment groups from the specified parent.", "items": {"$ref": "InterceptDeploymentGroup"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListInterceptDeploymentsResponse": {"description": "Response message for ListInterceptDeployments.", "id": "ListInterceptDeploymentsResponse", "properties": {"interceptDeployments": {"description": "The deployments from the specified parent.", "items": {"$ref": "InterceptDeployment"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInterceptEndpointGroupAssociationsResponse": {"description": "Response message for ListInterceptEndpointGroupAssociations.", "id": "ListInterceptEndpointGroupAssociationsResponse", "properties": {"interceptEndpointGroupAssociations": {"description": "The associations from the specified parent.", "items": {"$ref": "InterceptEndpointGroupAssociation"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListInterceptEndpointGroupsResponse": {"description": "Response message for ListInterceptEndpointGroups.", "id": "ListInterceptEndpointGroupsResponse", "properties": {"interceptEndpointGroups": {"description": "The endpoint groups from the specified parent.", "items": {"$ref": "InterceptEndpointGroup"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMirroringDeploymentGroupsResponse": {"description": "Response message for ListMirroringDeploymentGroups.", "id": "ListMirroringDeploymentGroupsResponse", "properties": {"mirroringDeploymentGroups": {"description": "The deployment groups from the specified parent.", "items": {"$ref": "MirroringDeploymentGroup"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListMirroringDeploymentsResponse": {"description": "Response message for ListMirroringDeployments.", "id": "ListMirroringDeploymentsResponse", "properties": {"mirroringDeployments": {"description": "The deployments from the specified parent.", "items": {"$ref": "MirroringDeployment"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMirroringEndpointGroupAssociationsResponse": {"description": "Response message for ListMirroringEndpointGroupAssociations.", "id": "ListMirroringEndpointGroupAssociationsResponse", "properties": {"mirroringEndpointGroupAssociations": {"description": "The associations from the specified parent.", "items": {"$ref": "MirroringEndpointGroupAssociation"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListMirroringEndpointGroupsResponse": {"description": "Response message for ListMirroringEndpointGroups.", "id": "ListMirroringEndpointGroupsResponse", "properties": {"mirroringEndpointGroups": {"description": "The endpoint groups from the specified parent.", "items": {"$ref": "MirroringEndpointGroup"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. See https://google.aip.dev/158 for more details.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListSecurityProfileGroupsResponse": {"description": "Response returned by the ListSecurityProfileGroups method.", "id": "ListSecurityProfileGroupsResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "securityProfileGroups": {"description": "List of SecurityProfileGroups resources.", "items": {"$ref": "SecurityProfileGroup"}, "type": "array"}}, "type": "object"}, "ListSecurityProfilesResponse": {"description": "Response returned by the ListSecurityProfiles method.", "id": "ListSecurityProfilesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "securityProfiles": {"description": "List of SecurityProfile resources.", "items": {"$ref": "SecurityProfile"}, "type": "array"}}, "type": "object"}, "ListServerTlsPoliciesResponse": {"description": "Response returned by the ListServerTlsPolicies method.", "id": "ListServerTlsPoliciesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "serverTlsPolicies": {"description": "List of ServerTlsPolicy resources.", "items": {"$ref": "ServerTlsPolicy"}, "type": "array"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into `return_partial_success` and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTlsInspectionPoliciesResponse": {"description": "Response returned by the ListTlsInspectionPolicies method.", "id": "ListTlsInspectionPoliciesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then 'next_page_token' is included. To get the next set of results, call this method again using the value of 'next_page_token' as 'page_token'.", "type": "string"}, "tlsInspectionPolicies": {"description": "List of TlsInspectionPolicies resources.", "items": {"$ref": "TlsInspectionPolicy"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListUrlListsResponse": {"description": "Response returned by the ListUrlLists method.", "id": "ListUrlListsResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "urlLists": {"description": "List of UrlList resources.", "items": {"$ref": "UrlList"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "MTLSPolicy": {"description": "Specification of the MTLSPolicy.", "id": "MTLSPolicy", "properties": {"clientValidationCa": {"description": "Required if the policy is to be used with Traffic Director. For Application Load Balancers it must be empty. Defines the mechanism to obtain the Certificate Authority certificate to validate the client certificate.", "items": {"$ref": "ValidationCA"}, "type": "array"}, "clientValidationMode": {"description": "When the client presents an invalid certificate or no certificate to the load balancer, the `client_validation_mode` specifies how the client connection is handled. Required if the policy is to be used with the Application Load Balancers. For Traffic Director it must be empty.", "enum": ["CLIENT_VALIDATION_MODE_UNSPECIFIED", "ALLOW_INVALID_OR_MISSING_CLIENT_CERT", "REJECT_INVALID"], "enumDescriptions": ["Not allowed.", "Allow connection even if certificate chain validation of the client certificate failed or no client certificate was presented. The proof of possession of the private key is always checked if client certificate was presented. This mode requires the backend to implement processing of data extracted from a client certificate to authenticate the peer, or to reject connections if the client certificate fingerprint is missing.", "Require a client certificate and allow connection to the backend only if validation of the client certificate passed. If set, requires a reference to non-empty TrustConfig specified in `client_validation_trust_config`."], "type": "string"}, "clientValidationTrustConfig": {"description": "Reference to the TrustConfig from certificatemanager.googleapis.com namespace. If specified, the chain validation will be performed against certificates configured in the given TrustConfig. Allowed only if the policy is to be used with Application Load Balancers.", "type": "string"}}, "type": "object"}, "MirroringDeployment": {"description": "A deployment represents a zonal mirroring backend ready to accept GENEVE-encapsulated replica traffic, e.g. a zonal instance group fronted by an internal passthrough load balancer. Deployments are always part of a global deployment group which represents a global mirroring service.", "id": "MirroringDeployment", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the deployment. Used as additional context for the deployment.", "type": "string"}, "forwardingRule": {"description": "Required. Immutable. The regional forwarding rule that fronts the mirroring collectors, for example: `projects/123456789/regions/us-central1/forwardingRules/my-rule`. See https://google.aip.dev/124.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "mirroringDeploymentGroup": {"description": "Required. Immutable. The deployment group that this deployment is a part of, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "type": "string"}, "name": {"description": "Immutable. Identifier. The resource name of this deployment, for example: `projects/123456789/locations/us-central1-a/mirroringDeployments/my-dep`. See https://google.aip.dev/122 for more details.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. linking a new association to the parent group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the deployment. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment is ready and in sync with the parent group.", "The deployment is being created.", "The deployment is being deleted.", "The deployment is out of sync with the parent group. In most cases, this is a result of a transient issue within the system (e.g. a delayed data-path config) and the system is expected to recover automatically. See the parent deployment group's state for more details.", "An attempt to delete the deployment has failed. This is a terminal state and the deployment is not expected to recover. The only permitted operation is to retry deleting the deployment."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringDeploymentGroup": {"description": "A deployment group aggregates many zonal mirroring backends (deployments) into a single global mirroring service. Consumers can connect this service using an endpoint group.", "id": "MirroringDeploymentGroup", "properties": {"connectedEndpointGroups": {"description": "Output only. The list of endpoint groups that are connected to this resource.", "items": {"$ref": "MirroringDeploymentGroupConnectedEndpointGroup"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the deployment group. Used as additional context for the deployment group.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "locations": {"description": "Output only. The list of locations where the deployment group is present.", "items": {"$ref": "MirroringLocation"}, "readOnly": true, "type": "array"}, "name": {"description": "Immutable. Identifier. The resource name of this deployment group, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/122 for more details.", "type": "string"}, "nestedDeployments": {"deprecated": true, "description": "Output only. The list of Mirroring Deployments that belong to this group.", "items": {"$ref": "MirroringDeploymentGroupDeployment"}, "readOnly": true, "type": "array"}, "network": {"description": "Required. Immutable. The network that will be used for all child deployments, for example: `projects/{project}/global/networks/{network}`. See https://google.aip.dev/124.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new deployment to the group) See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the deployment group. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment group is ready.", "The deployment group is being created.", "The deployment group is being deleted."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringDeploymentGroupConnectedEndpointGroup": {"description": "An endpoint group connected to this deployment group.", "id": "MirroringDeploymentGroupConnectedEndpointGroup", "properties": {"name": {"description": "Output only. The connected endpoint group's resource name, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringDeploymentGroupDeployment": {"description": "A deployment belonging to this deployment group.", "id": "MirroringDeploymentGroupDeployment", "properties": {"name": {"description": "Output only. The name of the Mirroring Deployment, in the format: `projects/{project}/locations/{location}/mirroringDeployments/{mirroring_deployment}`.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Most recent known state of the deployment.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The deployment is ready and in sync with the parent group.", "The deployment is being created.", "The deployment is being deleted.", "The deployment is out of sync with the parent group. In most cases, this is a result of a transient issue within the system (e.g. a delayed data-path config) and the system is expected to recover automatically. See the parent deployment group's state for more details.", "An attempt to delete the deployment has failed. This is a terminal state and the deployment is not expected to recover. The only permitted operation is to retry deleting the deployment."], "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringEndpointGroup": {"description": "An endpoint group is a consumer frontend for a deployment group (backend). In order to configure mirroring for a network, consumers must create: - An association between their network and the endpoint group. - A security profile that points to the endpoint group. - A mirroring rule that references the security profile (group).", "id": "MirroringEndpointGroup", "properties": {"associations": {"description": "Output only. List of associations to this endpoint group.", "items": {"$ref": "MirroringEndpointGroupAssociationDetails"}, "readOnly": true, "type": "array"}, "connectedDeploymentGroups": {"description": "Output only. List of details about the connected deployment groups to this endpoint group.", "items": {"$ref": "MirroringEndpointGroupConnectedDeploymentGroup"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-provided description of the endpoint group. Used as additional context for the endpoint group.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "mirroringDeploymentGroup": {"description": "Immutable. The deployment group that this DIRECT endpoint group is connected to, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "type": "string"}, "name": {"description": "Immutable. Identifier. The resource name of this endpoint group, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/122 for more details.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This is part of the normal operation (e.g. adding a new association to the group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the endpoint group. See https://google.aip.dev/216.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CLOSED", "CREATING", "DELETING", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["State not set (this is not a valid state).", "The endpoint group is ready and in sync with the target deployment group.", "The deployment group backing this endpoint group has been force-deleted. This endpoint group cannot be used and mirroring is effectively disabled.", "The endpoint group is being created.", "The endpoint group is being deleted.", "The endpoint group is out of sync with the backing deployment group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. See the associations field for details per network and location.", "An attempt to delete the endpoint group has failed. This is a terminal state and the endpoint group is not expected to recover. The only permitted operation is to retry deleting the endpoint group."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringEndpointGroupAssociation": {"description": "An endpoint group association represents a link between a network and an endpoint group in the organization. Creating an association creates the networking infrastructure linking the network to the endpoint group, but does not enable mirroring by itself. To enable mirroring, the user must also create a network firewall policy containing mirroring rules and associate it with the network.", "id": "MirroringEndpointGroupAssociation", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are key/value pairs that help to organize and filter resources.", "type": "object"}, "locations": {"description": "Output only. The list of locations where the association is configured. This information is retrieved from the linked endpoint group.", "items": {"$ref": "MirroringLocation"}, "readOnly": true, "type": "array"}, "locationsDetails": {"deprecated": true, "description": "Output only. The list of locations where the association is present. This information is retrieved from the linked endpoint group, and not configured as part of the association itself.", "items": {"$ref": "MirroringEndpointGroupAssociationLocationDetails"}, "readOnly": true, "type": "array"}, "mirroringEndpointGroup": {"description": "Immutable. The endpoint group that this association is connected to, for example: `projects/123456789/locations/global/mirroringEndpointGroups/my-eg`. See https://google.aip.dev/124.", "type": "string"}, "name": {"description": "Immutable. Identifier. The resource name of this endpoint group association, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-eg-association`. See https://google.aip.dev/122 for more details.", "type": "string"}, "network": {"description": "Immutable. The VPC network that is associated. for example: `projects/123456789/global/networks/my-network`. See https://google.aip.dev/124.", "type": "string"}, "reconciling": {"description": "Output only. The current state of the resource does not match the user's intended state, and the system is working to reconcile them. This part of the normal operation (e.g. adding a new location to the target deployment group). See https://google.aip.dev/128.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. Current state of the endpoint group association.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "CLOSED", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is being created.", "The association is being deleted.", "The association is disabled due to a breaking change in another resource.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. Check the `locations_details` field for more details.", "An attempt to delete the association has failed. This is a terminal state and the association is not expected to be usable as some of its resources have been deleted. The only permitted operation is to retry deleting the association."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was most recently updated. See https://google.aip.dev/148#timestamps.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringEndpointGroupAssociationDetails": {"description": "The endpoint group's view of a connected association.", "id": "MirroringEndpointGroupAssociationDetails", "properties": {"name": {"description": "Output only. The connected association's resource name, for example: `projects/123456789/locations/global/mirroringEndpointGroupAssociations/my-ega`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}, "network": {"description": "Output only. The associated network, for example: projects/123456789/global/networks/my-network. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Most recent known state of the association.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "CREATING", "DELETING", "CLOSED", "OUT_OF_SYNC", "DELETE_FAILED"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is being created.", "The association is being deleted.", "The association is disabled due to a breaking change in another resource.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically. Check the `locations_details` field for more details.", "An attempt to delete the association has failed. This is a terminal state and the association is not expected to be usable as some of its resources have been deleted. The only permitted operation is to retry deleting the association."], "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringEndpointGroupAssociationLocationDetails": {"description": "Contains details about the state of an association in a specific cloud location.", "id": "MirroringEndpointGroupAssociationLocationDetails", "properties": {"location": {"description": "Output only. The cloud location, e.g. \"us-central1-a\" or \"asia-south1\".", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the association in this location.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "OUT_OF_SYNC"], "enumDescriptions": ["Not set.", "The association is ready and in sync with the linked endpoint group.", "The association is out of sync with the linked endpoint group. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically."], "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringEndpointGroupConnectedDeploymentGroup": {"description": "The endpoint group's view of a connected deployment group.", "id": "MirroringEndpointGroupConnectedDeploymentGroup", "properties": {"locations": {"description": "Output only. The list of locations where the deployment group is present.", "items": {"$ref": "MirroringLocation"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. The connected deployment group's resource name, for example: `projects/123456789/locations/global/mirroringDeploymentGroups/my-dg`. See https://google.aip.dev/124.", "readOnly": true, "type": "string"}}, "type": "object"}, "MirroringLocation": {"description": "Details about mirroring in a specific cloud location.", "id": "MirroringLocation", "properties": {"location": {"description": "Output only. The cloud location, e.g. \"us-central1-a\" or \"asia-south1\".", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the association in this location.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "OUT_OF_SYNC"], "enumDescriptions": ["State not set (this is not a valid state).", "The resource is ready and in sync in the location.", "The resource is out of sync in the location. In most cases, this is a result of a transient issue within the system (e.g. an inaccessible location) and the system is expected to recover automatically."], "readOnly": true, "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "RemoveAddressGroupItemsRequest": {"description": "Request used by the RemoveAddressGroupItems method.", "id": "RemoveAddressGroupItemsRequest", "properties": {"items": {"description": "Required. List of items to remove.", "items": {"type": "string"}, "type": "array"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "Rule": {"description": "Specification of rules.", "id": "Rule", "properties": {"destinations": {"description": "Optional. List of attributes for the traffic destination. All of the destinations must match. A destination is a match if a request matches all the specified hosts, ports, methods and headers. If not set, the action specified in the 'action' field will be applied without any rule checks for the destination.", "items": {"$ref": "Destination"}, "type": "array"}, "sources": {"description": "Optional. List of attributes for the traffic source. All of the sources must match. A source is a match if both principals and ip_blocks match. If not set, the action specified in the 'action' field will be applied without any rule checks for the source.", "items": {"$ref": "Source"}, "type": "array"}}, "type": "object"}, "SecurityProfile": {"description": "SecurityProfile is a resource that defines the behavior for one of many ProfileTypes.", "id": "SecurityProfile", "properties": {"createTime": {"description": "Output only. Resource creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customInterceptProfile": {"$ref": "CustomInterceptProfile", "description": "The custom TPPI configuration for the SecurityProfile."}, "customMirroringProfile": {"$ref": "CustomMirroringProfile", "description": "The custom Packet Mirroring v2 configuration for the SecurityProfile."}, "description": {"description": "Optional. An optional description of the profile. Max length 512 characters.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs.", "type": "object"}, "name": {"description": "Immutable. Identifier. Name of the SecurityProfile resource. It matches pattern `projects|organizations/*/locations/{location}/securityProfiles/{security_profile}`.", "type": "string"}, "threatPreventionProfile": {"$ref": "ThreatPreventionProfile", "description": "The threat prevention configuration for the SecurityProfile."}, "type": {"description": "Immutable. The single ProfileType that the SecurityProfile resource configures.", "enum": ["PROFILE_TYPE_UNSPECIFIED", "THREAT_PREVENTION", "CUSTOM_MIRRORING", "CUSTOM_INTERCEPT"], "enumDescriptions": ["Profile type not specified.", "Profile type for threat prevention.", "Profile type for packet mirroring v2", "Profile type for TPPI."], "type": "string"}, "updateTime": {"description": "Output only. Last resource update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SecurityProfileGroup": {"description": "SecurityProfileGroup is a resource that defines the behavior for various ProfileTypes.", "id": "SecurityProfileGroup", "properties": {"createTime": {"description": "Output only. Resource creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customInterceptProfile": {"description": "Optional. Reference to a SecurityProfile with the CustomIntercept configuration.", "type": "string"}, "customMirroringProfile": {"description": "Optional. Reference to a SecurityProfile with the CustomMirroring configuration.", "type": "string"}, "dataPathId": {"description": "Output only. Identifier used by the data-path. Unique within {container, location}.", "format": "uint64", "readOnly": true, "type": "string"}, "description": {"description": "Optional. An optional description of the profile group. Max length 2048 characters.", "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs.", "type": "object"}, "name": {"description": "Immutable. Identifier. Name of the SecurityProfileGroup resource. It matches pattern `projects|organizations/*/locations/{location}/securityProfileGroups/{security_profile_group}`.", "type": "string"}, "threatPreventionProfile": {"description": "Optional. Reference to a SecurityProfile with the ThreatPrevention configuration.", "type": "string"}, "updateTime": {"description": "Output only. Last resource update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServerTlsPolicy": {"description": "ServerTlsPolicy is a resource that specifies how a server should authenticate incoming requests. This resource itself does not affect configuration unless it is attached to a target HTTPS proxy or endpoint config selector resource. ServerTlsPolicy in the form accepted by Application Load Balancers can be attached only to TargetHttpsProxy with an `EXTERNAL`, `EXTERNAL_MANAGED` or `INTERNAL_MANAGED` load balancing scheme. Traffic Director compatible ServerTlsPolicies can be attached to EndpointPolicy and TargetHttpsProxy with Traffic Director `INTERNAL_SELF_MANAGED` load balancing scheme.", "id": "ServerTlsPolicy", "properties": {"allowOpen": {"description": "This field applies only for Traffic Director policies. It is must be set to false for Application Load Balancer policies. Determines if server allows plaintext connections. If set to true, server allows plain text connections. By default, it is set to false. This setting is not exclusive of other encryption modes. For example, if `allow_open` and `mtls_policy` are set, server allows both plain text and mTLS connections. See documentation of other encryption modes to confirm compatibility. Consider using it if you wish to upgrade in place your deployment to TLS while having mixed TLS and non-TLS traffic reaching port :80.", "type": "boolean"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Free-text description of the resource.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Set of label tags associated with the resource.", "type": "object"}, "mtlsPolicy": {"$ref": "MTLSPolicy", "description": "This field is required if the policy is used with Application Load Balancers. This field can be empty for Traffic Director. Defines a mechanism to provision peer validation certificates for peer to peer authentication (Mutual TLS - mTLS). If not specified, client certificate will not be requested. The connection is treated as TLS and not mTLS. If `allow_open` and `mtls_policy` are set, server allows both plain text and mTLS connections."}, "name": {"description": "Required. Name of the ServerTlsPolicy resource. It matches the pattern `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`", "type": "string"}, "serverCertificate": {"$ref": "GoogleCloudNetworksecurityV1CertificateProvider", "description": "Optional if policy is to be used with Traffic Director. For Application Load Balancers must be empty. Defines a mechanism to provision server identity (public and private keys). Cannot be combined with `allow_open` as a permissive mode that allows both plain text and TLS is not supported."}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SeverityOverride": {"description": "Defines what action to take for a specific severity match.", "id": "SeverityOverride", "properties": {"action": {"description": "Required. Threat action override.", "enum": ["THREAT_ACTION_UNSPECIFIED", "DEFAULT_ACTION", "ALLOW", "ALERT", "DENY"], "enumDescriptions": ["Threat action not specified.", "The default action (as specified by the vendor) is taken.", "The packet matching this rule will be allowed to transmit.", "The packet matching this rule will be allowed to transmit, but a threat_log entry will be sent to the consumer project.", "The packet matching this rule will be dropped, and a threat_log entry will be sent to the consumer project."], "type": "string"}, "severity": {"description": "Required. Severity level to match.", "enum": ["SEVERITY_UNSPECIFIED", "INFORMATIONAL", "LOW", "MEDIUM", "HIGH", "CRITICAL"], "enumDescriptions": ["Severity level not specified.", "Suspicious events that do not pose an immediate threat, but that are reported to call attention to deeper problems that could possibly exist.", "Warning-level threats that have very little impact on an organization's infrastructure. They usually require local or physical system access and may often result in victim privacy issues and information leakage.", "Minor threats in which impact is minimized, that do not compromise the target or exploits that require an attacker to reside on the same local network as the victim, affect only non-standard configurations or obscure applications, or provide very limited access.", "Threats that have the ability to become critical but have mitigating factors; for example, they may be difficult to exploit, do not result in elevated privileges, or do not have a large victim pool.", "Serious threats, such as those that affect default installations of widely deployed software, result in root compromise of servers, and the exploit code is widely available to attackers. The attacker usually does not need any special authentication credentials or knowledge about the individual victims and the target does not need to be manipulated into performing any special functions."], "type": "string"}}, "type": "object"}, "Source": {"description": "Specification of traffic source attributes.", "id": "Source", "properties": {"ipBlocks": {"description": "Optional. List of CIDR ranges to match based on source IP address. At least one IP block should match. Single IP (e.g., \"*******\") and CIDR (e.g., \"*******/24\") are supported. Authorization based on source IP alone should be avoided. The IP addresses of any load balancers or proxies should be considered untrusted.", "items": {"type": "string"}, "type": "array"}, "principals": {"description": "Optional. List of peer identities to match for authorization. At least one principal should match. Each peer can be an exact match, or a prefix match (example, \"namespace/*\") or a suffix match (example, \"*/service-account\") or a presence match \"*\". Authorization based on the principal name without certificate validation (configured by ServerTlsPolicy resource) is considered insecure.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "ThreatOverride": {"description": "Defines what action to take for a specific threat_id match.", "id": "ThreatOverride", "properties": {"action": {"description": "Required. Threat action override. For some threat types, only a subset of actions applies.", "enum": ["THREAT_ACTION_UNSPECIFIED", "DEFAULT_ACTION", "ALLOW", "ALERT", "DENY"], "enumDescriptions": ["Threat action not specified.", "The default action (as specified by the vendor) is taken.", "The packet matching this rule will be allowed to transmit.", "The packet matching this rule will be allowed to transmit, but a threat_log entry will be sent to the consumer project.", "The packet matching this rule will be dropped, and a threat_log entry will be sent to the consumer project."], "type": "string"}, "threatId": {"description": "Required. Vendor-specific ID of a threat to override.", "type": "string"}, "type": {"description": "Output only. Type of the threat (read only).", "enum": ["THREAT_TYPE_UNSPECIFIED", "UNKNOWN", "VULNERABILITY", "ANTIVIRUS", "SPYWARE", "DNS"], "enumDescriptions": ["Type of threat not specified.", "Type of threat is not derivable from threat ID. An override will be created for all types. Firewall will ignore overridden signature ID's that don't exist in the specific type.", "Threats related to system flaws that an attacker might otherwise attempt to exploit.", "Threats related to viruses and malware found in executables and file types.", "Threats related to command-and-control (C2) activity, where spyware on an infected client is collecting data without the user's consent and/or communicating with a remote attacker.", "Threats related to DNS."], "readOnly": true, "type": "string"}}, "type": "object"}, "ThreatPreventionProfile": {"description": "ThreatPreventionProfile defines an action for specific threat signatures or severity levels.", "id": "ThreatPreventionProfile", "properties": {"antivirusOverrides": {"description": "Optional. Configuration for overriding antivirus actions per protocol.", "items": {"$ref": "AntivirusOverride"}, "type": "array"}, "severityOverrides": {"description": "Optional. Configuration for overriding threats actions by severity match.", "items": {"$ref": "SeverityOverride"}, "type": "array"}, "threatOverrides": {"description": "Optional. Configuration for overriding threats actions by threat_id match. If a threat is matched both by configuration provided in severity_overrides and threat_overrides, the threat_overrides action is applied.", "items": {"$ref": "ThreatOverride"}, "type": "array"}}, "type": "object"}, "TlsInspectionPolicy": {"description": "The TlsInspectionPolicy resource contains references to CA pools in Certificate Authority Service and associated metadata.", "id": "TlsInspectionPolicy", "properties": {"caPool": {"description": "Required. A CA pool resource used to issue interception certificates. The CA pool string has a relative resource path following the form \"projects/{project}/locations/{location}/caPools/{ca_pool}\".", "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customTlsFeatures": {"description": "Optional. List of custom TLS cipher suites selected. This field is valid only if the selected tls_feature_profile is CUSTOM. The compute.SslPoliciesService.ListAvailableFeatures method returns the set of features that can be specified in this list. Note that Secure Web Proxy does not yet honor this field.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "excludePublicCaSet": {"description": "Optional. If FALSE (the default), use our default set of public CAs in addition to any CAs specified in trust_config. These public CAs are currently based on the Mozilla Root Program and are subject to change over time. If TRUE, do not accept our default set of public CAs. Only CAs specified in trust_config will be accepted. This defaults to FALSE (use public CAs in addition to trust_config) for backwards compatibility, but trusting public root CAs is *not recommended* unless the traffic in question is outbound to public web servers. When possible, prefer setting this to \"false\" and explicitly specifying trusted CAs and certificates in a TrustConfig. Note that Secure Web Proxy does not yet honor this field.", "type": "boolean"}, "minTlsVersion": {"description": "Optional. Minimum TLS version that the firewall should use when negotiating connections with both clients and servers. If this is not set, then the default value is to allow the broadest set of clients and servers (TLS 1.0 or higher). Setting this to more restrictive values may improve security, but may also prevent the firewall from connecting to some clients or servers. Note that Secure Web Proxy does not yet honor this field.", "enum": ["TLS_VERSION_UNSPECIFIED", "TLS_1_0", "TLS_1_1", "TLS_1_2", "TLS_1_3"], "enumDescriptions": ["Indicates no TLS version was specified.", "TLS 1.0", "TLS 1.1", "TLS 1.2", "TLS 1.3"], "type": "string"}, "name": {"description": "Required. Name of the resource. Name is of the form projects/{project}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy} tls_inspection_policy should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "type": "string"}, "tlsFeatureProfile": {"description": "Optional. The selected Profile. If this is not set, then the default value is to allow the broadest set of clients and servers (\"PROFILE_COMPATIBLE\"). Setting this to more restrictive values may improve security, but may also prevent the TLS inspection proxy from connecting to some clients or servers. Note that Secure Web Proxy does not yet honor this field.", "enum": ["PROFILE_UNSPECIFIED", "PROFILE_COMPATIBLE", "PROFILE_MODERN", "PROFILE_RESTRICTED", "PROFILE_CUSTOM"], "enumDescriptions": ["Indicates no profile was specified.", "Compatible profile. Allows the broadest set of clients, even those which support only out-of-date SSL features to negotiate with the TLS inspection proxy.", "Modern profile. Supports a wide set of SSL features, allowing modern clients to negotiate SSL with the TLS inspection proxy.", "Restricted profile. Supports a reduced set of SSL features, intended to meet stricter compliance requirements.", "Custom profile. Allow only the set of allowed SSL features specified in the custom_features field of SslPolicy."], "type": "string"}, "trustConfig": {"description": "Optional. A TrustConfig resource used when making a connection to the TLS server. This is a relative resource path following the form \"projects/{project}/locations/{location}/trustConfigs/{trust_config}\". This is necessary to intercept TLS connections to servers with certificates signed by a private CA or self-signed certificates. Note that Secure Web Proxy does not yet honor this field.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "UrlList": {"description": "UrlList proto helps users to set reusable, independently manageable lists of hosts, host patterns, URLs, URL patterns.", "id": "UrlList", "properties": {"createTime": {"description": "Output only. Time when the security policy was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free-text description of the resource.", "type": "string"}, "name": {"description": "Required. Name of the resource provided by the user. Name is of the form projects/{project}/locations/{location}/urlLists/{url_list} url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).", "type": "string"}, "updateTime": {"description": "Output only. Time when the security policy was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "values": {"description": "Required. FQDNs and URLs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ValidationCA": {"description": "Specification of ValidationCA. Defines the mechanism to obtain the Certificate Authority certificate to validate the peer certificate.", "id": "ValidationCA", "properties": {"certificateProviderInstance": {"$ref": "CertificateProviderInstance", "description": "The certificate provider instance specification that will be passed to the data plane, which will be used to load necessary credential information."}, "grpcEndpoint": {"$ref": "GoogleCloudNetworksecurityV1GrpcEndpoint", "description": "gRPC specific configuration to access the gRPC server to obtain the CA certificate."}}, "type": "object"}}, "servicePath": "", "title": "Network Security API", "version": "v1", "version_module": true}