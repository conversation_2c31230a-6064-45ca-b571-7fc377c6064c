from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
import json
import uuid

from .models import Payment, PaymentMethod, PaymentHistory, Refund, ConsultationPricing
from consultations.models import Consultation
from users.models import CustomUser


@login_required
def initiate_payment(request, consultation_id):
    """Initiate payment for a consultation"""
    consultation = get_object_or_404(Consultation, id=consultation_id, user=request.user)

    if consultation.type == 'free':
        messages.error(request, "This consultation is free and doesn't require payment.")
        return redirect('consultation_detail', consultation_id=consultation.id)

    # Check if payment already exists
    if hasattr(consultation, 'payment'):
        if consultation.payment.status == 'completed':
            messages.info(request, "Payment for this consultation is already completed.")
            return redirect('consultation_detail', consultation_id=consultation.id)
        elif consultation.payment.status == 'pending':
            return redirect('payment_status', payment_id=consultation.payment.payment_id)

    # Get pricing
    pricing = ConsultationPricing.objects.filter(
        psychologist=consultation.psychologist,
        is_active=True
    ).first()

    if not pricing:
        # Default pricing
        pricing = ConsultationPricing.objects.filter(
            psychologist=None,
            is_active=True
        ).first()

    if not pricing:
        messages.error(request, "Pricing information not available.")
        return redirect('consultation_detail', consultation_id=consultation.id)

    # Get available payment methods
    payment_methods = PaymentMethod.objects.filter(is_active=True)

    if request.method == 'POST':
        payment_method_id = request.POST.get('payment_method')

        try:
            payment_method = PaymentMethod.objects.get(id=payment_method_id, is_active=True)

            # Create payment record
            payment = Payment.objects.create(
                user=request.user,
                consultation=consultation,
                payment_method=payment_method,
                amount=pricing.price,
                currency=pricing.currency,
                description=f"Payment for consultation with {consultation.psychologist.get_full_name()}"
            )

            # Log payment history
            PaymentHistory.objects.create(
                payment=payment,
                previous_status='',
                new_status='pending',
                changed_by=request.user,
                notes='Payment initiated'
            )

            # Redirect to payment gateway (simplified for demo)
            if payment_method.provider == 'chapa':
                return redirect('chapa_payment', payment_id=payment.payment_id)
            else:
                messages.error(request, "Payment method not supported yet.")
                return redirect('initiate_payment', consultation_id=consultation.id)

        except PaymentMethod.DoesNotExist:
            messages.error(request, "Invalid payment method selected.")

    return render(request, 'payments/initiate_payment.html', {
        'consultation': consultation,
        'pricing': pricing,
        'payment_methods': payment_methods
    })


def chapa_payment(request, payment_id):
    """Simulate Chapa payment process (placeholder)"""
    payment = get_object_or_404(Payment, payment_id=payment_id, user=request.user)

    # In a real implementation, this would redirect to Chapa's payment page
    # For demo purposes, we'll show a simple form

    if request.method == 'POST':
        # Simulate payment processing
        action = request.POST.get('action')

        if action == 'pay':
            # Simulate successful payment
            payment.status = 'completed'
            payment.completed_at = timezone.now()
            payment.external_transaction_id = f"chapa_{uuid.uuid4().hex[:12]}"
            payment.save()

            # Update consultation status
            payment.consultation.status = 'approved'
            payment.consultation.save()

            # Log payment history
            PaymentHistory.objects.create(
                payment=payment,
                previous_status='pending',
                new_status='completed',
                notes='Payment completed via Chapa'
            )

            return redirect('payment_success', payment_id=payment.payment_id)

        elif action == 'cancel':
            payment.status = 'cancelled'
            payment.save()

            PaymentHistory.objects.create(
                payment=payment,
                previous_status='pending',
                new_status='cancelled',
                notes='Payment cancelled by user'
            )

            return redirect('payment_failed', payment_id=payment.payment_id)

    return render(request, 'payments/chapa_payment.html', {
        'payment': payment
    })


@csrf_exempt
def payment_callback(request):
    """Handle payment gateway callbacks"""
    if request.method == 'POST':
        try:
            # Parse callback data (this would be specific to the payment gateway)
            data = json.loads(request.body)

            # Process the callback
            # This is a simplified version - real implementation would verify signatures, etc.

            return JsonResponse({'status': 'success'})

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


def payment_success(request, payment_id):
    """Payment success page"""
    payment = get_object_or_404(Payment, payment_id=payment_id, user=request.user)

    return render(request, 'payments/payment_success.html', {
        'payment': payment
    })


def payment_failed(request, payment_id):
    """Payment failed page"""
    payment = get_object_or_404(Payment, payment_id=payment_id, user=request.user)

    return render(request, 'payments/payment_failed.html', {
        'payment': payment
    })


@login_required
def payment_history(request):
    """User's payment history"""
    payments = Payment.objects.filter(user=request.user).order_by('-created_at')

    return render(request, 'payments/payment_history.html', {
        'payments': payments
    })


@login_required
def payment_receipt(request, payment_id):
    """Generate payment receipt"""
    payment = get_object_or_404(Payment, payment_id=payment_id, user=request.user)

    if payment.status != 'completed':
        messages.error(request, "Receipt is only available for completed payments.")
        return redirect('payment_history')

    return render(request, 'payments/payment_receipt.html', {
        'payment': payment
    })


@login_required
def request_refund(request, payment_id):
    """Request a refund"""
    payment = get_object_or_404(Payment, payment_id=payment_id, user=request.user)

    if payment.status != 'completed':
        messages.error(request, "Refunds can only be requested for completed payments.")
        return redirect('payment_history')

    # Check if refund already exists
    if payment.refunds.exists():
        messages.info(request, "A refund request already exists for this payment.")
        return redirect('payment_history')

    if request.method == 'POST':
        reason = request.POST.get('reason')

        if reason:
            refund = Refund.objects.create(
                payment=payment,
                amount=payment.amount,
                reason=reason,
                initiated_by=request.user
            )

            messages.success(request, "Refund request submitted successfully!")
            return redirect('payment_history')
        else:
            messages.error(request, "Please provide a reason for the refund.")

    return render(request, 'payments/request_refund.html', {
        'payment': payment
    })


# Admin Views
@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_payment_list(request):
    """Admin view of all payments"""
    payments = Payment.objects.all().order_by('-created_at')

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        payments = payments.filter(status=status_filter)

    return render(request, 'payments/admin_payment_list.html', {
        'payments': payments,
        'status_filter': status_filter,
        'status_choices': Payment.PAYMENT_STATUS_CHOICES
    })


@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_refund_list(request):
    """Admin view of refund requests"""
    refunds = Refund.objects.all().order_by('-created_at')

    if request.method == 'POST':
        refund_id = request.POST.get('refund_id')
        action = request.POST.get('action')

        try:
            refund = Refund.objects.get(id=refund_id)

            if action == 'approve':
                refund.status = 'completed'
                refund.completed_at = timezone.now()
                refund.save()

                # Update original payment
                refund.payment.status = 'refunded'
                refund.payment.save()

                messages.success(request, f"Refund approved for payment {refund.payment.payment_id}")

            elif action == 'reject':
                refund.status = 'failed'
                refund.save()

                messages.success(request, f"Refund rejected for payment {refund.payment.payment_id}")

        except Refund.DoesNotExist:
            messages.error(request, "Refund not found.")

    return render(request, 'payments/admin_refund_list.html', {
        'refunds': refunds
    })


def consultation_pricing(request):
    """Display consultation pricing"""
    pricing = ConsultationPricing.objects.filter(is_active=True).order_by('price')

    return render(request, 'payments/consultation_pricing.html', {
        'pricing': pricing
    })
