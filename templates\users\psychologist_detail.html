{% extends "base.html" %}
{% block content %}
<div class="max-w-4xl mx-auto px-4 py-8">
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <img class="h-24 w-24 rounded-full border-4 border-white" src="{{ psych.profile_picture.url|default:'/static/images/default-profile.png' }}" alt="{{ psych.first_name }}">
                </div>
                <div class="ml-6">
                    <h1 class="text-3xl font-bold">{{ psych.first_name }} {{ psych.last_name }}, {{ psych.qualifications }}</h1>
                    <div class="flex items-center mt-2">
                        <div class="flex items-center">
                            {% with ''|center:psych.rating as range %}
                            {% for _ in range %}
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            {% endfor %}
                            {% endwith %}
                            <span class="ml-1 text-blue-100">{{ psych.rating }} ({{ psych.review_count }} reviews)</span>
                        </div>
                        <span class="mx-2 text-blue-200">|</span>
                        <span class="text-blue-100">{{ psych.experience_years }} years experience</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800 mb-3">Specialties</h2>
                    <div class="flex flex-wrap gap-2">
                        {% for s in psych.specialties.all %}
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">{{ s.name }}</span>
                        {% endfor %}
                    </div>

                    <h2 class="text-xl font-semibold text-gray-800 mt-6 mb-3">Serves</h2>
                    <p class="text-gray-700">{{ psych.age_groups_served }}</p>

                    <h2 class="text-xl font-semibold text-gray-800 mt-6 mb-3">Languages</h2>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Amharic</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">English</span>
                    </div>
                </div>

                <div>
                    <h2 class="text-xl font-semibold text-gray-800 mb-3">About</h2>
                    <p class="text-gray-700 whitespace-pre-line">{{ psych.portfolio_description }}</p>

                    <h2 class="text-xl font-semibold text-gray-800 mt-6 mb-3">Availability</h2>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-day text-blue-500 mr-2"></i>
                            <span>Monday - Friday: 9:00 AM - 5:00 PM</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-video text-blue-500 mr-2"></i>
                            <span>Online sessions available</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-200">
                <a href="{% url 'request_consultation' %}?psychologist={{ psych.id }}" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                    Request Consultation
                    <i class="fas fa-chevron-right ml-2"></i>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}