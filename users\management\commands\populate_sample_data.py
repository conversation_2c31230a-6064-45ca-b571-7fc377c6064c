from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, time, timedelta
from users.models import CustomUser, Specialty, PsychologistAvailability
from discussions.models import Category, Post
from messaging.models import FAQ
from resources.models import ResourceCategory
from payments.models import PaymentMethod, ConsultationPricing


class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create specialties
        specialties_data = [
            'Clinical Psychology',
            'Counseling Psychology',
            'Child Psychology',
            'Cognitive Behavioral Therapy',
            'Family Therapy',
            'Trauma Therapy',
            'Addiction Counseling',
            'Anxiety Disorders',
            'Depression Treatment',
            'Relationship Counseling'
        ]
        
        for specialty_name in specialties_data:
            specialty, created = Specialty.objects.get_or_create(
                name=specialty_name,
                defaults={'description': f'Specialization in {specialty_name}'}
            )
            if created:
                self.stdout.write(f'Created specialty: {specialty_name}')
        
        # Create discussion categories
        discussion_categories = [
            ('General Discussion', 'General mental health discussions'),
            ('Anxiety & Stress', 'Topics related to anxiety and stress management'),
            ('Depression Support', 'Support and discussions about depression'),
            ('Relationships', 'Relationship advice and discussions'),
            ('Self-Care', 'Self-care tips and strategies'),
            ('Student Life', 'Mental health topics for students'),
            ('Work & Career', 'Work-related stress and career guidance'),
            ('Family Issues', 'Family dynamics and issues')
        ]
        
        for cat_name, cat_desc in discussion_categories:
            category, created = Category.objects.get_or_create(
                name=cat_name,
                defaults={'description': cat_desc, 'allow_guest_access': True}
            )
            if created:
                self.stdout.write(f'Created discussion category: {cat_name}')
        
        # Create resource categories
        resource_categories = [
            ('Self-Help Guides', 'Self-help and educational materials'),
            ('Meditation & Mindfulness', 'Meditation guides and mindfulness resources'),
            ('Worksheets', 'Therapeutic worksheets and exercises'),
            ('Videos', 'Educational and therapeutic videos'),
            ('Audio Resources', 'Podcasts and audio materials'),
            ('Research Papers', 'Academic research and studies')
        ]
        
        for cat_name, cat_desc in resource_categories:
            category, created = ResourceCategory.objects.get_or_create(
                name=cat_name,
                defaults={'description': cat_desc}
            )
            if created:
                self.stdout.write(f'Created resource category: {cat_name}')
        
        # Create FAQ entries
        faq_data = [
            {
                'question': 'How do I book a consultation?',
                'answer': 'To book a consultation, first register as a user, then browse our verified psychologists and select an available time slot from their calendar.',
                'category': 'Booking',
                'keywords': 'book, consultation, appointment, schedule'
            },
            {
                'question': 'What are your pricing options?',
                'answer': 'We offer both free and paid consultations. Free consultations are typically 30-minute introductory sessions. Paid consultations vary by psychologist and can range from $50-150 per session.',
                'category': 'Pricing',
                'keywords': 'price, cost, payment, fee, money'
            },
            {
                'question': 'How do I find a psychologist?',
                'answer': 'You can browse psychologists by specialty, rating, or availability. Use our search filters to find the right match for your needs.',
                'category': 'Finding Help',
                'keywords': 'find, psychologist, therapist, counselor, specialist'
            },
            {
                'question': 'Is my information confidential?',
                'answer': 'Yes, all consultations and personal information are strictly confidential. We follow professional ethics and privacy guidelines.',
                'category': 'Privacy',
                'keywords': 'confidential, privacy, secure, private, safe'
            },
            {
                'question': 'Can I cancel or reschedule my appointment?',
                'answer': 'Yes, you can cancel or reschedule appointments up to 24 hours before the scheduled time without penalty.',
                'category': 'Booking',
                'keywords': 'cancel, reschedule, change, appointment, booking'
            }
        ]
        
        for faq in faq_data:
            faq_obj, created = FAQ.objects.get_or_create(
                question=faq['question'],
                defaults={
                    'answer': faq['answer'],
                    'category': faq['category'],
                    'keywords': faq['keywords']
                }
            )
            if created:
                self.stdout.write(f'Created FAQ: {faq["question"][:50]}...')
        
        # Create payment methods
        payment_methods = [
            {
                'name': 'Chapa',
                'provider': 'chapa',
                'configuration': {'api_key': 'test_key', 'webhook_url': '/payments/callback/'}
            },
            {
                'name': 'Bank Transfer',
                'provider': 'manual',
                'configuration': {'account_number': '**********', 'bank_name': 'Sample Bank'}
            }
        ]
        
        for method in payment_methods:
            payment_method, created = PaymentMethod.objects.get_or_create(
                name=method['name'],
                defaults={
                    'provider': method['provider'],
                    'configuration': method['configuration']
                }
            )
            if created:
                self.stdout.write(f'Created payment method: {method["name"]}')
        
        # Create consultation pricing
        pricing_data = [
            {
                'name': 'Standard Consultation',
                'description': 'Standard 60-minute consultation session',
                'price': 100.00,
                'duration_minutes': 60
            },
            {
                'name': 'Extended Consultation',
                'description': 'Extended 90-minute consultation session',
                'price': 150.00,
                'duration_minutes': 90
            },
            {
                'name': 'Brief Consultation',
                'description': 'Brief 30-minute consultation session',
                'price': 60.00,
                'duration_minutes': 30
            }
        ]
        
        for pricing in pricing_data:
            pricing_obj, created = ConsultationPricing.objects.get_or_create(
                name=pricing['name'],
                defaults={
                    'description': pricing['description'],
                    'price': pricing['price'],
                    'duration_minutes': pricing['duration_minutes']
                }
            )
            if created:
                self.stdout.write(f'Created pricing: {pricing["name"]}')
        
        # Create admin user if it doesn't exist
        if not CustomUser.objects.filter(username='admin').exists():
            admin_user = CustomUser.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='Admin',
                last_name='User',
                role='admin',
                is_staff=True,
                is_superuser=True,
                is_verified=True,
                age_verified=True,
                date_of_birth=date(1990, 1, 1)
            )
            self.stdout.write('Created admin user (username: admin, password: admin123)')
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
        self.stdout.write('You can now:')
        self.stdout.write('1. Access admin panel at http://127.0.0.1:8000/admin/')
        self.stdout.write('2. Login with username: admin, password: admin123')
        self.stdout.write('3. Create psychologist accounts and approve them')
        self.stdout.write('4. Test the chatbot at http://127.0.0.1:8000/messaging/chatbot/')
