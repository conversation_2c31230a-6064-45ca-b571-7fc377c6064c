{% extends 'base.html' %}
{% load static %}

{% block title %}Psychologist <PERSON><PERSON> - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .dashboard-header-content {
        position: relative;
        z-index: 2;
    }
    
    .stats-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .quick-action-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--gray-200);
        text-decoration: none;
        color: var(--gray-700);
        transition: all 0.3s ease;
        display: block;
        height: 100%;
    }
    
    .quick-action-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
        color: var(--primary-blue);
        text-decoration: none;
    }
    
    .action-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .approval-status {
        padding: 1rem;
        border-radius: var(--radius-md);
        margin-bottom: 2rem;
    }
    
    .approval-pending {
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.3);
        color: #92400e;
    }
    
    .approval-approved {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: #065f46;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="dashboard-header-content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-user-md me-3"></i>
                            Welcome back, Dr. {{ user.first_name }}!
                        </h1>
                        <p class="mb-0 opacity-90">
                            Manage your consultations, schedule, and help your clients on their mental health journey.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="text-white-50">
                            <i class="fas fa-calendar me-2"></i>
                            {{ "now"|date:"F d, Y" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approval Status -->
        {% if not user.psychologist_approved %}
        <div class="approval-status approval-pending">
            <div class="d-flex align-items-center">
                <i class="fas fa-clock fa-2x me-3"></i>
                <div>
                    <h5 class="mb-1">Account Pending Approval</h5>
                    <p class="mb-0">Your psychologist account is currently under review. You'll be notified once it's approved.</p>
                </div>
            </div>
        </div>
        {% else %}
        <div class="approval-status approval-approved">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x me-3"></i>
                <div>
                    <h5 class="mb-1">Account Approved</h5>
                    <p class="mb-0">Your psychologist account is active. You can now offer consultations and manage your practice.</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card text-center">
                    <div class="stats-icon mx-auto" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-blue);">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stats-number">{{ pending_consultations|default:0 }}</div>
                    <h6 class="text-muted">Pending Consultations</h6>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card text-center">
                    <div class="stats-icon mx-auto" style="background: rgba(16, 185, 129, 0.1); color: var(--success);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number">{{ total_consultations|default:0 }}</div>
                    <h6 class="text-muted">Total Consultations</h6>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card text-center">
                    <div class="stats-icon mx-auto" style="background: rgba(245, 158, 11, 0.1); color: var(--warning);">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-number">{{ user.rating|default:"N/A" }}</div>
                    <h6 class="text-muted">Average Rating</h6>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card text-center">
                    <div class="stats-icon mx-auto" style="background: rgba(139, 92, 246, 0.1); color: #8b5cf6;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number">24/7</div>
                    <h6 class="text-muted">Availability</h6>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'psychologist_consultations' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5 class="mb-2">View Consultations</h5>
                    <p class="text-muted mb-0">Manage your upcoming and past consultations</p>
                </a>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'manage_time_slots' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5 class="mb-2">Manage Schedule</h5>
                    <p class="text-muted mb-0">Set your availability and time slots</p>
                </a>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'my_resources' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <h5 class="mb-2">My Resources</h5>
                    <p class="text-muted mb-0">Upload and manage your resources</p>
                </a>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'conversation_list' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h5 class="mb-2">Messages</h5>
                    <p class="text-muted mb-0">Communicate with your clients</p>
                </a>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'create_notification' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h5 class="mb-2">Create Notification</h5>
                    <p class="text-muted mb-0">Notify your followers about events</p>
                </a>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <a href="{% url 'upload_resource' %}" class="quick-action-card">
                    <div class="action-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h5 class="mb-2">Upload Resource</h5>
                    <p class="text-muted mb-0">Share helpful resources with clients</p>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
