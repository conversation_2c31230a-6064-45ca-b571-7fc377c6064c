# Generated by Django 5.2.1 on 2025-07-08 17:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('consultations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsultationTopic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('is_paid', models.BooleanField(default=True)),
            ],
        ),
        migrations.AddField(
            model_name='consultation',
            name='feedback',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='consultation',
            name='rating',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
    ]
