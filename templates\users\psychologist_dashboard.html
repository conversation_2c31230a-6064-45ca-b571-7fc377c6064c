{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Psychologist Dashboard" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .dashboard-hero {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .dashboard-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(16, 185, 129, 0.1);
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 32px rgba(0,0,0,0.15);
    }
    
    .card-header-custom {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #10b981;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #64748b;
        font-weight: 500;
        font-size: 1rem;
    }
    
    .stat-change {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    
    .stat-increase {
        color: #10b981;
    }
    
    .stat-decrease {
        color: #ef4444;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .action-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border: none;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        color: var(--white);
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }
    
    .action-btn.secondary:hover {
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
    }
    
    .recent-activity {
        background: var(--white);
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        padding: 1.5rem;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        background: #f8fafc;
        border-left: 4px solid #10b981;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .consultation-list {
        background: var(--white);
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .consultation-item {
        padding: 1.5rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .consultation-item:last-child {
        border-bottom: none;
    }
    
    .consultation-info {
        flex: 1;
    }
    
    .consultation-patient {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .consultation-details {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .consultation-status {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        color: var(--white);
    }
    
    .status-confirmed {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: var(--white);
    }
    
    .status-completed {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
    }
    
    .welcome-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .welcome-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="dashboard-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        {% trans "Welcome back" %}, Dr. {{ request.user.get_full_name|default:request.user.username }}
                    </h1>
                    <p class="lead mb-0">{% trans "Manage your consultations and help your patients on their mental health journey" %}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Welcome Card -->
    <div class="welcome-card">
        <div class="welcome-avatar">
            <i class="fas fa-user-md"></i>
        </div>
        <h3 class="mb-2">{% trans "Your Impact Today" %}</h3>
        <p class="mb-0">{% trans "You're making a difference in people's lives. Keep up the excellent work!" %}</p>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ pending_consultations|default:0 }}</div>
            <div class="stat-label">{% trans "Pending Requests" %}</div>
            <div class="stat-change stat-increase">
                <i class="fas fa-arrow-up me-1"></i>+2 {% trans "this week" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ upcoming_consultations|default:0 }}</div>
            <div class="stat-label">{% trans "Upcoming Sessions" %}</div>
            <div class="stat-change stat-increase">
                <i class="fas fa-calendar me-1"></i>{% trans "Next: Today 2:00 PM" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ completed_consultations|default:0 }}</div>
            <div class="stat-label">{% trans "Completed Sessions" %}</div>
            <div class="stat-change stat-increase">
                <i class="fas fa-check me-1"></i>+5 {% trans "this month" %}
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ average_rating|default:4.8 }}</div>
            <div class="stat-label">{% trans "Average Rating" %}</div>
            <div class="stat-change stat-increase">
                <i class="fas fa-star me-1"></i>{% trans "Excellent" %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-card">
        <div class="card-header-custom">
            <h3 class="text-success mb-0">
                <i class="fas fa-bolt me-2"></i>
                {% trans "Quick Actions" %}
            </h3>
        </div>
        <div class="p-4">
            <div class="quick-actions">
                <a href="{% url 'psychologist_consultations' %}" class="action-btn">
                    <i class="fas fa-calendar-check"></i>
                    {% trans "View Consultations" %}
                </a>
                <a href="{% url 'manage_time_slots' %}" class="action-btn secondary">
                    <i class="fas fa-clock"></i>
                    {% trans "Manage Schedule" %}
                </a>
                <a href="{% url 'discussion_list' %}" class="action-btn">
                    <i class="fas fa-comments"></i>
                    {% trans "Community Forum" %}
                </a>
                <a href="{% url 'resource_list' %}" class="action-btn secondary">
                    <i class="fas fa-book"></i>
                    {% trans "Resources" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="card-header-custom">
                    <h4 class="text-success mb-0">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Recent Activity" %}
                    </h4>
                </div>
                <div class="p-4">
                    <div class="recent-activity">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{% trans "New consultation request" %}</div>
                                <div class="activity-time">{% trans "2 hours ago" %}</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{% trans "Session completed with John D." %}</div>
                                <div class="activity-time">{% trans "Yesterday" %}</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{% trans "Received 5-star rating" %}</div>
                                <div class="activity-time">{% trans "2 days ago" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Consultations -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="card-header-custom">
                    <h4 class="text-success mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {% trans "Today's Schedule" %}
                    </h4>
                </div>
                <div class="consultation-list">
                    {% if upcoming_consultations_today %}
                        {% for consultation in upcoming_consultations_today %}
                        <div class="consultation-item">
                            <div class="consultation-info">
                                <div class="consultation-patient">{{ consultation.user.get_full_name|default:consultation.user.username }}</div>
                                <div class="consultation-details">
                                    {{ consultation.topic }} • {{ consultation.scheduled_time|time:"g:i A" }}
                                </div>
                            </div>
                            <div class="consultation-status status-{{ consultation.status }}">
                                {{ consultation.get_status_display }}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="consultation-item">
                            <div class="consultation-info">
                                <div class="consultation-patient">{% trans "No consultations scheduled" %}</div>
                                <div class="consultation-details">{% trans "Enjoy your free time today!" %}</div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any dashboard-specific JavaScript here
    console.log('Psychologist dashboard loaded');
    
    // Auto-refresh stats every 5 minutes
    setInterval(function() {
        // You could add AJAX calls here to update stats
        console.log('Stats refresh interval');
    }, 300000);
});
</script>
{% endblock %}
