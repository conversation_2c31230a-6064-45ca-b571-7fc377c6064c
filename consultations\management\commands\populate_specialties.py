from django.core.management.base import BaseCommand
from consultations.models import ConsultationSpecialty


class Command(BaseCommand):
    help = 'Populate consultation specialties'

    def handle(self, *args, **options):
        specialties = [
            {
                'name': 'Anxiety Disorders',
                'description': 'Treatment for anxiety, panic attacks, phobias, and related conditions',
                'icon': 'fas fa-heart-pulse'
            },
            {
                'name': 'Depression & Mood Disorders',
                'description': 'Support for depression, bipolar disorder, and mood-related issues',
                'icon': 'fas fa-brain'
            },
            {
                'name': 'Relationship Counseling',
                'description': 'Couples therapy, marriage counseling, and relationship issues',
                'icon': 'fas fa-heart'
            },
            {
                'name': 'Family Therapy',
                'description': 'Family dynamics, parenting support, and family conflict resolution',
                'icon': 'fas fa-home'
            },
            {
                'name': 'Trauma & PTSD',
                'description': 'Treatment for trauma, PTSD, and stress-related disorders',
                'icon': 'fas fa-shield-alt'
            },
            {
                'name': 'Addiction & Substance Abuse',
                'description': 'Support for addiction recovery and substance abuse issues',
                'icon': 'fas fa-ban'
            },
            {
                'name': 'Child & Adolescent Psychology',
                'description': 'Mental health support for children and teenagers',
                'icon': 'fas fa-child'
            },
            {
                'name': 'Career & Life Coaching',
                'description': 'Career guidance, life transitions, and personal development',
                'icon': 'fas fa-briefcase'
            },
            {
                'name': 'Stress Management',
                'description': 'Techniques for managing stress, burnout, and work-life balance',
                'icon': 'fas fa-leaf'
            },
            {
                'name': 'Grief & Loss Counseling',
                'description': 'Support for bereavement, loss, and grief processing',
                'icon': 'fas fa-dove'
            },
            {
                'name': 'Eating Disorders',
                'description': 'Treatment for anorexia, bulimia, and other eating disorders',
                'icon': 'fas fa-utensils'
            },
            {
                'name': 'Sleep Disorders',
                'description': 'Help with insomnia, sleep anxiety, and sleep-related issues',
                'icon': 'fas fa-bed'
            },
            {
                'name': 'Anger Management',
                'description': 'Techniques for managing anger and aggressive behaviors',
                'icon': 'fas fa-fist-raised'
            },
            {
                'name': 'Self-Esteem & Confidence',
                'description': 'Building self-worth, confidence, and positive self-image',
                'icon': 'fas fa-star'
            },
            {
                'name': 'Social Anxiety',
                'description': 'Help with social fears, shyness, and social interaction difficulties',
                'icon': 'fas fa-users'
            }
        ]

        created_count = 0
        for specialty_data in specialties:
            specialty, created = ConsultationSpecialty.objects.get_or_create(
                name=specialty_data['name'],
                defaults={
                    'description': specialty_data['description'],
                    'icon': specialty_data['icon'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                self.stdout.write(f"Created specialty: {specialty.name}")

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} consultation specialties')
        )
