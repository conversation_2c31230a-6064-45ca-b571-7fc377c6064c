<h1 class="text-2xl font-bold mb-4">Browse Psychologists</h1>

<form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 bg-white p-4 rounded shadow">
  <div>
    <label>Specialty:</label>
    <select name="specialty" class="w-full border px-2 py-1">
      <option value="">All</option>
      {% for s in specialties %}
        <option value="{{ s.name }}" {% if request.GET.specialty == s.name %}selected{% endif %}>{{ s.name }}</option>
      {% endfor %}
    </select>
  </div>

  <div>
    <label>Age Group:</label>
    <select name="age_group" class="w-full border px-2 py-1">
      <option value="">All</option>
      {% for group in age_groups %}
        <option value="{{ group }}" {% if request.GET.age_group == group %}selected{% endif %}>{{ group }}</option>
      {% endfor %}
    </select>
  </div>

  <div>
    <label>Min Experience (Years):</label>
    <input type="number" name="min_experience" class="w-full border px-2 py-1" value="{{ request.GET.min_experience }}">
  </div>

  <div>
    <label>Min Rating:</label>
    <input type="number" step="0.1" name="min_rating" class="w-full border px-2 py-1" value="{{ request.GET.min_rating }}">
  </div>

  <div class="md:col-span-4 text-right">
    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Filter</button>
  </div>
</form>

<div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
  {% for psych in psychologists %}
    <div class="border p-4 rounded shadow bg-white">
      <h2 class="text-xl font-semibold">{{ psych.first_name }} {{ psych.last_name }}</h2>
      <p><strong>Specialties:</strong>
        {% for s in psych.specialties.all %}
          {{ s.name }}{% if not forloop.last %}, {% endif %}
        {% endfor %}
      </p>
      <p><strong>Experience:</strong> {{ psych.experience_years }} years</p>
      <p><strong>Serves:</strong> {{ psych.age_groups_served }}</p>
      <p><strong>Rating:</strong> ⭐ {{ psych.rating }}</p>
      <p><strong>Qualifications:</strong> {{ psych.qualifications }}</p>
      <p><strong>Bio:</strong> {{ psych.portfolio_description }}</p>
      <a href="{% url 'psychologist_detail' psych.id %}" class="text-blue-600 underline">View Profile</a>
    </div>
  {% empty %}
    <p>No psychologists match your filter.</p>
  {% endfor %}
</div>
