{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Find Psychologists" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .psychologists-hero {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    .psychologists-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .search-section {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 3rem;
        border: 1px solid rgba(16, 185, 129, 0.1);
    }
    
    .psychologist-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(16, 185, 129, 0.1);
        transition: all 0.3s ease;
    }
    
    .psychologist-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }
    
    .psychologist-header {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        padding: 2rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }
    
    .psychologist-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 2rem;
        flex-shrink: 0;
        border: 4px solid var(--white);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }
    
    .psychologist-info {
        flex: 1;
    }
    
    .psychologist-name {
        color: #065f46;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .psychologist-title {
        color: #059669;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .rating-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #fbbf24;
    }
    
    .psychologist-body {
        padding: 2rem;
    }
    
    .specialties-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .specialty-tag {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .psychologist-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .psychologist-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }
    
    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        text-decoration: none;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
    }
    
    .btn-outline-action {
        background: transparent;
        color: #10b981;
        border: 2px solid #10b981;
    }
    
    .btn-outline-action:hover {
        background: #10b981;
        color: var(--white);
    }
    
    .filter-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .filter-label {
        color: #374151;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #10b981;
        box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
    }
    
    .availability-badge {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .unavailable-badge {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    }
    
    .empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: #6b7280;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }
    
    .pagination {
        background: var(--white);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        padding: 0.5rem;
    }
    
    .page-link {
        border: none;
        color: #10b981;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin: 0 0.25rem;
        transition: all 0.3s ease;
    }
    
    .page-link:hover {
        background: #10b981;
        color: var(--white);
    }
    
    .page-item.active .page-link {
        background: #10b981;
        color: var(--white);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="psychologists-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-user-md me-3"></i>
                        {% trans "Find Your Psychologist" %}
                    </h1>
                    <p class="lead mb-0">
                        {% trans "Connect with licensed mental health professionals who understand your needs and cultural background" %}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Search and Filter Section -->
    <div class="search-section">
        <h3 class="text-success mb-3">
            <i class="fas fa-search me-2"></i>
            {% trans "Find the Right Match" %}
        </h3>
        
        <form method="GET" id="filterForm">
            <div class="filter-section">
                <div class="filter-group">
                    <label class="filter-label">{% trans "Search by Name" %}</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="{% trans 'Enter psychologist name...' %}" 
                           value="{{ request.GET.search }}">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">{% trans "Specialization" %}</label>
                    <select name="specialization" class="form-select">
                        <option value="">{% trans "All Specializations" %}</option>
                        <option value="anxiety">{% trans "Anxiety & Stress" %}</option>
                        <option value="depression">{% trans "Depression" %}</option>
                        <option value="relationships">{% trans "Relationships" %}</option>
                        <option value="family">{% trans "Family Therapy" %}</option>
                        <option value="trauma">{% trans "Trauma & PTSD" %}</option>
                        <option value="addiction">{% trans "Addiction" %}</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">{% trans "Language" %}</label>
                    <select name="language" class="form-select">
                        <option value="">{% trans "Any Language" %}</option>
                        <option value="amharic">{% trans "Amharic" %}</option>
                        <option value="english">{% trans "English" %}</option>
                        <option value="oromo">{% trans "Oromo" %}</option>
                        <option value="tigrinya">{% trans "Tigrinya" %}</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">{% trans "Availability" %}</label>
                    <select name="availability" class="form-select">
                        <option value="">{% trans "Any Time" %}</option>
                        <option value="today">{% trans "Available Today" %}</option>
                        <option value="week">{% trans "This Week" %}</option>
                        <option value="month">{% trans "This Month" %}</option>
                    </select>
                </div>
            </div>
            
            <div class="text-center mt-3">
                <button type="submit" class="btn btn-action btn-primary-action">
                    <i class="fas fa-search me-2"></i>{% trans "Search Psychologists" %}
                </button>
                <a href="{% url 'psychologist_list' %}" class="btn btn-action btn-outline-action">
                    <i class="fas fa-refresh me-2"></i>{% trans "Clear Filters" %}
                </a>
            </div>
        </form>
    </div>

    <!-- Results Count -->
    {% if psychologists %}
    <div class="mb-4">
        <h5 class="text-muted">
            {% blocktrans count counter=psychologists|length %}
                Found {{ counter }} psychologist
            {% plural %}
                Found {{ counter }} psychologists
            {% endblocktrans %}
        </h5>
    </div>
    {% endif %}

    <!-- Psychologists Grid -->
    {% if psychologists %}
        {% for psychologist in psychologists %}
        <div class="psychologist-card">
            <div class="psychologist-header">
                <div class="psychologist-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="psychologist-info">
                    <h4 class="psychologist-name">
                        Dr. {{ psychologist.get_full_name|default:psychologist.username }}
                    </h4>
                    {% if psychologist.profile.specialization %}
                    <p class="psychologist-title">{{ psychologist.profile.specialization }}</p>
                    {% endif %}
                    <div class="rating-display">
                        {% for i in "12345" %}
                            {% if forloop.counter <= psychologist.average_rating|default:5 %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="ms-2 text-muted">({{ psychologist.total_reviews|default:0 }} {% trans "reviews" %})</span>
                    </div>
                </div>
                <div class="availability-badge">
                    <i class="fas fa-circle"></i>
                    {% trans "Available" %}
                </div>
            </div>
            
            <div class="psychologist-body">
                {% if psychologist.profile.bio %}
                <p class="text-muted mb-3">{{ psychologist.profile.bio|truncatewords:25 }}</p>
                {% endif %}
                
                <!-- Specialties -->
                {% if psychologist.profile.specialties %}
                <div class="specialties-grid">
                    {% for specialty in psychologist.profile.specialties|slice:":3" %}
                    <span class="specialty-tag">{{ specialty }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Stats -->
                <div class="psychologist-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ psychologist.years_experience|default:5 }}</div>
                        <div class="stat-label">{% trans "Years" %}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ psychologist.total_consultations|default:50 }}</div>
                        <div class="stat-label">{% trans "Sessions" %}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ psychologist.response_time|default:2 }}</div>
                        <div class="stat-label">{% trans "Hours" %}</div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="psychologist-actions">
                    <a href="{% url 'psychologist_detail' psychologist.id %}" class="btn btn-action btn-outline-action">
                        <i class="fas fa-eye"></i>
                        {% trans "View Profile" %}
                    </a>
                    <a href="{% url 'request_consultation' %}?psychologist={{ psychologist.id }}" class="btn btn-action btn-primary-action">
                        <i class="fas fa-calendar-plus"></i>
                        {% trans "Book Session" %}
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination-wrapper">
            <nav aria-label="{% trans 'Psychologists pagination' %}">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-user-md"></i>
            </div>
            <h4 class="mb-3">{% trans "No Psychologists Found" %}</h4>
            <p class="text-muted mb-4">
                {% trans "We couldn't find any psychologists matching your criteria. Try adjusting your search filters." %}
            </p>
            <a href="{% url 'psychologist_list' %}" class="btn btn-action btn-primary-action">
                <i class="fas fa-refresh me-2"></i>{% trans "View All Psychologists" %}
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.getElementById('filterForm');
    const filterInputs = filterForm.querySelectorAll('select, input');
    
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.type !== 'text') {
                filterForm.submit();
            }
        });
    });
    
    // Debounce text input
    let searchTimeout;
    const searchInput = filterForm.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}
