<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ECPI - Ethiopian Psychological Consultation{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'images/favicon.ico' %}" type="image/x-icon">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'landing' %}">
                <img src="{% static 'images/logo.png' %}" alt="ECPI Logo" height="40">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'landing' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'discussion_list' %}">Discussions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'psychologist_list' %}">Find Psychologists</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'about' %}">About</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                {% if user.role == 'psychologist' %}
                                    <i class="fas fa-user-md me-1"></i>
                                {% else %}
                                    <i class="fas fa-user me-1"></i>
                                {% endif %}
                                {{ user.first_name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                {% if user.role == 'psychologist' %}
                                    <li><a class="dropdown-item" href="{% url 'psychologist_dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                {% elif user.role == 'end_user' %}
                                    <li><a class="dropdown-item" href="{% url 'enduser_dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{% url 'my_consultations' %}"><i class="fas fa-calendar-check me-2"></i>My Consultations</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}"><i class="fas fa-sign-in-alt me-1"></i>Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register' %}"><i class="fas fa-user-plus me-1"></i>Register</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-4 flex-grow-1">
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5>About ECPI</h5>
                    <p class="mb-0">Web-based consultation and discussion platform for Ethiopian psychological issues.</p>
                </div>
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'landing' %}" class="text-white">Home</a></li>
                        <li><a href="{% url 'discussion_list' %}" class="text-white">Discussions</a></li>
                        <li><a href="{% url 'psychologist_list' %}" class="text-white">Find Psychologists</a></li>
                        <li><a href="{% url 'about' %}" class="text-white">About</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact</h5>
                    <p class="mb-1"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p class="mb-0"><i class="fas fa-phone me-2"></i> +251 123 456 789</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; {% now "Y" %} ECPI. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
