<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional psychology consultation platform connecting you with licensed mental health professionals">
    <meta name="keywords" content="psychology, mental health, consultation, therapy, counseling">
    <title>{% block title %}Ethiopian Psychological Consultation Platform{% endblock %}</title>
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    {% load static %}
    {% load i18n %}
    <link href="{% static 'css/theme.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Skip to main content for accessibility -->
    <a class="visually-hidden-focusable" href="#main-content">Skip to main content</a>
    
    <!-- Navigation -->
    <header class="header" role="banner">
        <nav class="navbar navbar-expand-lg" aria-label="Main navigation">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="{% url 'landing' %}">
                    <i class="fas fa-brain me-2" aria-hidden="true"></i>
                    <span>Psychology Platform</span>
                </a>
                
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'landing' %}">
                                <i class="fas fa-home me-1" aria-hidden="true"></i>Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'discussion_list' %}">
                                <i class="fas fa-comments me-1" aria-hidden="true"></i>Discussions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'psychologist_list' %}">
                                <i class="fas fa-user-md me-1" aria-hidden="true"></i>Psychologists
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'resource_list' %}">
                                <i class="fas fa-book me-1" aria-hidden="true"></i>Resources
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'chatbot_interface' %}">
                                <i class="fas fa-comments me-1" aria-hidden="true"></i>Chat Assistant
                            </a>
                        </li>
                    </ul>
                    
                    <!-- Language Switcher -->
                    <ul class="navbar-nav me-3">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-globe me-1"></i>
                                {% get_current_language as LANGUAGE_CODE %}
                                {% if LANGUAGE_CODE == 'am' %}አማርኛ{% else %}English{% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="post" action="{% url 'set_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="en">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-flag-usa me-2"></i>English
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="post" action="{% url 'set_language' %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="language" value="am">
                                        <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-flag me-2"></i>አማርኛ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    </ul>

                    <!-- User Menu -->
                    <ul class="navbar-nav">
                        {% if user.is_authenticated %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="avatar-sm me-2">
                                        <i class="fas fa-user-circle fa-lg" aria-hidden="true"></i>
                                    </div>
                                    <span class="d-none d-md-inline">{{ user.first_name|default:user.username }}</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li class="dropdown-header">
                                        <strong>{{ user.get_full_name|default:user.username }}</strong>
                                        <small class="text-muted d-block">{{ user.get_role_display }}</small>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    
                                    {% if user.role == 'psychologist' %}
                                        <li><a class="dropdown-item" href="{% url 'psychologist_dashboard' %}">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                        </a></li>
                                    {% elif user.role == 'end_user' %}
                                        <li><a class="dropdown-item" href="{% url 'enduser_dashboard' %}">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                        </a></li>
                                    {% elif user.role == 'admin' %}
                                        <li><a class="dropdown-item" href="{% url 'custom_admin:dashboard' %}">
                                            <i class="fas fa-chart-line me-2"></i>Admin Dashboard
                                        </a></li>
                                        <li><a class="dropdown-item" href="/admin/">
                                            <i class="fas fa-cog me-2"></i>Django Admin
                                        </a></li>
                                    {% endif %}
                                    
                                    <li><a class="dropdown-item" href="{% url 'my_consultations' %}">
                                        <i class="fas fa-calendar-check me-2"></i>My Consultations
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'conversation_list' %}">
                                        <i class="fas fa-envelope me-2"></i>Messages
                                    </a></li>
                                    
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="{% url 'logout' %}">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </a></li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item me-2">
                                <a class="nav-link" href="{% url 'login' %}">
                                    <i class="fas fa-sign-in-alt me-1" aria-hidden="true"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="btn btn-primary btn-sm" href="{% url 'register' %}">
                                    <i class="fas fa-user-plus me-1" aria-hidden="true"></i>Register
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        {% if message.tags == 'success' %}
                            <i class="fas fa-check-circle me-2" aria-hidden="true"></i>
                        {% elif message.tags == 'error' or message.tags == 'danger' %}
                            <i class="fas fa-exclamation-triangle me-2" aria-hidden="true"></i>
                        {% elif message.tags == 'warning' %}
                            <i class="fas fa-exclamation-circle me-2" aria-hidden="true"></i>
                        {% else %}
                            <i class="fas fa-info-circle me-2" aria-hidden="true"></i>
                        {% endif %}
                        <span>{{ message }}</span>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main id="main-content" class="flex-grow-1" role="main">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-brain me-2"></i>Psychology Platform
                    </h5>
                    <p class="mb-3">Connecting you with professional mental health support through secure, accessible, and comprehensive psychological services.</p>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Services</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'psychologist_list' %}">Find Psychologists</a></li>
                        <li class="mb-2"><a href="{% url 'resource_list' %}">Resources</a></li>
                        <li class="mb-2"><a href="{% url 'discussion_list' %}">Community</a></li>
                        <li class="mb-2"><a href="{% url 'chatbot_interface' %}">Chat Assistant</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="mb-3">Support</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{% url 'about' %}">About Us</a></li>
                        <li class="mb-2"><a href="{% url 'contact' %}">Contact</a></li>
                        <li class="mb-2"><a href="#">Help Center</a></li>
                        <li class="mb-2"><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">Contact Info</h5>
                    <div class="mb-2">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:+251911234567">+251 91 123 4567</a>
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Addis Ababa, Ethiopia
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Psychology Platform. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        Made with <i class="fas fa-heart text-danger"></i> for mental health
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
