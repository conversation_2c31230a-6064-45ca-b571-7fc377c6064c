from django import forms
from .models import Consultation

class ConsultationForm(forms.ModelForm):
    class Meta:
        model = Consultation
        fields = ['type', 'topic', 'description', 'scheduled_date']
        widgets = {
            'scheduled_date': forms.DateInput(attrs={'type': 'date'}),
        }

class ConsultationStatusForm(forms.ModelForm):
    class Meta:
        model = Consultation
        fields = ['status']

from users.models import CustomUser

class AdminAssignPsychologistForm(forms.ModelForm):
    psychologist = forms.ModelChoiceField(
        queryset=CustomUser.objects.filter(role='psychologist', is_verified=True),
        required=False,
        label="Assign Psychologist"
    )

    class Meta:
        model = Consultation
        fields = ['psychologist']

class RatingForm(forms.ModelForm):
    class Meta:
        model = Consultation
        fields = ['rating', 'feedback']
        widgets = {
            'rating': forms.NumberInput(attrs={'min': 1, 'max': 5}),
            'feedback': forms.Textarea(attrs={'rows': 3, 'placeholder': 'Optional feedback'}),
        }
