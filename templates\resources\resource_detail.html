{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ resource.title }} - {% trans "Resources" %}{% endblock %}

{% block extra_css %}
<style>
    .resource-header {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .resource-content-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .resource-media {
        height: 300px;
        background: linear-gradient(135deg, var(--success) 0%, var(--info) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 4rem;
        position: relative;
    }
    
    .resource-type-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(255, 255, 255, 0.9);
        color: var(--success);
        padding: 0.5rem 1rem;
        border-radius: var(--radius-full);
        font-weight: 600;
    }
    
    .resource-info {
        padding: 2rem;
    }
    
    .author-card {
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid var(--success);
    }
    
    .author-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--success) 0%, var(--info) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin-right: 1rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.3s ease;
        flex: 1;
        min-width: 150px;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .meta-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .meta-item {
        background: var(--gray-50);
        padding: 1rem;
        border-radius: var(--radius-md);
        text-align: center;
        border-left: 3px solid var(--success);
    }
    
    .meta-icon {
        font-size: 1.5rem;
        color: var(--success);
        margin-bottom: 0.5rem;
    }
    
    .related-resources {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 2rem;
        margin-top: 2rem;
    }
    
    .related-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: var(--radius-md);
        margin-bottom: 1rem;
        background: var(--gray-50);
        transition: all 0.3s ease;
    }
    
    .related-item:hover {
        background: var(--gray-100);
        transform: translateX(5px);
    }
    
    .related-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--success);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        margin-right: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="resource-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'resource_list' %}" class="text-white-50">{% trans "Resources" %}</a></li>
                        {% if resource.category %}
                        <li class="breadcrumb-item"><span class="text-white-50">{{ resource.category.name }}</span></li>
                        {% endif %}
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ resource.title|truncatewords:5 }}</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold mb-3">{{ resource.title }}</h1>
                <p class="lead mb-0">{{ resource.description|truncatewords:30 }}</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex flex-column align-items-lg-end gap-2">
                    {% if resource.file %}
                    <a href="{% url 'download_resource' resource.id %}" class="btn btn-light btn-lg">
                        <i class="fas fa-download me-2"></i>{% trans "Download" %}
                    </a>
                    {% endif %}
                    {% if user.is_authenticated %}
                    <button class="btn btn-outline-light" onclick="toggleBookmark({{ resource.id }})">
                        <i class="fas fa-bookmark me-2"></i>{% trans "Bookmark" %}
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="resource-content-card">
                <!-- Media Section -->
                <div class="resource-media">
                    <span class="resource-type-badge">{{ resource.get_resource_type_display }}</span>
                    {% if resource.resource_type == 'video' %}
                        <i class="fas fa-play-circle"></i>
                    {% elif resource.resource_type == 'audio' %}
                        <i class="fas fa-headphones"></i>
                    {% elif resource.resource_type == 'document' %}
                        <i class="fas fa-file-alt"></i>
                    {% elif resource.resource_type == 'image' %}
                        <i class="fas fa-image"></i>
                    {% else %}
                        <i class="fas fa-book"></i>
                    {% endif %}
                </div>
                
                <!-- Content -->
                <div class="resource-info">
                    <div class="meta-grid">
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div>
                                <strong>{% trans "Published" %}</strong><br>
                                <span class="text-muted">{{ resource.created_at|date:"F d, Y" }}</span>
                            </div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div>
                                <strong>{% trans "Downloads" %}</strong><br>
                                <span class="text-muted">{{ resource.download_count|default:"0" }}</span>
                            </div>
                        </div>
                        
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div>
                                <strong>{% trans "Rating" %}</strong><br>
                                <span class="text-muted">{{ resource.average_rating|default:"N/A" }}</span>
                            </div>
                        </div>
                        
                        {% if resource.file_size %}
                        <div class="meta-item">
                            <div class="meta-icon">
                                <i class="fas fa-file"></i>
                            </div>
                            <div>
                                <strong>{% trans "File Size" %}</strong><br>
                                <span class="text-muted">{{ resource.file_size|filesizeformat }}</span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-4">
                        <h4 class="text-success mb-3">{% trans "Description" %}</h4>
                        <div class="text-muted">{{ resource.description|linebreaks }}</div>
                    </div>
                    
                    <!-- Tags -->
                    {% if resource.tags %}
                    <div class="mb-4">
                        <h5 class="text-success mb-3">{% trans "Tags" %}</h5>
                        <div>
                            {% for tag in resource.tags.all %}
                            <span class="badge bg-success me-2 mb-2">{{ tag.name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- External Link -->
                    {% if resource.url %}
                    <div class="mb-4">
                        <h5 class="text-success mb-3">{% trans "External Link" %}</h5>
                        <a href="{{ resource.url }}" target="_blank" class="btn btn-outline-success">
                            <i class="fas fa-external-link-alt me-2"></i>{% trans "Visit Link" %}
                        </a>
                    </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        {% if resource.file %}
                        <a href="{% url 'download_resource' resource.id %}" class="btn btn-success btn-action">
                            <i class="fas fa-download me-2"></i>{% trans "Download Resource" %}
                        </a>
                        {% endif %}
                        
                        {% if user.is_authenticated %}
                        <button class="btn btn-outline-primary btn-action" onclick="toggleBookmark({{ resource.id }})">
                            <i class="fas fa-bookmark me-2"></i>{% trans "Bookmark" %}
                        </button>
                        
                        <button class="btn btn-outline-warning btn-action" onclick="showRatingModal()">
                            <i class="fas fa-star me-2"></i>{% trans "Rate Resource" %}
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-info btn-action" onclick="shareResource()">
                            <i class="fas fa-share me-2"></i>{% trans "Share" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Author Info -->
            <div class="author-card">
                <h5 class="text-success mb-3">
                    <i class="fas fa-user-md me-2"></i>{% trans "Author" %}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="author-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{{ resource.author.get_full_name|default:resource.author.username }}</h6>
                        <p class="text-muted mb-1">{% trans "Licensed Psychologist" %}</p>
                        {% if resource.author.bio %}
                        <small class="text-muted">{{ resource.author.bio|truncatewords:15 }}</small>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'psychologist_detail' resource.author.id %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-eye me-1"></i>{% trans "View Profile" %}
                    </a>
                    {% if user.is_authenticated and user != resource.author %}
                    <a href="{% url 'start_conversation' resource.author.id %}" class="btn btn-success btn-sm">
                        <i class="fas fa-comments me-1"></i>{% trans "Message" %}
                    </a>
                    {% endif %}
                </div>
            </div>
            
            <!-- Category Info -->
            {% if resource.category %}
            <div class="author-card">
                <h5 class="text-success mb-3">
                    <i class="fas fa-folder me-2"></i>{% trans "Category" %}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="author-avatar">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{{ resource.category.name }}</h6>
                        <p class="text-muted mb-0">{{ resource.category.description|truncatewords:10 }}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'resource_by_category' resource.category.id %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-eye me-1"></i>{% trans "Browse Category" %}
                    </a>
                </div>
            </div>
            {% endif %}
            
            <!-- Related Resources -->
            <div class="related-resources">
                <h5 class="text-success mb-3">
                    <i class="fas fa-link me-2"></i>{% trans "Related Resources" %}
                </h5>
                
                <!-- This would be populated by the view with related resources -->
                <div class="related-item">
                    <div class="related-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Understanding Anxiety" %}</h6>
                        <small class="text-muted">{% trans "A comprehensive guide to anxiety disorders" %}</small>
                    </div>
                </div>
                
                <div class="related-item">
                    <div class="related-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Stress Management Techniques" %}</h6>
                        <small class="text-muted">{% trans "Video guide on managing daily stress" %}</small>
                    </div>
                </div>
                
                <div class="related-item">
                    <div class="related-icon">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{% trans "Meditation Audio Guide" %}</h6>
                        <small class="text-muted">{% trans "Guided meditation for beginners" %}</small>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{% url 'resource_list' %}" class="btn btn-outline-success">
                        <i class="fas fa-eye me-2"></i>{% trans "View All Resources" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleBookmark(resourceId) {
    fetch(`/resources/bookmark/${resourceId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.bookmarked) {
            alert('{% trans "Resource bookmarked!" %}');
        } else {
            alert('{% trans "Bookmark removed!" %}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "Error updating bookmark" %}');
    });
}

function showRatingModal() {
    // This would show a rating modal
    alert('{% trans "Rating functionality would be implemented here" %}');
}

function shareResource() {
    if (navigator.share) {
        navigator.share({
            title: '{{ resource.title }}',
            text: '{{ resource.description|truncatewords:20 }}',
            url: window.location.href
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('{% trans "Link copied to clipboard!" %}');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
