from django.db import models
from users.models import CustomUser


class Conversation(models.Model):
    """Private conversation between users"""
    participants = models.ManyToMany<PERSON>ield(CustomUser, related_name='conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        participants_list = list(self.participants.all())
        if len(participants_list) >= 2:
            return f"Conversation between {participants_list[0].username} and {participants_list[1].username}"
        return f"Conversation {self.id}"

    def get_other_participant(self, user):
        """Get the other participant in a 2-person conversation"""
        return self.participants.exclude(id=user.id).first()


class Message(models.Model):
    """Individual message in a conversation"""
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} at {self.created_at}"


class PrivateMessage(models.Model):
    """Private message for real-time chat"""
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='private_messages', null=True, blank=True)
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_private_messages')
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='received_private_messages', null=True, blank=True)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    message_type = models.CharField(max_length=20, default='text', choices=[
        ('text', 'Text'),
        ('file', 'File'),
        ('image', 'Image'),
    ])
    file_attachment = models.FileField(upload_to='chat_files/', null=True, blank=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Private message from {self.sender.username} at {self.created_at}"


class FAQ(models.Model):
    """Frequently Asked Questions for chatbot"""
    question = models.CharField(max_length=500)
    answer = models.TextField()
    category = models.CharField(max_length=100, blank=True)
    keywords = models.TextField(help_text="Comma-separated keywords for matching")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "FAQ"
        verbose_name_plural = "FAQs"

    def __str__(self):
        return self.question[:100]


class ChatbotInteraction(models.Model):
    """Log of chatbot interactions"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=100)  # For anonymous users
    user_message = models.TextField()
    bot_response = models.TextField()
    matched_faq = models.ForeignKey(FAQ, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_display = self.user.username if self.user else f"Anonymous ({self.session_id})"
        return f"Chat with {user_display} at {self.created_at}"


class PsychologistNotification(models.Model):
    """Notifications from psychologists to their followers"""
    NOTIFICATION_TYPES = [
        ('meeting', 'Meeting'),
        ('seminar', 'Seminar'),
        ('workshop', 'Workshop'),
        ('announcement', 'General Announcement'),
    ]

    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'})
    title = models.CharField(max_length=200)
    content = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    scheduled_date = models.DateTimeField(null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    is_online = models.BooleanField(default=False)
    meeting_link = models.URLField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.title} by {self.psychologist.username}"


class NotificationSubscription(models.Model):
    """User subscriptions to psychologist notifications"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='subscriptions')
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   related_name='followers',
                                   limit_choices_to={'role': 'psychologist'})
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'psychologist']

    def __str__(self):
        return f"{self.user.username} follows {self.psychologist.username}"
