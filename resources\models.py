from django.db import models
from users.models import CustomUser
import os


def resource_upload_path(instance, filename):
    """Generate upload path for resources"""
    return f'resources/{instance.category.name}/{filename}'


class ResourceCategory(models.Model):
    """Categories for organizing resources"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Resource Categories"
        ordering = ['name']

    def __str__(self):
        return self.name


class Resource(models.Model):
    """Multimedia resources uploaded by psychologists"""
    RESOURCE_TYPES = [
        ('document', 'Document'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('image', 'Image'),
        ('link', 'External Link'),
    ]

    title = models.Char<PERSON><PERSON>(max_length=200)
    description = models.TextField()
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'})
    category = models.ForeignKey(ResourceCategory, on_delete=models.CASCADE)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)

    # File upload (for documents, videos, audio, images)
    file = models.FileField(upload_to=resource_upload_path, null=True, blank=True)

    # External link (for links)
    external_url = models.URLField(null=True, blank=True)

    # Metadata
    file_size = models.BigIntegerField(null=True, blank=True)  # in bytes
    duration = models.DurationField(null=True, blank=True)  # for video/audio

    # Access control
    is_public = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=False)  # Admin approval required
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL,
                                  null=True, blank=True, related_name='approved_resources')

    # Engagement metrics
    download_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)

    # Tags for search
    tags = models.CharField(max_length=500, blank=True,
                          help_text="Comma-separated tags for search")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    @property
    def file_extension(self):
        """Get file extension"""
        if self.file:
            return os.path.splitext(self.file.name)[1].lower()
        return None

    @property
    def formatted_file_size(self):
        """Format file size in human readable format"""
        if not self.file_size:
            return None

        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"


class ResourceRating(models.Model):
    """User ratings for resources"""
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, related_name='ratings')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    rating = models.PositiveIntegerField(choices=[(i, i) for i in range(1, 6)])  # 1-5 stars
    review = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['resource', 'user']

    def __str__(self):
        return f"{self.user.username} rated {self.resource.title}: {self.rating}/5"


class ResourceDownload(models.Model):
    """Track resource downloads"""
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, related_name='downloads')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    downloaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_display = self.user.username if self.user else "Anonymous"
        return f"{user_display} downloaded {self.resource.title}"


class ResourceBookmark(models.Model):
    """User bookmarks for resources"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='bookmarked_resources')
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, related_name='bookmarks')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'resource']

    def __str__(self):
        return f"{self.user.username} bookmarked {self.resource.title}"
