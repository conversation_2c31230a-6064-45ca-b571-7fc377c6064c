from django.contrib import admin
from .models import (ResourceCategory, Resource, ResourceRating,
                    ResourceDownload, ResourceBookmark)


@admin.register(ResourceCategory)
class ResourceCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    list_editable = ('is_active',)


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    list_display = ('title', 'psychologist', 'category', 'resource_type', 'is_approved', 'is_public', 'created_at')
    list_filter = ('resource_type', 'is_approved', 'is_public', 'category', 'created_at')
    search_fields = ('title', 'description', 'tags', 'psychologist__username')
    list_editable = ('is_approved', 'is_public')
    readonly_fields = ('download_count', 'view_count', 'created_at', 'updated_at')


@admin.register(ResourceRating)
class ResourceRatingAdmin(admin.ModelAdmin):
    list_display = ('resource', 'user', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('resource__title', 'user__username')


@admin.register(ResourceDownload)
class ResourceDownloadAdmin(admin.ModelAdmin):
    list_display = ('resource', 'user', 'ip_address', 'downloaded_at')
    list_filter = ('downloaded_at',)
    search_fields = ('resource__title', 'user__username')
    readonly_fields = ('downloaded_at',)


@admin.register(ResourceBookmark)
class ResourceBookmarkAdmin(admin.ModelAdmin):
    list_display = ('user', 'resource', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'resource__title')
