from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, timedelta, date
import json

from .forms import ConsultationForm, ConsultationStatusForm, AdminAssignPsychologistForm, RatingForm
from .models import Consultation, TimeSlot, ConsultationHistory
from users.models import CustomUser, PsychologistAvailability
from django.db.models import Count, Q
from django.http import JsonResponse
import random


def auto_assign_psychologist(topic, consultation_type):
    """
    Automatically assign a psychologist based on:
    1. Specialty matching with the topic
    2. Current workload (fewer active consultations)
    3. Availability
    4. Experience level
    """
    # Get all available psychologists
    available_psychologists = CustomUser.objects.filter(
        role='psychologist',
        is_verified=True,
        psychologist_approved=True,
        is_active=True
    ).annotate(
        active_consultations=Count(
            'assigned_consultations',
            filter=Q(assigned_consultations__status__in=['pending', 'approved'])
        )
    ).order_by('active_consultations')  # Order by workload (ascending)

    if not available_psychologists.exists():
        return None

    # Topic-based specialty matching
    specialty_keywords = {
        'Anxiety & Stress': ['anxiety', 'stress', 'panic', 'worry'],
        'Depression': ['depression', 'mood', 'sad', 'hopeless'],
        'Relationships': ['relationship', 'couple', 'marriage', 'partner'],
        'Family Issues': ['family', 'parent', 'child', 'sibling'],
        'Work & Career': ['work', 'career', 'job', 'professional'],
        'Self-Esteem': ['confidence', 'self-esteem', 'worth', 'identity'],
        'Trauma': ['trauma', 'ptsd', 'abuse', 'violence'],
        'Other': []
    }

    # Find psychologists with matching specialties
    topic_lower = topic.lower()
    matching_psychologists = []

    for specialty, keywords in specialty_keywords.items():
        if any(keyword in topic_lower for keyword in keywords):
            # Filter psychologists by specialty (if profile has specialization field)
            specialty_matches = available_psychologists.filter(
                profile__specialization__icontains=specialty
            )
            if specialty_matches.exists():
                matching_psychologists.extend(list(specialty_matches[:3]))  # Top 3 by workload
                break

    # If no specialty match found, use general assignment
    if not matching_psychologists:
        matching_psychologists = list(available_psychologists[:5])  # Top 5 by workload

    # For free consultations, prefer less experienced psychologists
    if consultation_type == 'free':
        # Sort by experience (ascending) - newer psychologists handle free consultations
        matching_psychologists.sort(key=lambda p: getattr(p.profile, 'years_experience', 0))
    else:
        # For paid consultations, prefer more experienced psychologists
        matching_psychologists.sort(key=lambda p: getattr(p.profile, 'years_experience', 0), reverse=True)

    # Return the best match (or random from top 3 if multiple good matches)
    if len(matching_psychologists) > 3:
        return random.choice(matching_psychologists[:3])
    elif matching_psychologists:
        return matching_psychologists[0]
    else:
        # Fallback: return any available psychologist
        return available_psychologists.first()


@login_required
def request_consultation(request):
    """Enhanced consultation request with psychologist selection"""
    if not request.user.can_access_consultations():
        messages.error(request, "Please verify your email and age to access consultations.")
        return redirect('enduser_dashboard')

    psychologists = CustomUser.objects.filter(
        role='psychologist',
        is_verified=True,
        psychologist_approved=True
    )

    if request.method == 'POST':
        form = ConsultationForm(request.POST)
        psychologist_id = request.POST.get('psychologist')

        if form.is_valid():
            consultation = form.save(commit=False)
            consultation.user = request.user

            # Handle psychologist assignment
            psychologist_selection = request.POST.get('psychologist_selection', 'auto')

            if psychologist_selection == 'choose' and psychologist_id:
                try:
                    consultation.psychologist = CustomUser.objects.get(
                        id=psychologist_id,
                        role='psychologist',
                        psychologist_approved=True
                    )
                except CustomUser.DoesNotExist:
                    messages.error(request, "Selected psychologist is not available.")
                    return render(request, 'consultations/request_consultation.html', {
                        'form': form, 'psychologists': psychologists
                    })
            else:
                # Auto-assign psychologist based on specialty and availability
                consultation.psychologist = auto_assign_psychologist(
                    consultation.topic,
                    consultation.type
                )

            consultation.save()

            # Log consultation history
            ConsultationHistory.objects.create(
                consultation=consultation,
                previous_status='',
                new_status='pending',
                changed_by=request.user,
                notes='Consultation requested'
            )

            messages.success(request, "Consultation request submitted successfully!")
            return redirect('my_consultations')
    else:
        form = ConsultationForm()

    return render(request, 'consultations/request_consultation.html', {
        'form': form,
        'psychologists': psychologists
    })


@login_required
def calendar_booking(request, psychologist_id):
    """Calendar-based booking interface"""
    psychologist = get_object_or_404(
        CustomUser,
        id=psychologist_id,
        role='psychologist',
        psychologist_approved=True
    )

    # Get available time slots for the next 30 days
    start_date = date.today()
    end_date = start_date + timedelta(days=30)

    available_slots = TimeSlot.objects.filter(
        psychologist=psychologist,
        date__range=[start_date, end_date],
        is_available=True
    ).order_by('date', 'start_time')

    if request.method == 'POST':
        slot_id = request.POST.get('time_slot')
        consultation_type = request.POST.get('type')
        topic = request.POST.get('topic')
        description = request.POST.get('description')

        try:
            time_slot = TimeSlot.objects.get(
                id=slot_id,
                psychologist=psychologist,
                is_available=True
            )

            # Create consultation
            consultation = Consultation.objects.create(
                user=request.user,
                psychologist=psychologist,
                type=consultation_type,
                topic=topic,
                description=description,
                scheduled_date=time_slot.date,
                scheduled_time=time_slot.start_time,
                status='approved'  # Auto-approve calendar bookings
            )

            # Mark time slot as booked
            time_slot.is_available = False
            time_slot.consultation = consultation
            time_slot.save()

            # Log history
            ConsultationHistory.objects.create(
                consultation=consultation,
                previous_status='',
                new_status='approved',
                changed_by=request.user,
                notes='Booked via calendar'
            )

            messages.success(request, "Consultation booked successfully!")
            return redirect('consultation_detail', consultation_id=consultation.id)

        except TimeSlot.DoesNotExist:
            messages.error(request, "Selected time slot is no longer available.")

    return render(request, 'consultations/calendar_booking.html', {
        'psychologist': psychologist,
        'available_slots': available_slots
    })


@login_required
def consultation_detail(request, consultation_id):
    """View consultation details"""
    consultation = get_object_or_404(Consultation, id=consultation_id)

    # Check permissions
    if not (consultation.user == request.user or
            consultation.psychologist == request.user or
            request.user.role == 'admin'):
        messages.error(request, "You don't have permission to view this consultation.")
        return redirect('my_consultations')

    history = consultation.history.all().order_by('-created_at')

    return render(request, 'consultations/consultation_detail.html', {
        'consultation': consultation,
        'history': history
    })


@login_required
def manage_time_slots(request):
    """Psychologist time slot management"""
    if request.user.role != 'psychologist':
        messages.error(request, "Only psychologists can manage time slots.")
        return redirect('enduser_dashboard')

    # Get existing time slots
    time_slots = TimeSlot.objects.filter(
        psychologist=request.user
    ).order_by('date', 'start_time')

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create_slots':
            # Bulk create time slots based on availability
            start_date = request.POST.get('start_date')
            end_date = request.POST.get('end_date')
            selected_days = request.POST.getlist('days')  # List of day numbers

            if start_date and end_date and selected_days:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

                # Get psychologist's availability
                availabilities = PsychologistAvailability.objects.filter(
                    psychologist=request.user,
                    is_active=True,
                    day_of_week__in=selected_days
                )

                created_count = 0
                current_date = start_date

                while current_date <= end_date:
                    day_of_week = current_date.weekday()

                    if str(day_of_week) in selected_days:
                        for availability in availabilities.filter(day_of_week=day_of_week):
                            # Create hourly slots
                            current_time = availability.start_time
                            end_time = availability.end_time

                            while current_time < end_time:
                                next_time = (datetime.combine(date.today(), current_time) +
                                           timedelta(hours=1)).time()

                                if next_time <= end_time:
                                    slot, created = TimeSlot.objects.get_or_create(
                                        psychologist=request.user,
                                        date=current_date,
                                        start_time=current_time,
                                        defaults={
                                            'end_time': next_time,
                                            'is_available': True
                                        }
                                    )
                                    if created:
                                        created_count += 1

                                current_time = next_time

                    current_date += timedelta(days=1)

                messages.success(request, f"Created {created_count} time slots.")

        elif action == 'toggle_slot':
            slot_id = request.POST.get('slot_id')
            try:
                slot = TimeSlot.objects.get(id=slot_id, psychologist=request.user)
                if not slot.consultation:  # Only toggle if not booked
                    slot.is_available = not slot.is_available
                    slot.save()
                    status = "enabled" if slot.is_available else "disabled"
                    messages.success(request, f"Time slot {status}.")
                else:
                    messages.error(request, "Cannot modify booked time slot.")
            except TimeSlot.DoesNotExist:
                messages.error(request, "Time slot not found.")

    # Get psychologist's availability settings
    availabilities = PsychologistAvailability.objects.filter(
        psychologist=request.user
    ).order_by('day_of_week', 'start_time')

    return render(request, 'consultations/manage_time_slots.html', {
        'time_slots': time_slots,
        'availabilities': availabilities
    })


def get_available_slots_api(request, psychologist_id):
    """API endpoint to get available slots for a psychologist"""
    try:
        psychologist = CustomUser.objects.get(
            id=psychologist_id,
            role='psychologist',
            psychologist_approved=True
        )

        date_str = request.GET.get('date')
        if date_str:
            selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            slots = TimeSlot.objects.filter(
                psychologist=psychologist,
                date=selected_date,
                is_available=True
            ).order_by('start_time')

            slots_data = [{
                'id': slot.id,
                'start_time': slot.start_time.strftime('%H:%M'),
                'end_time': slot.end_time.strftime('%H:%M')
            } for slot in slots]

            return JsonResponse({'slots': slots_data})

        return JsonResponse({'error': 'Date parameter required'}, status=400)

    except CustomUser.DoesNotExist:
        return JsonResponse({'error': 'Psychologist not found'}, status=404)
    except ValueError:
        return JsonResponse({'error': 'Invalid date format'}, status=400)


@login_required
def my_consultations(request):
    consultations = request.user.consultations.all()
    return render(request, 'consultations/my_consultations.html', {'consultations': consultations})


def is_psychologist(user):
    return user.is_authenticated and user.role == 'psychologist'


@user_passes_test(is_psychologist)
def psychologist_consultations(request):
    consultations = Consultation.objects.filter(psychologist=request.user)
    # Pre-compute status counts
    total = consultations.count()
    pending = consultations.filter(status='Pending').count()
    completed = consultations.filter(status='Completed').count()
    rejected = consultations.filter(status='Rejected').count()
    return render(request, 'consultations/psychologist_consultations.html', {
        'consultations': consultations,'total': total,
        'pending': pending,
        'completed': completed,
        'rejected': rejected,
        
        })


@user_passes_test(is_psychologist)
def update_consultation_status(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id, psychologist=request.user)
    if request.method == 'POST':
        form = ConsultationStatusForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Consultation status updated.")
            return redirect('psychologist_consultations')
    else:
        form = ConsultationStatusForm(instance=consultation)
    return render(request, 'consultations/update_consultation_status.html', {'form': form, 'consultation': consultation})


@staff_member_required
def admin_consultation_list(request):
    consultations = Consultation.objects.all().order_by('-created_at')
    return render(request, 'consultations/admin_consultations.html', {'consultations': consultations})


@staff_member_required
def assign_psychologist(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id)
    if request.method == 'POST':
        form = AdminAssignPsychologistForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Psychologist reassigned successfully.")
            return redirect('admin_consultation_list')
    else:
        form = AdminAssignPsychologistForm(instance=consultation)
    return render(request, 'consultations/assign_psychologist.html', {'form': form, 'consultation': consultation})

@login_required
def rate_consultation(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id, user=request.user)

    if consultation.status != 'completed':
        messages.error(request, "You can only rate completed consultations.")
        return redirect('my_consultations')

    if request.method == 'POST':
        form = RatingForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Thank you for your feedback!")
            return redirect('my_consultations')
    else:
        form = RatingForm(instance=consultation)

    return render(request, 'consultations/rate_consultation.html', {
        'form': form,
        'consultation': consultation
    })


@login_required
def api_psychologists(request):
    """API endpoint to fetch psychologists for consultation booking"""
    specialization = request.GET.get('specialization', '')

    # Get available psychologists
    psychologists = CustomUser.objects.filter(
        role='psychologist',
        is_verified=True,
        psychologist_approved=True,
        is_active=True
    ).annotate(
        active_consultations=Count(
            'assigned_consultations',
            filter=Q(assigned_consultations__status__in=['pending', 'approved'])
        )
    ).order_by('active_consultations')

    # Filter by specialization if provided
    if specialization:
        psychologists = psychologists.filter(
            profile__specialization__icontains=specialization
        )

    # Prepare response data
    psychologists_data = []
    for psych in psychologists[:10]:  # Limit to 10 results
        # Calculate availability (simplified)
        is_available = psych.active_consultations < 5  # Max 5 active consultations

        # Get average rating (simplified - you might want to implement a proper rating system)
        avg_rating = 4.5 + (hash(psych.username) % 10) / 20  # Mock rating between 4.5-5.0

        psychologists_data.append({
            'id': psych.id,
            'name': psych.get_full_name() or psych.username,
            'specialization': getattr(psych.profile, 'specialization', 'General Psychology') if hasattr(psych, 'profile') else 'General Psychology',
            'experience': f"{getattr(psych.profile, 'years_experience', 5)} years" if hasattr(psych, 'profile') else '5 years',
            'rating': round(avg_rating, 1),
            'available': is_available,
            'active_consultations': psych.active_consultations
        })

    return JsonResponse({
        'psychologists': psychologists_data,
        'count': len(psychologists_data)
    })
