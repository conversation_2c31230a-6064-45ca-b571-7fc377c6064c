from django.shortcuts import render

# Create your views here.
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages

from .forms import ConsultationForm, ConsultationStatusForm, AdminAssignPsychologistForm, RatingForm
from .models import Consultation
from users.models import CustomUser


@login_required
def request_consultation(request):
    if request.method == 'POST':
        form = ConsultationForm(request.POST)
        if form.is_valid():
            consultation = form.save(commit=False)
            consultation.user = request.user
            available_psychologist = CustomUser.objects.filter(role='psychologist', is_verified=True).first()
            consultation.psychologist = available_psychologist
            if available_psychologist is None:
                messages.warning(request, "No psychologist available. Your request is pending assignment.")
            consultation.save()
            return redirect('my_consultations')
    else:
        form = ConsultationForm()
    return render(request, 'consultations/request_consultation.html', {'form': form})


@login_required
def my_consultations(request):
    consultations = request.user.consultations.all()
    return render(request, 'consultations/my_consultations.html', {'consultations': consultations})


def is_psychologist(user):
    return user.is_authenticated and user.role == 'psychologist'


@user_passes_test(is_psychologist)
def psychologist_consultations(request):
    consultations = Consultation.objects.filter(psychologist=request.user)
    # Pre-compute status counts
    total = consultations.count()
    pending = consultations.filter(status='Pending').count()
    completed = consultations.filter(status='Completed').count()
    rejected = consultations.filter(status='Rejected').count()
    return render(request, 'consultations/psychologist_consultations.html', {
        'consultations': consultations,'total': total,
        'pending': pending,
        'completed': completed,
        'rejected': rejected,
        
        })


@user_passes_test(is_psychologist)
def update_consultation_status(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id, psychologist=request.user)
    if request.method == 'POST':
        form = ConsultationStatusForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Consultation status updated.")
            return redirect('psychologist_consultations')
    else:
        form = ConsultationStatusForm(instance=consultation)
    return render(request, 'consultations/update_consultation_status.html', {'form': form, 'consultation': consultation})


@staff_member_required
def admin_consultation_list(request):
    consultations = Consultation.objects.all().order_by('-created_at')
    return render(request, 'consultations/admin_consultations.html', {'consultations': consultations})


@staff_member_required
def assign_psychologist(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id)
    if request.method == 'POST':
        form = AdminAssignPsychologistForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Psychologist reassigned successfully.")
            return redirect('admin_consultation_list')
    else:
        form = AdminAssignPsychologistForm(instance=consultation)
    return render(request, 'consultations/assign_psychologist.html', {'form': form, 'consultation': consultation})

@login_required
def rate_consultation(request, consultation_id):
    consultation = get_object_or_404(Consultation, id=consultation_id, user=request.user)

    if consultation.status != 'completed':
        messages.error(request, "You can only rate completed consultations.")
        return redirect('my_consultations')

    if request.method == 'POST':
        form = RatingForm(request.POST, instance=consultation)
        if form.is_valid():
            form.save()
            messages.success(request, "Thank you for your feedback!")
            return redirect('my_consultations')
    else:
        form = RatingForm(instance=consultation)

    return render(request, 'consultations/rate_consultation.html', {
        'form': form,
        'consultation': consultation
    })
