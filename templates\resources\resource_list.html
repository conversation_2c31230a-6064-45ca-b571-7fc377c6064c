{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Mental Health Resources" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .resources-hero {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .filter-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    .resource-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid var(--gray-200);
    }
    
    .resource-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-xl);
    }
    
    .resource-image {
        height: 200px;
        background: linear-gradient(135deg, var(--success) 0%, var(--info) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 3rem;
    }
    
    .resource-content {
        padding: 1.5rem;
    }
    
    .resource-type-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(255, 255, 255, 0.9);
        color: var(--success);
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .resource-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: var(--gray-600);
    }
    
    .resource-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-resource {
        flex: 1;
        padding: 0.5rem;
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        text-align: center;
        border-left: 4px solid var(--success);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--success);
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="resources-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-book-open me-3"></i>
                    {% trans "Mental Health Resources" %}
                </h1>
                <p class="lead mb-0">{% trans "Access educational materials, self-help guides, and resources created by our professional psychologists" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ page_obj.paginator.count }}</div>
            <div class="text-muted">{% trans "Total Resources" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ categories.count }}</div>
            <div class="text-muted">{% trans "Categories" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">15+</div>
            <div class="text-muted">{% trans "Expert Authors" %}</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">1000+</div>
            <div class="text-muted">{% trans "Downloads" %}</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="search" class="form-label">{% trans "Search Resources" %}</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" name="q" class="form-control" id="search" 
                           placeholder="{% trans 'Search by title, description, or tags...' %}" 
                           value="{{ query }}">
                </div>
            </div>
            
            <div class="col-md-3">
                <label for="category" class="form-label">{% trans "Category" %}</label>
                <select name="category" class="form-select" id="category">
                    <option value="">{% trans "All Categories" %}</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="type" class="form-label">{% trans "Resource Type" %}</label>
                <select name="type" class="form-select" id="type">
                    <option value="">{% trans "All Types" %}</option>
                    {% for type_code, type_name in resource_types %}
                    <option value="{{ type_code }}" {% if selected_type == type_code %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <button type="submit" class="btn btn-success w-100">
                    <i class="fas fa-filter me-1"></i>{% trans "Filter" %}
                </button>
            </div>
        </form>
    </div>

    <!-- Resources Grid -->
    {% if page_obj %}
    <div class="row">
        {% for resource in page_obj %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="resource-card position-relative">
                <div class="resource-image">
                    {% if resource.resource_type == 'video' %}
                        <i class="fas fa-play-circle"></i>
                    {% elif resource.resource_type == 'audio' %}
                        <i class="fas fa-headphones"></i>
                    {% elif resource.resource_type == 'document' %}
                        <i class="fas fa-file-alt"></i>
                    {% elif resource.resource_type == 'image' %}
                        <i class="fas fa-image"></i>
                    {% else %}
                        <i class="fas fa-book"></i>
                    {% endif %}
                </div>
                
                <span class="resource-type-badge">{{ resource.get_resource_type_display }}</span>
                
                <div class="resource-content">
                    <h5 class="mb-2">{{ resource.title }}</h5>
                    <p class="text-muted mb-3">{{ resource.description|truncatewords:20 }}</p>
                    
                    <div class="resource-meta">
                        <span><i class="fas fa-user me-1"></i>{{ resource.author.get_full_name|default:resource.author.username }}</span>
                        <span><i class="fas fa-calendar me-1"></i>{{ resource.created_at|date:"M d, Y" }}</span>
                    </div>
                    
                    {% if resource.category %}
                    <div class="mb-2">
                        <span class="badge bg-success">{{ resource.category.name }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="resource-actions">
                        <a href="{% url 'resource_detail' resource.id %}" class="btn btn-outline-success btn-resource">
                            <i class="fas fa-eye me-1"></i>{% trans "View" %}
                        </a>
                        {% if resource.file %}
                        <a href="{% url 'download_resource' resource.id %}" class="btn btn-success btn-resource">
                            <i class="fas fa-download me-1"></i>{% trans "Download" %}
                        </a>
                        {% endif %}
                        {% if user.is_authenticated %}
                        <button class="btn btn-outline-primary btn-resource" onclick="toggleBookmark({{ resource.id }})">
                            <i class="fas fa-bookmark me-1"></i>{% trans "Save" %}
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Resources pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}">
                    <i class="fas fa-angle-left"></i>
                </a>
            </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}">
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">{% trans "No Resources Found" %}</h5>
        <p class="text-muted">{% trans "Try adjusting your search criteria or browse all resources." %}</p>
        <a href="{% url 'resource_list' %}" class="btn btn-success">
            <i class="fas fa-refresh me-2"></i>{% trans "View All Resources" %}
        </a>
    </div>
    {% endif %}
    
    <!-- Call to Action -->
    {% if user.is_authenticated and user.role == 'psychologist' %}
    <div class="text-center mt-5 p-4 bg-light rounded">
        <h4 class="mb-3">{% trans "Share Your Knowledge" %}</h4>
        <p class="mb-3">{% trans "Help others by sharing your expertise and resources with the community." %}</p>
        <a href="{% url 'upload_resource' %}" class="btn btn-success btn-lg">
            <i class="fas fa-plus me-2"></i>{% trans "Upload Resource" %}
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleBookmark(resourceId) {
    fetch(`/resources/bookmark/${resourceId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.bookmarked) {
            alert('{% trans "Resource bookmarked!" %}');
        } else {
            alert('{% trans "Bookmark removed!" %}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{% trans "Error updating bookmark" %}');
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
