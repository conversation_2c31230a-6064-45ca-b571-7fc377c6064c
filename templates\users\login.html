{% extends "base.html" %}
{% block title %}Login | ECPI{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-body p-5">
                <h2 class="card-title text-center mb-4">Login</h2>
                
                <form method="POST" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            Please enter a correct username and password. Note that both fields may be case-sensitive.
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="id_username" class="form-label">Email</label>
                        <input type="text" name="username" autofocus autocapitalize="none" autocomplete="username" 
                               maxlength="150" class="form-control" required id="id_username">
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_password" class="form-label">Password</label>
                        <input type="password" name="password" autocomplete="current-password" 
                               class="form-control" required id="id_password">
                        <div class="form-text">
                            <a href="#">Forgot password?</a>
                        </div>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <p class="mb-0">Don't have an account? <a href="{% url 'register' %}">Register here</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}