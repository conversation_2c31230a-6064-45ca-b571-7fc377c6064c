{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Dr. {{ psychologist.get_full_name|default:psychologist.username }} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .profile-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .profile-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 3rem 0;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .profile-content {
        position: relative;
        z-index: 2;
    }
    
    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        margin: 0 auto 1.5rem;
        border: 4px solid rgba(255, 255, 255, 0.3);
    }
    
    .rating-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
        margin: 1rem 0;
    }
    
    .star-display {
        color: #fbbf24;
        font-size: 1.2rem;
    }
    
    .info-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    .info-section {
        margin-bottom: 2rem;
    }
    
    .info-section:last-child {
        margin-bottom: 0;
    }
    
    .info-section h5 {
        color: var(--primary-blue);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .specialization-tag {
        display: inline-block;
        background: rgba(37, 99, 235, 0.1);
        color: var(--primary-blue);
        padding: 0.5rem 1rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }
    
    .action-btn {
        padding: 0.875rem 2rem;
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        text-decoration: none;
    }
    
    .btn-book {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
    }
    
    .btn-chat {
        background: var(--success);
        color: var(--white);
    }
    
    .btn-follow {
        background: var(--white);
        color: var(--primary-blue);
        border: 2px solid var(--primary-blue);
    }
    
    .availability-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .availability-day {
        text-align: center;
        padding: 0.75rem;
        background: var(--gray-50);
        border-radius: var(--radius-md);
        border: 1px solid var(--gray-200);
    }
    
    .availability-day.available {
        background: rgba(16, 185, 129, 0.1);
        border-color: var(--success);
        color: var(--success);
    }
    
    .reviews-section {
        margin-top: 2rem;
    }
    
    .review-card {
        background: var(--gray-50);
        padding: 1.5rem;
        border-radius: var(--radius-md);
        margin-bottom: 1rem;
        border-left: 4px solid var(--primary-blue);
    }
    
    .review-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .reviewer-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .reviewer-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--gray-300);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(37, 99, 235, 0.05);
        border-radius: var(--radius-md);
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-content">
                <div class="row align-items-center">
                    <div class="col-lg-4 text-center">
                        <div class="profile-avatar">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h2 class="mb-1">Dr. {{ psychologist.get_full_name|default:psychologist.username }}</h2>
                        <p class="opacity-90 mb-2">{% trans "Licensed Psychologist" %}</p>
                        
                        {% if psychologist.psychologist_rating %}
                        <div class="rating-display">
                            {% for i in "12345" %}
                                {% if forloop.counter <= psychologist.psychologist_rating.average_rating %}
                                    <span class="star-display">★</span>
                                {% else %}
                                    <span class="star-display" style="color: rgba(255,255,255,0.3);">★</span>
                                {% endif %}
                            {% endfor %}
                            <span class="ms-2">{{ psychologist.psychologist_rating.average_rating|floatformat:1 }} ({{ psychologist.psychologist_rating.total_ratings }} {% trans "reviews" %})</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-lg-8">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">{{ psychologist.years_experience|default:"5+" }}</div>
                                <div class="stat-label">{% trans "Years Experience" %}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ psychologist.total_consultations|default:"100+" }}</div>
                                <div class="stat-label">{% trans "Consultations" %}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ psychologist.response_time|default:"< 2h" }}</div>
                                <div class="stat-label">{% trans "Response Time" %}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{% if psychologist.psychologist_rating %}{{ psychologist.psychologist_rating.recommendation_percentage|floatformat:0 }}%{% else %}95%{% endif %}</div>
                                <div class="stat-label">{% trans "Recommend Rate" %}</div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{% url 'book_consultation' psychologist.id %}" class="action-btn btn-book">
                                <i class="fas fa-calendar-plus"></i>
                                {% trans "Book Consultation" %}
                            </a>
                            <a href="{% url 'private_chat' psychologist.id %}" class="action-btn btn-chat">
                                <i class="fas fa-comments"></i>
                                {% trans "Start Chat" %}
                            </a>
                            <a href="{% url 'follow_psychologist' psychologist.id %}" class="action-btn btn-follow">
                                <i class="fas fa-heart"></i>
                                {% trans "Follow" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Info -->
            <div class="col-lg-8">
                <div class="info-card">
                    <!-- About -->
                    <div class="info-section">
                        <h5>
                            <i class="fas fa-user"></i>
                            {% trans "About" %}
                        </h5>
                        <p>{{ psychologist.bio|default:"Professional psychologist dedicated to helping clients achieve mental wellness and personal growth." }}</p>
                    </div>
                    
                    <!-- Specializations -->
                    <div class="info-section">
                        <h5>
                            <i class="fas fa-brain"></i>
                            {% trans "Specializations" %}
                        </h5>
                        <div>
                            {% if psychologist.specializations.all %}
                                {% for spec in psychologist.specializations.all %}
                                    <span class="specialization-tag">{{ spec.name }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="specialization-tag">{% trans "General Psychology" %}</span>
                                <span class="specialization-tag">{% trans "Anxiety & Depression" %}</span>
                                <span class="specialization-tag">{% trans "Relationship Counseling" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Education -->
                    <div class="info-section">
                        <h5>
                            <i class="fas fa-graduation-cap"></i>
                            {% trans "Education & Credentials" %}
                        </h5>
                        <div>
                            <p><strong>{% trans "Degree:" %}</strong> {{ psychologist.education|default:"Ph.D. in Clinical Psychology" }}</p>
                            <p><strong>{% trans "License:" %}</strong> {{ psychologist.license_number|default:"Licensed Clinical Psychologist" }}</p>
                            <p><strong>{% trans "Institution:" %}</strong> {{ psychologist.institution|default:"Addis Ababa University" }}</p>
                        </div>
                    </div>
                    
                    <!-- Languages -->
                    <div class="info-section">
                        <h5>
                            <i class="fas fa-language"></i>
                            {% trans "Languages" %}
                        </h5>
                        <div>
                            {% if psychologist.languages %}
                                {{ psychologist.languages }}
                            {% else %}
                                <span class="specialization-tag">{% trans "English" %}</span>
                                <span class="specialization-tag">{% trans "Amharic" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Reviews Section -->
                <div class="info-card">
                    <div class="reviews-section">
                        <h5 class="mb-4">
                            <i class="fas fa-star text-warning"></i>
                            {% trans "Client Reviews" %}
                        </h5>
                        
                        {% if recent_reviews %}
                            {% for review in recent_reviews %}
                            <div class="review-card">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <div class="reviewer-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{% if review.is_anonymous %}{% trans "Anonymous" %}{% else %}{{ review.client.first_name }}{% endif %}</strong>
                                            <div class="rating-display">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= review.overall_rating %}
                                                        <span class="star-display" style="font-size: 1rem;">★</span>
                                                    {% else %}
                                                        <span class="star-display" style="font-size: 1rem; color: var(--gray-300);">★</span>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                </div>
                                {% if review.positive_feedback %}
                                <p class="mb-0">{{ review.positive_feedback }}</p>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-star fa-2x text-muted mb-3"></i>
                            <p class="text-muted">{% trans "No reviews yet. Be the first to leave a review!" %}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Availability -->
                <div class="info-card">
                    <h5 class="mb-3">
                        <i class="fas fa-calendar-alt"></i>
                        {% trans "Availability" %}
                    </h5>
                    <div class="availability-grid">
                        <div class="availability-day available">
                            <div><strong>{% trans "Mon" %}</strong></div>
                            <small>9AM-5PM</small>
                        </div>
                        <div class="availability-day available">
                            <div><strong>{% trans "Tue" %}</strong></div>
                            <small>9AM-5PM</small>
                        </div>
                        <div class="availability-day available">
                            <div><strong>{% trans "Wed" %}</strong></div>
                            <small>9AM-5PM</small>
                        </div>
                        <div class="availability-day available">
                            <div><strong>{% trans "Thu" %}</strong></div>
                            <small>9AM-5PM</small>
                        </div>
                        <div class="availability-day available">
                            <div><strong>{% trans "Fri" %}</strong></div>
                            <small>9AM-3PM</small>
                        </div>
                        <div class="availability-day">
                            <div><strong>{% trans "Sat" %}</strong></div>
                            <small>{% trans "Closed" %}</small>
                        </div>
                        <div class="availability-day">
                            <div><strong>{% trans "Sun" %}</strong></div>
                            <small>{% trans "Closed" %}</small>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="info-card">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle"></i>
                        {% trans "Session Info" %}
                    </h5>
                    <div class="mb-3">
                        <strong>{% trans "Session Duration:" %}</strong><br>
                        <span class="text-muted">50 minutes</span>
                    </div>
                    <div class="mb-3">
                        <strong>{% trans "Session Fee:" %}</strong><br>
                        <span class="text-primary fw-bold">{{ psychologist.consultation_fee|default:"500" }} ETB</span>
                    </div>
                    <div class="mb-3">
                        <strong>{% trans "Payment Methods:" %}</strong><br>
                        <span class="text-muted">Bank Transfer, Mobile Money</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
