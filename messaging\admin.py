from django.contrib import admin
from .models import (Conversation, Message, FAQ, ChatbotInteraction,
                    PsychologistNotification, NotificationSubscription)


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ('question', 'category', 'is_active', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('question', 'answer', 'keywords')
    list_editable = ('is_active',)


@admin.register(ChatbotInteraction)
class ChatbotInteractionAdmin(admin.ModelAdmin):
    list_display = ('user', 'session_id', 'user_message', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user_message', 'bot_response')
    readonly_fields = ('created_at',)


@admin.register(PsychologistNotification)
class PsychologistNotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'psychologist', 'notification_type', 'scheduled_date', 'is_active')
    list_filter = ('notification_type', 'is_active', 'created_at')
    search_fields = ('title', 'content', 'psychologist__username')


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'created_at', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('sender', 'conversation', 'created_at', 'is_read')
    list_filter = ('is_read', 'created_at')
    search_fields = ('content', 'sender__username')


@admin.register(NotificationSubscription)
class NotificationSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('user', 'psychologist', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'psychologist__username')
