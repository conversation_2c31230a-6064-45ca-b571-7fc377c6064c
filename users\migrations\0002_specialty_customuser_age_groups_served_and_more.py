# Generated by Django 5.2.1 on 2025-06-16 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Specialty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.AddField(
            model_name='customuser',
            name='age_groups_served',
            field=models.CharField(blank=True, help_text='e.g. Children, Teens, Adults', max_length=100),
        ),
        migrations.AddField(
            model_name='customuser',
            name='experience_years',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='portfolio_description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='qualifications',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='rating',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('end_user', 'End User'), ('psychologist', 'Psychologist'), ('admin', 'Admin')], max_length=20),
        ),
        migrations.AddField(
            model_name='customuser',
            name='specialties',
            field=models.ManyToManyField(blank=True, to='users.specialty'),
        ),
    ]
