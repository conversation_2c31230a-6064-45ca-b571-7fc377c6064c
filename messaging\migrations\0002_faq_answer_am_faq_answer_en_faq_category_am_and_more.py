# Generated by Django 5.2.1 on 2025-07-20 19:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='faq',
            name='answer_am',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='faq',
            name='answer_en',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='faq',
            name='category_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='faq',
            name='category_en',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='faq',
            name='question_am',
            field=models.CharField(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='faq',
            name='question_en',
            field=models.Char<PERSON>ield(max_length=500, null=True),
        ),
        migrations.AddField(
            model_name='psychologistnotification',
            name='content_am',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='psychologistnotification',
            name='content_en',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='psychologistnotification',
            name='title_am',
            field=models.CharField(max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='psychologistnotification',
            name='title_en',
            field=models.CharField(max_length=200, null=True),
        ),
    ]
