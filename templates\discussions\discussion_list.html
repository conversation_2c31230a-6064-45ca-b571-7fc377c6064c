{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/discussion.css' %}">
<style>
    .discussion-list-item {
        transition: all 0.2s ease;
        border-left: 4px solid transparent;
    }
    .discussion-list-item:hover {
        transform: translateX(5px);
        border-left-color: #4e73df;
        background-color: #f8f9fa;
    }
    .post-title {
        color: #2d3748;
    }
    .post-meta {
        font-size: 0.85rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="card discussion-card mb-4">
        <div class="card-header discussion-header py-3">
            <h2 class="h4 mb-0"><i class="fas fa-comments me-2"></i>Discussion Forum</h2>
        </div>
        
        <div class="card-body">
            <!-- Search & Filter Card -->
            <div class="card search-filter-card mb-4 border-0">
                <div class="card-body p-3">
                    <form method="get" class="row g-3 align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" name="q" class="form-control" placeholder="Search discussions..." value="{{ query }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                {% for cat in categories %}
                                <option value="{{ cat.id }}" {% if selected_category == cat.id|stringformat:"s" %}selected{% endif %}>
                                    {{ cat.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-1"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- New Post Button -->
            <div class="d-flex justify-content-end mb-4">
                <a href="{% url 'create_post' %}" class="btn btn-success new-post-btn">
                    <i class="fas fa-plus me-2"></i>New Post
                </a>
            </div>
            
            <!-- Posts List -->
            {% if posts %}
            <div class="list-group list-group-flush">
                {% for post in posts %}
                <a href="{% url 'post_detail' post.id %}" class="list-group-item discussion-list-item p-4">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1 me-3">
                            <h5 class="post-title fw-bold mb-2">{{ post.title }}</h5>
                            <p class="mb-2">{{ post.content|truncatewords:30 }}</p>
                            <div class="post-meta text-muted">
                                <span class="badge bg-primary me-2">{{ post.category.name }}</span>
                                <span class="me-2">Posted by {{ post.author.first_name }}</span>
                                <span class="me-2">{{ post.created_at|timesince }} ago</span>
                                <span><i class="far fa-comment me-1"></i> {{ post.replies.count }} replies</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-chevron-right text-muted"></i>
                        </div>
                    </div>
                </a>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if posts.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ posts.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in posts.paginator.page_range %}
                    <li class="page-item {% if num == posts.number %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endfor %}
                    
                    {% if posts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ posts.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="text-center py-5">
                <i class="far fa-comments fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No discussions found</h5>
                <p class="text-muted">Start a new discussion by clicking the button above</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}