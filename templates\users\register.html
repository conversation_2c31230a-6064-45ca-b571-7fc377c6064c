{% extends "base.html" %}
{% block title %}Register | ECPI{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-body p-5">
                <h2 class="card-title text-center mb-4">Create an Account</h2>
                
                <form method="POST" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="id_first_name" class="form-label">First Name</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.first_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="id_last_name" class="form-label">Last Name</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {{ form.last_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_email" class="form-label">Email</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_role" class="form-label">I am a</label>
                        {{ form.role }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="id_password1" class="form-label">Password</label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="invalid-feedback">
                                    {{ form.password1.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="id_password2" class="form-label">Confirm Password</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    {{ form.password2.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="termsCheck" required>
                        <label class="form-check-label" for="termsCheck">
                            I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register
                        </button>
                    </div>
                    
                    <div class="text-center mt-3">
                        <p class="mb-0">Already have an account? <a href="{% url 'login' %}">Login here</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// Add Bootstrap validation classes
(function () {
    'use strict'
    
    // Fetch all the forms we want to apply custom Bootstrap validation styles to
    const forms = document.querySelectorAll('.needs-validation')
    
    // Loop over them and prevent submission
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %}
{% endblock %}