{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Handle Complaints" %} - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .admin-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .admin-header {
        background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
    }
    
    .complaint-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-lg);
        border-left: 4px solid var(--danger);
    }
    
    .complaint-header {
        display: flex;
        justify-content: between;
        align-items: start;
        margin-bottom: var(--spacing-md);
    }
    
    .complaint-info {
        flex: 1;
    }
    
    .complaint-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-shrink: 0;
    }
    
    .severity-badge {
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .severity-low { background: rgba(59, 130, 246, 0.1); color: var(--info); }
    .severity-medium { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
    .severity-high { background: rgba(239, 68, 68, 0.1); color: var(--danger); }
    
    .complaint-content {
        background: var(--gray-50);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin: var(--spacing-md) 0;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
    
    .resolve-form {
        background: var(--gray-50);
        padding: var(--spacing-lg);
        border-radius: var(--radius-md);
        margin-top: var(--spacing-md);
        border: 1px solid var(--gray-200);
    }
    
    .section-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }
    
    .section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .section-content {
        padding: var(--spacing-lg);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        text-align: center;
        border-left: 4px solid var(--danger);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--danger);
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-flag me-3"></i>
                        {% trans "Handle Complaints" %}
                    </h1>
                    <p class="mb-0 opacity-90">{% trans "Review and resolve user reports and complaints" %}</p>
                </div>
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ pending_reports.count }}</div>
                <div class="text-muted">{% trans "Pending Reports" %}</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ resolved_reports.count }}</div>
                <div class="text-muted">{% trans "Resolved Today" %}</div>
            </div>
        </div>

        <!-- Pending Complaints -->
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                    {% trans "Pending Complaints" %}
                </h4>
            </div>
            <div class="section-content">
                {% for report in pending_reports %}
                <div class="complaint-card">
                    <div class="complaint-header">
                        <div class="complaint-info">
                            <div class="user-info">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong>{{ report.reporter.get_full_name|default:report.reporter.username }}</strong>
                                    <small class="text-muted">reported</small>
                                    <strong>{{ report.reported_user.get_full_name|default:report.reported_user.username }}</strong>
                                </div>
                            </div>
                            <div class="d-flex gap-2 mb-2">
                                <span class="severity-badge severity-{{ report.severity|default:'medium' }}">
                                    {{ report.get_severity_display|default:"Medium" }}
                                </span>
                                <span class="badge bg-secondary">{{ report.get_report_type_display }}</span>
                                <small class="text-muted">{{ report.created_at|timesince }} ago</small>
                            </div>
                        </div>
                        <div class="complaint-actions">
                            <button class="btn btn-success btn-sm" onclick="showResolveForm({{ report.id }})">
                                <i class="fas fa-check me-1"></i>{% trans "Resolve" %}
                            </button>
                            <button class="btn btn-info btn-sm" onclick="toggleDetails({{ report.id }})">
                                <i class="fas fa-eye me-1"></i>{% trans "Details" %}
                            </button>
                        </div>
                    </div>
                    
                    <div class="complaint-content">
                        <strong>{% trans "Reason:" %}</strong> {{ report.reason }}
                        {% if report.description %}
                        <br><br>
                        <strong>{% trans "Description:" %}</strong><br>
                        {{ report.description|linebreaks }}
                        {% endif %}
                    </div>
                    
                    <!-- Resolve Form (Hidden by default) -->
                    <div class="resolve-form" id="resolveForm{{ report.id }}" style="display: none;">
                        <form method="post" action="{% url 'custom_admin:resolve_complaint' report.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="adminNotes{{ report.id }}" class="form-label">{% trans "Admin Notes" %}</label>
                                <textarea class="form-control" id="adminNotes{{ report.id }}" name="admin_notes" rows="3" 
                                         placeholder="{% trans 'Add your resolution notes...' %}"></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>{% trans "Mark as Resolved" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideResolveForm({{ report.id }})">
                                    {% trans "Cancel" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>{% trans "No Pending Complaints" %}</h5>
                    <p class="text-muted">{% trans "All complaints have been resolved!" %}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recently Resolved -->
        {% if resolved_reports %}
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    {% trans "Recently Resolved" %}
                </h4>
            </div>
            <div class="section-content">
                {% for report in resolved_reports %}
                <div class="complaint-card" style="border-left-color: var(--success); opacity: 0.8;">
                    <div class="complaint-header">
                        <div class="complaint-info">
                            <div class="user-info">
                                <div class="user-avatar">
                                    <i class="fas fa-check text-success"></i>
                                </div>
                                <div>
                                    <strong>{{ report.reporter.get_full_name|default:report.reporter.username }}</strong>
                                    <small class="text-muted">reported</small>
                                    <strong>{{ report.reported_user.get_full_name|default:report.reported_user.username }}</strong>
                                </div>
                            </div>
                            <div class="d-flex gap-2 mb-2">
                                <span class="badge bg-success">{% trans "Resolved" %}</span>
                                <span class="badge bg-secondary">{{ report.get_report_type_display }}</span>
                                <small class="text-muted">{% trans "Resolved" %} {{ report.resolved_at|timesince }} ago</small>
                            </div>
                        </div>
                    </div>
                    
                    {% if report.admin_notes %}
                    <div class="complaint-content">
                        <strong>{% trans "Resolution Notes:" %}</strong><br>
                        {{ report.admin_notes|linebreaks }}
                        <small class="text-muted d-block mt-2">
                            {% trans "Resolved by" %} {{ report.resolved_by.get_full_name|default:report.resolved_by.username }}
                        </small>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showResolveForm(reportId) {
    document.getElementById('resolveForm' + reportId).style.display = 'block';
}

function hideResolveForm(reportId) {
    document.getElementById('resolveForm' + reportId).style.display = 'none';
}

function toggleDetails(reportId) {
    // This could expand to show more details if needed
    console.log('Toggle details for report:', reportId);
}
</script>
{% endblock %}
