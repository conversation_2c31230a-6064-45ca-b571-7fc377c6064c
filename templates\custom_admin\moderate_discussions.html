{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Moderate Discussions" %} - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .admin-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .admin-header {
        background: linear-gradient(135deg, var(--warning) 0%, #f59e0b 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
    }
    
    .discussion-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-lg);
        border-left: 4px solid var(--warning);
    }
    
    .discussion-header {
        display: flex;
        justify-content: between;
        align-items: start;
        margin-bottom: var(--spacing-md);
    }
    
    .discussion-info {
        flex: 1;
    }
    
    .discussion-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-shrink: 0;
    }
    
    .author-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .author-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
    
    .discussion-content {
        background: var(--gray-50);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin: var(--spacing-md) 0;
        border: 1px solid var(--gray-200);
    }
    
    .flag-badge {
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger);
    }
    
    .section-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }
    
    .section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .section-content {
        padding: var(--spacing-lg);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        text-align: center;
        border-left: 4px solid var(--warning);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--warning);
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-comments me-3"></i>
                        {% trans "Moderate Discussions" %}
                    </h1>
                    <p class="mb-0 opacity-90">{% trans "Review and moderate discussion posts and comments" %}</p>
                </div>
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ flagged_posts.count }}</div>
                <div class="text-muted">{% trans "Flagged Posts" %}</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ recent_posts.count }}</div>
                <div class="text-muted">{% trans "Recent Posts" %}</div>
            </div>
        </div>

        <!-- Flagged Posts -->
        {% if flagged_posts %}
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-flag me-2 text-danger"></i>
                    {% trans "Flagged Posts" %} ({{ flagged_posts.count }})
                </h4>
            </div>
            <div class="section-content">
                {% for post in flagged_posts %}
                <div class="discussion-card">
                    <div class="discussion-header">
                        <div class="discussion-info">
                            <div class="author-info">
                                <div class="author-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong>{{ post.author.get_full_name|default:post.author.username }}</strong>
                                    <small class="text-muted d-block">{{ post.created_at|date:"M d, Y H:i" }}</small>
                                </div>
                            </div>
                            <h6 class="mb-2">{{ post.title }}</h6>
                            <div class="d-flex gap-2 mb-2">
                                <span class="flag-badge">{% trans "Flagged" %}</span>
                                <span class="badge bg-secondary">{{ post.category.name }}</span>
                            </div>
                        </div>
                        <div class="discussion-actions">
                            <button class="btn btn-success btn-sm" onclick="approvePost({{ post.id }})">
                                <i class="fas fa-check me-1"></i>{% trans "Approve" %}
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="removePost({{ post.id }})">
                                <i class="fas fa-trash me-1"></i>{% trans "Remove" %}
                            </button>
                            <button class="btn btn-info btn-sm" onclick="toggleContent({{ post.id }})">
                                <i class="fas fa-eye me-1"></i>{% trans "View" %}
                            </button>
                        </div>
                    </div>
                    
                    <div class="discussion-content" id="content{{ post.id }}" style="display: none;">
                        {{ post.content|linebreaks|truncatewords:50 }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Recent Posts -->
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>
                    {% trans "Recent Posts" %}
                </h4>
            </div>
            <div class="section-content">
                {% for post in recent_posts %}
                <div class="discussion-card" style="border-left-color: var(--info);">
                    <div class="discussion-header">
                        <div class="discussion-info">
                            <div class="author-info">
                                <div class="author-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong>{{ post.author.get_full_name|default:post.author.username }}</strong>
                                    <small class="text-muted d-block">{{ post.created_at|date:"M d, Y H:i" }}</small>
                                </div>
                            </div>
                            <h6 class="mb-2">{{ post.title }}</h6>
                            <div class="d-flex gap-2 mb-2">
                                <span class="badge bg-success">{% trans "Active" %}</span>
                                <span class="badge bg-secondary">{{ post.category.name }}</span>
                                <small class="text-muted">{{ post.comments.count }} {% trans "comments" %}</small>
                            </div>
                        </div>
                        <div class="discussion-actions">
                            <a href="{% url 'post_detail' post.id %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>{% trans "View" %}
                            </a>
                            <button class="btn btn-warning btn-sm" onclick="flagPost({{ post.id }})">
                                <i class="fas fa-flag me-1"></i>{% trans "Flag" %}
                            </button>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h5>{% trans "No Recent Posts" %}</h5>
                    <p class="text-muted">{% trans "No posts have been created recently." %}</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleContent(postId) {
    const content = document.getElementById('content' + postId);
    if (content.style.display === 'none') {
        content.style.display = 'block';
    } else {
        content.style.display = 'none';
    }
}

function approvePost(postId) {
    if (confirm('{% trans "Approve this post?" %}')) {
        // Here you would implement the approve functionality
        alert('{% trans "Post approval functionality would be implemented here." %}');
    }
}

function removePost(postId) {
    if (confirm('{% trans "Remove this post? This action cannot be undone." %}')) {
        // Here you would implement the remove functionality
        alert('{% trans "Post removal functionality would be implemented here." %}');
    }
}

function flagPost(postId) {
    if (confirm('{% trans "Flag this post for review?" %}')) {
        // Here you would implement the flag functionality
        alert('{% trans "Post flagging functionality would be implemented here." %}');
    }
}
</script>
{% endblock %}
