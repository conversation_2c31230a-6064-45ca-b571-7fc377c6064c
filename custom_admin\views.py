from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import user_passes_test
from django.contrib import messages
from django.db.models import Count, Q, Avg, Sum
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from django.http import JsonResponse

from users.models import CustomUser, UserReport
from consultations.models import Consultation
from discussions.models import Post, Category
from resources.models import Resource
from payments.models import Payment
from messaging.models import ChatbotInteraction, FAQ


def is_admin_or_staff(user):
    return user.is_authenticated and (user.is_staff or user.role == 'admin')


@user_passes_test(is_admin_or_staff)
def admin_dashboard(request):
    """Custom admin dashboard with analytics"""

    # Get date range for analytics (last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # User statistics
    total_users = CustomUser.objects.count()
    new_users_this_month = CustomUser.objects.filter(
        date_joined__date__gte=start_date
    ).count()

    psychologists_count = CustomUser.objects.filter(role='psychologist').count()
    approved_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=True
    ).count()
    pending_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=False
    ).count()

    # Consultation statistics
    total_consultations = Consultation.objects.count()
    consultations_this_month = Consultation.objects.filter(
        created_at__date__gte=start_date
    ).count()

    consultation_status_stats = Consultation.objects.values('status').annotate(
        count=Count('id')
    )

    # Discussion statistics
    total_posts = Post.objects.count()
    posts_this_month = Post.objects.filter(
        created_at__date__gte=start_date
    ).count()

    # Resource statistics
    total_resources = Resource.objects.count()
    pending_resources = Resource.objects.filter(is_approved=False).count()

    # Payment statistics
    total_payments = Payment.objects.filter(status='completed').count()
    total_revenue = Payment.objects.filter(status='completed').aggregate(
        total=Sum('amount')
    )['total'] or 0

    # Chatbot statistics
    chatbot_interactions = ChatbotInteraction.objects.filter(
        created_at__date__gte=start_date
    ).count()

    # Reports statistics
    pending_reports = UserReport.objects.filter(resolved=False).count()

    context = {
        'total_users': total_users,
        'new_users_this_month': new_users_this_month,
        'psychologists_count': psychologists_count,
        'approved_psychologists': approved_psychologists,
        'pending_psychologists': pending_psychologists,
        'total_consultations': total_consultations,
        'consultations_this_month': consultations_this_month,
        'consultation_status_stats': consultation_status_stats,
        'total_posts': total_posts,
        'posts_this_month': posts_this_month,
        'total_resources': total_resources,
        'pending_resources': pending_resources,
        'total_payments': total_payments,
        'total_revenue': total_revenue,
        'chatbot_interactions': chatbot_interactions,
        'pending_reports': pending_reports,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'custom_admin/dashboard.html', context)


@user_passes_test(is_admin_or_staff)
def analytics_api(request):
    """API endpoint for dashboard charts"""
    chart_type = request.GET.get('type')

    if chart_type == 'user_registrations':
        # User registrations over time (last 30 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        data = []
        current_date = start_date
        while current_date <= end_date:
            count = CustomUser.objects.filter(
                date_joined__date=current_date
            ).count()
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': count
            })
            current_date += timedelta(days=1)

        return JsonResponse({'data': data})

    elif chart_type == 'consultation_status':
        # Consultation status distribution
        data = list(Consultation.objects.values('status').annotate(
            count=Count('id')
        ))
        return JsonResponse({'data': data})

    elif chart_type == 'monthly_revenue':
        # Monthly revenue (last 12 months)
        end_date = timezone.now().date()
        start_date = end_date.replace(day=1) - timedelta(days=365)

        data = []
        current_date = start_date.replace(day=1)
        while current_date <= end_date:
            next_month = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1)
            revenue = Payment.objects.filter(
                status='completed',
                completed_at__date__gte=current_date,
                completed_at__date__lt=next_month
            ).aggregate(total=Sum('amount'))['total'] or 0

            data.append({
                'month': current_date.strftime('%Y-%m'),
                'revenue': float(revenue)
            })
            current_date = next_month

        return JsonResponse({'data': data})

    return JsonResponse({'error': 'Invalid chart type'}, status=400)
