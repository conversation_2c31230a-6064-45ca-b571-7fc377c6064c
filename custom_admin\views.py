from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import user_passes_test
from django.contrib import messages
from django.db.models import Count, Q, Avg, Sum
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from django.http import JsonResponse

from users.models import CustomUser, UserReport
from consultations.models import Consultation, ConsultationRating
from discussions.models import Post, Category
from resources.models import Resource
from payments.models import Payment
from messaging.models import ChatbotInteraction, FAQ


def is_admin_or_staff(user):
    return user.is_authenticated and (user.is_staff or user.role == 'admin')


@user_passes_test(is_admin_or_staff)
def admin_dashboard(request):
    """Custom admin dashboard with analytics"""

    # Get date range for analytics (last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # User statistics
    total_users = CustomUser.objects.count()
    new_users_this_month = CustomUser.objects.filter(
        date_joined__date__gte=start_date
    ).count()

    psychologists_count = CustomUser.objects.filter(role='psychologist').count()
    approved_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=True
    ).count()
    pending_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=False
    ).count()

    # Consultation statistics
    total_consultations = Consultation.objects.count()
    consultations_this_month = Consultation.objects.filter(
        created_at__date__gte=start_date
    ).count()

    consultation_status_stats = Consultation.objects.values('status').annotate(
        count=Count('id')
    )

    # Discussion statistics
    total_posts = Post.objects.count()
    posts_this_month = Post.objects.filter(
        created_at__date__gte=start_date
    ).count()

    # Resource statistics
    total_resources = Resource.objects.count()
    pending_resources = Resource.objects.filter(is_approved=False).count()

    # Payment statistics
    total_payments = Payment.objects.filter(status='completed').count()
    total_revenue = Payment.objects.filter(status='completed').aggregate(
        total=Sum('amount')
    )['total'] or 0

    # Chatbot statistics
    chatbot_interactions = ChatbotInteraction.objects.filter(
        created_at__date__gte=start_date
    ).count()

    # Reports statistics
    pending_reports = UserReport.objects.filter(resolved=False).count()

    context = {
        'total_users': total_users,
        'new_users_this_month': new_users_this_month,
        'psychologists_count': psychologists_count,
        'approved_psychologists': approved_psychologists,
        'pending_psychologists': pending_psychologists,
        'total_consultations': total_consultations,
        'consultations_this_month': consultations_this_month,
        'consultation_status_stats': consultation_status_stats,
        'total_posts': total_posts,
        'posts_this_month': posts_this_month,
        'total_resources': total_resources,
        'pending_resources': pending_resources,
        'total_payments': total_payments,
        'total_revenue': total_revenue,
        'chatbot_interactions': chatbot_interactions,
        'pending_reports': pending_reports,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'custom_admin/dashboard.html', context)


@user_passes_test(is_admin_or_staff)
def analytics_api(request):
    """API endpoint for dashboard charts"""
    chart_type = request.GET.get('type')

    if chart_type == 'user_registrations':
        # User registrations over time (last 30 days)
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        data = []
        current_date = start_date
        while current_date <= end_date:
            count = CustomUser.objects.filter(
                date_joined__date=current_date
            ).count()
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': count
            })
            current_date += timedelta(days=1)

        return JsonResponse({'data': data})

    elif chart_type == 'consultation_status':
        # Consultation status distribution
        data = list(Consultation.objects.values('status').annotate(
            count=Count('id')
        ))
        return JsonResponse({'data': data})

    elif chart_type == 'monthly_revenue':
        # Monthly revenue (last 12 months)
        end_date = timezone.now().date()
        start_date = end_date.replace(day=1) - timedelta(days=365)

        data = []
        current_date = start_date.replace(day=1)
        while current_date <= end_date:
            next_month = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1)
            revenue = Payment.objects.filter(
                status='completed',
                completed_at__date__gte=current_date,
                completed_at__date__lt=next_month
            ).aggregate(total=Sum('amount'))['total'] or 0

            data.append({
                'month': current_date.strftime('%Y-%m'),
                'revenue': float(revenue)
            })
            current_date = next_month

        return JsonResponse({'data': data})

    return JsonResponse({'error': 'Invalid chart type'}, status=400)


@user_passes_test(is_admin_or_staff)
def moderate_discussions(request):
    """Moderate discussion posts and comments"""
    # Get posts that need moderation
    flagged_posts = Post.objects.filter(is_flagged=True).order_by('-created_at')
    recent_posts = Post.objects.all().order_by('-created_at')[:20]

    context = {
        'flagged_posts': flagged_posts,
        'recent_posts': recent_posts,
    }
    return render(request, 'custom_admin/moderate_discussions.html', context)


@user_passes_test(is_admin_or_staff)
def moderate_resources(request):
    """Moderate resources submitted by psychologists"""
    pending_resources = Resource.objects.filter(is_approved=False).order_by('-created_at')
    recent_resources = Resource.objects.all().order_by('-created_at')[:20]

    context = {
        'pending_resources': pending_resources,
        'recent_resources': recent_resources,
    }
    return render(request, 'custom_admin/moderate_resources.html', context)


@user_passes_test(is_admin_or_staff)
def manage_users(request):
    """Manage users, psychologists, and handle complaints"""
    # Get user statistics
    total_users = CustomUser.objects.count()
    active_users = CustomUser.objects.filter(is_active=True).count()
    banned_users = CustomUser.objects.filter(is_active=False).count()

    # Psychologist management
    pending_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=False
    ).order_by('-date_joined')

    approved_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=True
    ).order_by('-date_joined')[:10]

    # Recent users
    recent_users = CustomUser.objects.all().order_by('-date_joined')[:20]

    context = {
        'total_users': total_users,
        'active_users': active_users,
        'banned_users': banned_users,
        'pending_psychologists': pending_psychologists,
        'approved_psychologists': approved_psychologists,
        'recent_users': recent_users,
    }
    return render(request, 'custom_admin/manage_users.html', context)


@user_passes_test(is_admin_or_staff)
def handle_complaints(request):
    """Handle user reports and complaints"""
    pending_reports = UserReport.objects.filter(resolved=False).order_by('-created_at')
    resolved_reports = UserReport.objects.filter(resolved=True).order_by('-resolved_at')[:20]

    context = {
        'pending_reports': pending_reports,
        'resolved_reports': resolved_reports,
    }
    return render(request, 'custom_admin/handle_complaints.html', context)


@user_passes_test(is_admin_or_staff)
def approve_psychologist(request, user_id):
    """Approve a psychologist"""
    if request.method == 'POST':
        psychologist = get_object_or_404(CustomUser, id=user_id, role='psychologist')
        psychologist.psychologist_approved = True
        psychologist.save()

        messages.success(request, f'Psychologist {psychologist.get_full_name()} has been approved.')

    return redirect('manage_users')


@user_passes_test(is_admin_or_staff)
def ban_user(request, user_id):
    """Ban/unban a user"""
    if request.method == 'POST':
        user = get_object_or_404(CustomUser, id=user_id)
        user.is_active = not user.is_active
        user.save()

        action = "unbanned" if user.is_active else "banned"
        messages.success(request, f'User {user.username} has been {action}.')

    return redirect('manage_users')


@user_passes_test(is_admin_or_staff)
def approve_resource(request, resource_id):
    """Approve a resource"""
    if request.method == 'POST':
        resource = get_object_or_404(Resource, id=resource_id)
        resource.is_approved = True
        resource.save()

        messages.success(request, f'Resource "{resource.title}" has been approved.')

    return redirect('moderate_resources')


@user_passes_test(is_admin_or_staff)
def resolve_complaint(request, report_id):
    """Resolve a user complaint"""
    if request.method == 'POST':
        report = get_object_or_404(UserReport, id=report_id)
        report.resolved = True
        report.resolved_at = timezone.now()
        report.resolved_by = request.user
        report.admin_notes = request.POST.get('admin_notes', '')
        report.save()

        messages.success(request, 'Complaint has been resolved.')

    return redirect('handle_complaints')
