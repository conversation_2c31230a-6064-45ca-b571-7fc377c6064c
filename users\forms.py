from django import forms
from django.contrib.auth.forms import UserCreationForm, PasswordResetForm
from django.core.exceptions import ValidationError
from datetime import date
from .models import CustomUser, UserReport, PsychologistAvailability

class UserRegisterForm(UserCreationForm):
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text="You must be at least 14 years old to register."
    )

    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'email', 'date_of_birth', 'role', 'password1', 'password2']

    def clean_date_of_birth(self):
        dob = self.cleaned_data.get('date_of_birth')
        if dob:
            today = date.today()
            age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
            if age < 14:
                raise ValidationError("You must be at least 14 years old to register.")
        return dob

    def clean_role(self):
        role = self.cleaned_data.get('role')
        # Prevent direct admin registration
        if role == 'admin':
            raise ValidationError("Admin accounts cannot be created through registration.")
        return role

    def save(self, commit=True):
        user = super().save(commit=False)
        user.username = self.cleaned_data['email']  # Use email as username
        user.age_verified = True  # Set to True since we validated age
        if commit:
            user.save()
        return user


class PsychologistRegistrationForm(UserRegisterForm):
    psychologist_documents = forms.FileField(
        required=True,
        help_text="Upload your professional credentials (PDF format preferred)"
    )
    qualifications = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4}),
        help_text="Describe your qualifications and certifications"
    )
    experience_years = forms.IntegerField(
        min_value=0,
        help_text="Years of professional experience"
    )

    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'email', 'date_of_birth', 'qualifications',
                 'experience_years', 'psychologist_documents', 'password1', 'password2']

    def save(self, commit=True):
        user = super().save(commit=False)
        user.role = 'psychologist'  # Force psychologist role
        user.psychologist_approved = False  # Requires admin approval
        if commit:
            user.save()
        return user


class CustomPasswordResetForm(PasswordResetForm):
    def get_users(self, email):
        """Override to use our custom user model"""
        active_users = CustomUser.objects.filter(
            email__iexact=email,
            is_active=True,
        )
        return (u for u in active_users if u.has_usable_password())


class UserReportForm(forms.ModelForm):
    class Meta:
        model = UserReport
        fields = ['report_type', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Please describe the issue...'})
        }


class AvailabilityForm(forms.ModelForm):
    class Meta:
        model = PsychologistAvailability
        fields = ['day_of_week', 'start_time', 'end_time']
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'type': 'time'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        if start_time and end_time and start_time >= end_time:
            raise ValidationError("End time must be after start time.")

        return cleaned_data
