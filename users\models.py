from django.db import models
from django.contrib.auth.models import AbstractUser

class CustomUser(AbstractUser):
    ROLE_CHOICES = (
        ('end_user', 'End User'),
        ('psychologist', 'Psychologist'),
        ('admin', 'Admin'),
    )

    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    is_verified = models.BooleanField(default=False)

    #  New Fields for Psychologist Port<PERSON>lio
    specialties = models.ManyToManyField('Specialty', blank=True)
    age_groups_served = models.CharField(max_length=100, blank=True, help_text="e.g. Children, Teens, Adults")
    experience_years = models.PositiveIntegerField(null=True, blank=True)
    qualifications = models.TextField(blank=True)
    portfolio_description = models.TextField(blank=True)
    rating = models.FloatField(default=0.0)

class Specialty(models.Model):
    name = models.CharField(max_length=50)

    def __str__(self):
        return self.name

