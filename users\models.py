from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid

class CustomUser(AbstractUser):
    ROLE_CHOICES = (
        ('guest', 'Guest'),
        ('end_user', 'End User'),
        ('psychologist', 'Psychologist'),
        ('admin', 'Admin'),
    )

    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='guest')
    is_verified = models.BooleanField(default=False)
    email_verification_token = models.UUIDField(default=uuid.uuid4, editable=False)
    email_verified_at = models.DateTimeField(null=True, blank=True)

    # Age verification (FR-01)
    date_of_birth = models.DateField(null=True, blank=True)
    age_verified = models.BooleanField(default=False)

    # Psychologist approval (FR-04)
    psychologist_approved = models.<PERSON><PERSON>anField(default=False)
    psychologist_approval_date = models.DateTime<PERSON>ield(null=True, blank=True)
    psychologist_documents = models.FileField(upload_to='psychologist_docs/', null=True, blank=True)

    # Password reset
    password_reset_token = models.UUIDField(null=True, blank=True)
    password_reset_expires = models.DateTimeField(null=True, blank=True)

    #  New Fields for Psychologist Portfolio
    specialties = models.ManyToManyField('Specialty', blank=True)
    age_groups_served = models.CharField(max_length=100, blank=True, help_text="e.g. Children, Teens, Adults")
    experience_years = models.PositiveIntegerField(null=True, blank=True)
    qualifications = models.TextField(blank=True)
    portfolio_description = models.TextField(blank=True)
    rating = models.FloatField(default=0.0)

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def age(self):
        """Calculate age from date of birth"""
        if self.date_of_birth:
            today = timezone.now().date()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None

    @property
    def is_age_eligible(self):
        """Check if user is 14+ years old"""
        return self.age and self.age >= 14

    def can_access_consultations(self):
        """Check if user can access consultation features"""
        return self.role in ['end_user', 'psychologist', 'admin'] and self.is_verified and self.age_verified

    def can_moderate_content(self):
        """Check if user can moderate content"""
        return self.role == 'admin' or (self.role == 'psychologist' and self.psychologist_approved)

class Specialty(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Specialties"

    def __str__(self):
        return self.name


class PsychologistAvailability(models.Model):
    """Time slots when psychologists are available"""
    psychologist = models.ForeignKey(CustomUser, on_delete=models.CASCADE,
                                   limit_choices_to={'role': 'psychologist'})
    day_of_week = models.IntegerField(choices=[
        (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
        (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
    ])
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['psychologist', 'day_of_week', 'start_time']

    def __str__(self):
        return f"{self.psychologist.username} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"


class UserReport(models.Model):
    """User reports for content moderation"""
    REPORT_TYPES = [
        ('inappropriate_content', 'Inappropriate Content'),
        ('spam', 'Spam'),
        ('harassment', 'Harassment'),
        ('fake_profile', 'Fake Profile'),
        ('other', 'Other'),
    ]

    reporter = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='reports_made')
    reported_user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='reports_received')
    report_type = models.CharField(max_length=50, choices=REPORT_TYPES)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='reports_resolved')
    resolved_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Report: {self.reported_user.username} by {self.reporter.username}"

