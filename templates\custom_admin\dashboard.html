{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Admin Dashboard" %} - Psychology Platform{% endblock %}

{% block extra_css %}
<style>
    .admin-dashboard {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
    }
    
    .stats-card {
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: var(--gray-600);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .stats-change {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .stats-change.positive {
        color: var(--success);
    }
    
    .stats-change.negative {
        color: var(--danger);
    }
    
    .chart-container {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--spacing-lg);
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }
    
    .quick-action-btn {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        text-decoration: none;
        color: var(--gray-700);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
    }
    
    .quick-action-btn:hover {
        background: var(--primary-blue);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .quick-action-icon {
        font-size: 1.5rem;
        margin-right: var(--spacing-md);
        width: 40px;
        text-align: center;
    }
    
    .admin-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: var(--spacing-xl) 0;
        margin-bottom: var(--spacing-xl);
        border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-dashboard">
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        {% trans "Admin Dashboard" %}
                    </h1>
                    <p class="mb-0 opacity-75">
                        {% trans "Welcome back! Here's what's happening with your platform." %}
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="text-white-50">
                        <i class="fas fa-calendar me-2"></i>
                        {{ start_date|date:"M d" }} - {{ end_date|date:"M d, Y" }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="{% url 'custom_admin:manage_users' %}" class="quick-action-btn">
                <i class="fas fa-user-check quick-action-icon"></i>
                <div>
                    <div class="fw-bold">Manage Users</div>
                    <small class="text-muted">{{ pending_psychologists }} pending approvals</small>
                </div>
            </a>

            <a href="{% url 'custom_admin:handle_complaints' %}" class="quick-action-btn">
                <i class="fas fa-flag quick-action-icon"></i>
                <div>
                    <div class="fw-bold">Handle Complaints</div>
                    <small class="text-muted">{{ pending_reports }} pending reports</small>
                </div>
            </a>

            <a href="{% url 'custom_admin:moderate_resources' %}" class="quick-action-btn">
                <i class="fas fa-file-check quick-action-icon"></i>
                <div>
                    <div class="fw-bold">Moderate Resources</div>
                    <small class="text-muted">{{ pending_resources }} pending approval</small>
                </div>
            </a>

            <a href="{% url 'custom_admin:moderate_discussions' %}" class="quick-action-btn">
                <i class="fas fa-comments quick-action-icon"></i>
                <div>
                    <div class="fw-bold">Moderate Discussions</div>
                    <small class="text-muted">Review posts & comments</small>
                </div>
            </a>

            <a href="/admin/" class="quick-action-btn">
                <i class="fas fa-cogs quick-action-icon"></i>
                <div>
                    <div class="fw-bold">Django Admin</div>
                    <small class="text-muted">Advanced settings</small>
                </div>
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number">{{ total_users }}</div>
                    <div class="stats-label">{% trans "Total Users" %}</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +{{ new_users_this_month }} {% trans "this month" %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number">{{ approved_psychologists }}</div>
                    <div class="stats-label">{% trans "Active Psychologists" %}</div>
                    <div class="stats-change">
                        {{ pending_psychologists }} {% trans "pending approval" %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number">{{ total_consultations }}</div>
                    <div class="stats-label">{% trans "Total Consultations" %}</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +{{ consultations_this_month }} {% trans "this month" %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-number">{{ total_revenue|floatformat:0 }} ETB</div>
                    <div class="stats-label">{% trans "Total Revenue" %}</div>
                    <div class="stats-change">
                        {{ total_payments }} {% trans "completed payments" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        {% trans "User Registrations (Last 30 Days)" %}
                    </h5>
                    <canvas id="userRegistrationsChart" height="100"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        {% trans "Consultation Status" %}
                    </h5>
                    <canvas id="consultationStatusChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        {% trans "Monthly Revenue" %}
                    </h5>
                    <canvas id="monthlyRevenueChart" height="150"></canvas>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        {% trans "Platform Statistics" %}
                    </h5>
                    <div class="row">
                        <div class="col-6 text-center py-3">
                            <div class="h4 text-primary mb-1">{{ total_posts }}</div>
                            <small class="text-muted">{% trans "Discussion Posts" %}</small>
                        </div>
                        <div class="col-6 text-center py-3">
                            <div class="h4 text-primary mb-1">{{ total_resources }}</div>
                            <small class="text-muted">{% trans "Resources" %}</small>
                        </div>
                        <div class="col-6 text-center py-3">
                            <div class="h4 text-primary mb-1">{{ chatbot_interactions }}</div>
                            <small class="text-muted">{% trans "AI Interactions" %}</small>
                        </div>
                        <div class="col-6 text-center py-3">
                            <div class="h4 text-primary mb-1">{{ posts_this_month }}</div>
                            <small class="text-muted">{% trans "Posts This Month" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeUserRegistrationsChart();
    initializeConsultationStatusChart();
    initializeMonthlyRevenueChart();
});

function initializeUserRegistrationsChart() {
    fetch('{% url "custom_admin:analytics_api" %}?type=user_registrations')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('userRegistrationsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.data.map(item => new Date(item.date).toLocaleDateString()),
                    datasets: [{
                        label: '{% trans "New Users" %}',
                        data: data.data.map(item => item.count),
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        });
}

function initializeConsultationStatusChart() {
    fetch('{% url "custom_admin:analytics_api" %}?type=consultation_status')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('consultationStatusChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.data.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1)),
                    datasets: [{
                        data: data.data.map(item => item.count),
                        backgroundColor: [
                            '#2563eb',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
}

function initializeMonthlyRevenueChart() {
    fetch('{% url "custom_admin:analytics_api" %}?type=monthly_revenue')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('monthlyRevenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.data.map(item => item.month),
                    datasets: [{
                        label: '{% trans "Revenue (ETB)" %}',
                        data: data.data.map(item => item.revenue),
                        backgroundColor: 'rgba(37, 99, 235, 0.8)',
                        borderColor: '#2563eb',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
}
</script>
{% endblock %}
