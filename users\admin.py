from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, Specialty, PsychologistAvailability, UserReport


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'is_verified', 'psychologist_approved')
    list_filter = ('role', 'is_verified', 'psychologist_approved', 'is_active')
    search_fields = ('username', 'email', 'first_name', 'last_name')

    fieldsets = UserAdmin.fieldsets + (
        ('Custom Fields', {
            'fields': ('role', 'is_verified', 'date_of_birth', 'age_verified',
                      'psychologist_approved', 'psychologist_documents',
                      'specialties', 'experience_years', 'qualifications')
        }),
    )


@admin.register(Specialty)
class SpecialtyAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)


@admin.register(PsychologistAvailability)
class PsychologistAvailabilityAdmin(admin.ModelAdmin):
    list_display = ('psychologist', 'get_day_of_week_display', 'start_time', 'end_time', 'is_active')
    list_filter = ('day_of_week', 'is_active')
    search_fields = ('psychologist__username', 'psychologist__first_name', 'psychologist__last_name')


@admin.register(UserReport)
class UserReportAdmin(admin.ModelAdmin):
    list_display = ('reporter', 'reported_user', 'report_type', 'created_at', 'resolved')
    list_filter = ('report_type', 'resolved', 'created_at')
    search_fields = ('reporter__username', 'reported_user__username')
    readonly_fields = ('created_at',)
