from django.contrib import admin

# Register your models here.
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, Specialty

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    model = CustomUser
    list_display = ('username', 'email', 'role', 'is_verified', 'is_active')
    filter_horizontal = ('specialties',)

    def get_fieldsets(self, request, obj=None):
        # Get default fieldsets first
        fieldsets = super().get_fieldsets(request, obj)

        # Add 'is_verified' under Permissions block
        personal_info = list(fieldsets[1][1].get('fields', []))
        if 'is_verified' not in personal_info:
            personal_info.append('is_verified')
            fieldsets[1][1]['fields'] = tuple(personal_info)

        # If psychologist, show their extra fields
        if obj and obj.role == 'psychologist':
            fieldsets += (
                ('Psychologist Info', {
                    'fields': (
                        'specialties',  # ✅ bring back this line
                        'age_groups_served',
                        'experience_years',
                        'qualifications',
                        'portfolio_description',
                        'rating',
                    )
                }),
            )

        return fieldsets


@admin.register(Specialty)
class SpecialtyAdmin(admin.ModelAdmin):
    list_display = ['name']
