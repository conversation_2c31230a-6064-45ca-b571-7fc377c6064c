/*!
 * Chart.js v4.4.0
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Chart=e()}(this,(function(){"use strict";var t=Object.freeze({__proto__:null,get Colors(){return So},get Decimation(){return Ao},get Filler(){return Ko},get Legend(){return Jo},get SubTitle(){return Qo},get Title(){return Go},get Tooltip(){return la}});
// Chart.js minified content would go here - this is a placeholder
// In a real implementation, you would download the actual Chart.js file
console.log("Chart.js placeholder loaded");
window.Chart = {
    register: function() { console.log("Chart.register called"); },
    Chart: function() { console.log("Chart constructor called"); }
};
