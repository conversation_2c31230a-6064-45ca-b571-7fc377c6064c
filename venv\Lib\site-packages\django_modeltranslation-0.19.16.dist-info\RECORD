django_modeltranslation-0.19.16.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_modeltranslation-0.19.16.dist-info/METADATA,sha256=-DNphwICcPbZFyTStqs4UUa5eo52bOkz_n255JfOgwA,3518
django_modeltranslation-0.19.16.dist-info/RECORD,,
django_modeltranslation-0.19.16.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_modeltranslation-0.19.16.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
django_modeltranslation-0.19.16.dist-info/licenses/AUTHORS.rst,sha256=ZRCUUS4zWrEoantSPWebJAYZ4S8jYUrvh7nKVSRqmp4,1049
django_modeltranslation-0.19.16.dist-info/licenses/LICENSE.txt,sha256=Q8K-7-SxJQPYDFK3vL9Ef7f0TdJanbKPYsreNiQ9dVs,1543
modeltranslation/__init__.py,sha256=yd3gAzb4T_GuNrObsbKOpa-oTFaNPW-cAsrsKZLu2JM,105
modeltranslation/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/__pycache__/_compat.cpython-313.pyc,,
modeltranslation/__pycache__/_typing.cpython-313.pyc,,
modeltranslation/__pycache__/admin.cpython-313.pyc,,
modeltranslation/__pycache__/apps.cpython-313.pyc,,
modeltranslation/__pycache__/decorators.cpython-313.pyc,,
modeltranslation/__pycache__/fields.cpython-313.pyc,,
modeltranslation/__pycache__/forms.cpython-313.pyc,,
modeltranslation/__pycache__/manager.cpython-313.pyc,,
modeltranslation/__pycache__/models.cpython-313.pyc,,
modeltranslation/__pycache__/settings.cpython-313.pyc,,
modeltranslation/__pycache__/thread_context.cpython-313.pyc,,
modeltranslation/__pycache__/translator.cpython-313.pyc,,
modeltranslation/__pycache__/utils.cpython-313.pyc,,
modeltranslation/__pycache__/widgets.cpython-313.pyc,,
modeltranslation/_compat.py,sha256=Va5MuhugBU6gsiRapCWbF1jcX-KQMvNyNqqcTBSKWWQ,2242
modeltranslation/_typing.py,sha256=ZdTY3L0HHOHdDybQi_d6rsWRYCwrcKdS2CFIo2HrfH4,1455
modeltranslation/admin.py,sha256=LQuYsbpPpIq3tvxy6bqrmyIoWe7uGJoAFIzdJfIYz6U,19657
modeltranslation/apps.py,sha256=F9YCRiRU97NaNEeHXbxEaUSKo5KAQwRrAk1eJxiFzlk,296
modeltranslation/decorators.py,sha256=Wb_1bKGJb_NMDCRxZAjGe8q9_Ia2iLsg3By60D3uKCg,1347
modeltranslation/fields.py,sha256=-FCh9s3gZPqHgdaAOzcAs7u10Tjvvi1Oz0EO_vr6vlk,21646
modeltranslation/forms.py,sha256=HUMOgkAT7TXcIlGJOUgxt3kkG7gczvAgVGrmkcpW-H4,1609
modeltranslation/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/management/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/management/commands/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/management/commands/__pycache__/loaddata.cpython-313.pyc,,
modeltranslation/management/commands/__pycache__/sync_translation_fields.cpython-313.pyc,,
modeltranslation/management/commands/__pycache__/update_translation_fields.cpython-313.pyc,,
modeltranslation/management/commands/loaddata.py,sha256=YScZsAlV26oSwqL1-bKLGsfqcaiEbRY1aFiMUWiC09Y,2423
modeltranslation/management/commands/sync_translation_fields.py,sha256=HlObJgzOvM90qOCHRMDEHFE3O1YZ0JJP2QHQhDQvJHE,5859
modeltranslation/management/commands/update_translation_fields.py,sha256=HAMX9U7ysoByuT4Yv1wNH16NJMk1akvzX0ljg9FMFu4,4144
modeltranslation/manager.py,sha256=GcH5MAFcspYQWJmZg-29UXUXXCUeZX_ODNd5xEIGmcU,25362
modeltranslation/models.py,sha256=w90bbMgK5NRqTzc5z-pqfF7CJOl8NeBEiO9MwHBFqCk,3143
modeltranslation/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/settings.py,sha256=4zr6tqI4OWy8LrgdUHo6XUl1MVGGYTUjojgmxzHx-9o,3824
modeltranslation/static/modeltranslation/css/tabbed_translation_fields.css,sha256=YhcYhzsl8-8Iy0jNzMnDfEQB3H9SWKq-6BZzNjcm2eY,4250
modeltranslation/static/modeltranslation/js/clearable_inputs.js,sha256=oYwlDqmcX1WvriCkA7Gz5yoNJCKprEXw9ciiwaIsH84,492
modeltranslation/static/modeltranslation/js/force_jquery.js,sha256=TRhEr9SdhwwMcjPHUl89lhQeo27kTy9DLAK0zyKEB9E,65
modeltranslation/static/modeltranslation/js/tabbed_translation_fields.js,sha256=hiNe1_cNB4Fv9-QfqW_iPH2kSlS2ovyAeEEvkAOpGtg,18925
modeltranslation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/tests/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/tests/__pycache__/admin.cpython-313.pyc,,
modeltranslation/tests/__pycache__/models.cpython-313.pyc,,
modeltranslation/tests/__pycache__/project_translation.cpython-313.pyc,,
modeltranslation/tests/__pycache__/settings.cpython-313.pyc,,
modeltranslation/tests/__pycache__/test_admin.cpython-313.pyc,,
modeltranslation/tests/__pycache__/test_admin_views.cpython-313.pyc,,
modeltranslation/tests/__pycache__/test_compat.cpython-313.pyc,,
modeltranslation/tests/__pycache__/test_runtime_typing.cpython-313.pyc,,
modeltranslation/tests/__pycache__/test_third_party.cpython-313.pyc,,
modeltranslation/tests/__pycache__/tests.cpython-313.pyc,,
modeltranslation/tests/__pycache__/translation.cpython-313.pyc,,
modeltranslation/tests/__pycache__/urls.cpython-313.pyc,,
modeltranslation/tests/admin.py,sha256=Yr55Vrj7UxPG9wEkAIkgE9EpFe_qrjnakdBvh11zkbY,213
modeltranslation/tests/fixtures/fixture.json,sha256=RGA4Lw7Ts_5UFwqC2tnB70PC_EijdGXhQp52infT9pc,153
modeltranslation/tests/migrations/0001_initial.py,sha256=WyfZG_2z1o4i2jNSpXwWVdSxlwmp8_Gkw5d6Ym8W-qc,60991
modeltranslation/tests/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/tests/migrations/__pycache__/0001_initial.cpython-313.pyc,,
modeltranslation/tests/migrations/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/tests/models.py,sha256=Y3iQsgUEU1_hv33ySWGUQuWUc_DjM7-DqahbpQYmpHI,14440
modeltranslation/tests/project_translation.py,sha256=44YEchPqGcC3G2c3MLmRRN4pE3_pI4MEuTpxl3ZAMo8,192
modeltranslation/tests/settings.py,sha256=rqf7QpjotYdm4mkgOuCFjwDNL7lDvSud7L9Z6WyswM4,3249
modeltranslation/tests/test_admin.py,sha256=fMoLb2jtD86-7ZByCxW_iEGpeQJUFTYHfltI__prvzU,21526
modeltranslation/tests/test_admin_views.py,sha256=G14g0RIMGGAUcJqr5FqzcjaKAfAAOC-1GuX9tRSFrws,759
modeltranslation/tests/test_app/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
modeltranslation/tests/test_app/__pycache__/__init__.cpython-313.pyc,,
modeltranslation/tests/test_app/__pycache__/models.cpython-313.pyc,,
modeltranslation/tests/test_app/__pycache__/translation.cpython-313.pyc,,
modeltranslation/tests/test_app/models.py,sha256=B7dcuJp_aCCn7kj380I0xYuNSHW4ixQQBjPVbHY2fjc,330
modeltranslation/tests/test_app/translation.py,sha256=SZhOiCiPTe-sCYv7Vmk72Tbv8e6OgOdkuMWz-7MIMks,183
modeltranslation/tests/test_compat.py,sha256=JSkW3ZysOg3_vMGWpQ_7ULmWwIB3sRoZdYKrYPDSPtc,595
modeltranslation/tests/test_runtime_typing.py,sha256=YztMC3CT_eB4pXXZ3sFDvcs92kE-WsKQq4rAypi2JK0,749
modeltranslation/tests/test_third_party.py,sha256=38Vg2DizmqYUhQpFFX6Qf9Jt1prkNazPq_hO7QuL3-8,1359
modeltranslation/tests/tests.py,sha256=wlRZIkpmIi79qXhCfjC0FJtv83uDp8YJ4-fpl_Gk_gs,134869
modeltranslation/tests/translation.py,sha256=MwlF5pII-Nd04gttVwG3YLOIN2xKA8T1v-oH_1BCb9E,7396
modeltranslation/tests/urls.py,sha256=NR4c2ybRQNjbtJwi-PTsYB9oacVuVZsIRHo7ZnA0WIc,118
modeltranslation/thread_context.py,sha256=Nj10HyQpz7DrXnQDYri-IduJLcN3d9tEIIyFsUlAAFk,1236
modeltranslation/translator.py,sha256=BZm2C3oSEAvmf4oTFL8TfoTvcVRjYnlLK2xw2Bu4YXw,29643
modeltranslation/utils.py,sha256=npXmmrtKFt6qVAxDaqEK4UoUXo7PE5q4fNvXp0LbozM,8035
modeltranslation/widgets.py,sha256=jOfIG1CBr4nFVcdXoIibZlZ7wqK6wHg-3ZFe2zR3c9E,4215
