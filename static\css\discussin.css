/* Discussion Forum Styling */
.discussion-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.discussion-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 0.5rem rgba(0, 0, 0, 0.1);
}

.discussion-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
}

.post-author-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.reply-author-img {
    width: 32px;
    height: 32px;
    object-fit: cover;
}

.category-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.reply-form textarea {
    min-height: 100px;
}

.search-filter-card {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.new-post-btn {
    transition: all 0.2s;
}

.new-post-btn:hover {
    transform: translateY(-2px);
}