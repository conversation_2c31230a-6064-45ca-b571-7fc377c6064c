{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Moderate Resources" %} - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .admin-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .admin-header {
        background: linear-gradient(135deg, var(--info) 0%, #0ea5e9 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
    }
    
    .resource-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-lg);
        border-left: 4px solid var(--info);
    }
    
    .resource-header {
        display: flex;
        justify-content: between;
        align-items: start;
        margin-bottom: var(--spacing-md);
    }
    
    .resource-info {
        flex: 1;
    }
    
    .resource-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-shrink: 0;
    }
    
    .resource-preview {
        background: var(--gray-50);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin: var(--spacing-md) 0;
        border: 1px solid var(--gray-200);
    }
    
    .resource-meta {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        flex-wrap: wrap;
    }
    
    .author-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .author-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
    
    .resource-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
        background: rgba(59, 130, 246, 0.1);
        color: var(--info);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-pending { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
    .status-approved { background: rgba(16, 185, 129, 0.1); color: var(--success); }
    
    .section-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }
    
    .section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .section-content {
        padding: var(--spacing-lg);
    }
    
    .file-info {
        background: var(--gray-100);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-sm);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 0.875rem;
        margin-top: var(--spacing-sm);
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-file-check me-3"></i>
                        {% trans "Moderate Resources" %}
                    </h1>
                    <p class="mb-0 opacity-90">{% trans "Review and approve resources submitted by psychologists" %}</p>
                </div>
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>

        <!-- Pending Resources -->
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-clock me-2 text-warning"></i>
                    {% trans "Pending Approval" %} ({{ pending_resources.count }})
                </h4>
            </div>
            <div class="section-content">
                {% for resource in pending_resources %}
                <div class="resource-card">
                    <div class="resource-header">
                        <div class="resource-info">
                            <div class="resource-meta">
                                <div class="author-info">
                                    <div class="author-avatar">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                    <div>
                                        <strong>{{ resource.author.get_full_name|default:resource.author.username }}</strong>
                                        <small class="text-muted d-block">{{ resource.created_at|date:"M d, Y" }}</small>
                                    </div>
                                </div>
                                <span class="resource-type-badge">{{ resource.get_resource_type_display }}</span>
                                <span class="status-badge status-pending">{% trans "Pending" %}</span>
                            </div>
                            <h5 class="mb-2">{{ resource.title }}</h5>
                        </div>
                        <div class="resource-actions">
                            <form method="post" action="{% url 'custom_admin:approve_resource' resource.id %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('{% trans "Approve this resource?" %}')">
                                    <i class="fas fa-check me-1"></i>{% trans "Approve" %}
                                </button>
                            </form>
                            <button class="btn btn-danger btn-sm" onclick="rejectResource({{ resource.id }})">
                                <i class="fas fa-times me-1"></i>{% trans "Reject" %}
                            </button>
                            <button class="btn btn-info btn-sm" onclick="togglePreview({{ resource.id }})">
                                <i class="fas fa-eye me-1"></i>{% trans "Preview" %}
                            </button>
                        </div>
                    </div>
                    
                    <div class="resource-preview" id="preview{{ resource.id }}" style="display: none;">
                        <h6>{% trans "Description:" %}</h6>
                        <p>{{ resource.description|linebreaks }}</p>
                        
                        {% if resource.file %}
                        <div class="file-info">
                            <i class="fas fa-file"></i>
                            <span>{{ resource.file.name|default:"Attached file" }}</span>
                            <a href="{{ resource.file.url }}" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                <i class="fas fa-download me-1"></i>{% trans "Download" %}
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if resource.url %}
                        <div class="mt-2">
                            <strong>{% trans "External Link:" %}</strong>
                            <a href="{{ resource.url }}" target="_blank" class="ms-2">{{ resource.url }}</a>
                        </div>
                        {% endif %}
                        
                        {% if resource.tags %}
                        <div class="mt-2">
                            <strong>{% trans "Tags:" %}</strong>
                            {% for tag in resource.tags.all %}
                                <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>{% trans "No Pending Resources" %}</h5>
                    <p class="text-muted">{% trans "All resources have been reviewed!" %}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recently Approved -->
        {% if recent_resources %}
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    {% trans "Recently Approved" %}
                </h4>
            </div>
            <div class="section-content">
                {% for resource in recent_resources %}
                {% if resource.is_approved %}
                <div class="resource-card" style="border-left-color: var(--success); opacity: 0.8;">
                    <div class="resource-header">
                        <div class="resource-info">
                            <div class="resource-meta">
                                <div class="author-info">
                                    <div class="author-avatar">
                                        <i class="fas fa-user-md text-success"></i>
                                    </div>
                                    <div>
                                        <strong>{{ resource.author.get_full_name|default:resource.author.username }}</strong>
                                        <small class="text-muted d-block">{{ resource.created_at|date:"M d, Y" }}</small>
                                    </div>
                                </div>
                                <span class="resource-type-badge">{{ resource.get_resource_type_display }}</span>
                                <span class="status-badge status-approved">{% trans "Approved" %}</span>
                            </div>
                            <h6 class="mb-1">{{ resource.title }}</h6>
                            <p class="text-muted mb-0">{{ resource.description|truncatewords:20 }}</p>
                        </div>
                        <div class="resource-actions">
                            <a href="{% url 'resource_detail' resource.id %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>{% trans "View" %}
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePreview(resourceId) {
    const preview = document.getElementById('preview' + resourceId);
    if (preview.style.display === 'none') {
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

function rejectResource(resourceId) {
    if (confirm('{% trans "Are you sure you want to reject this resource?" %}')) {
        // Here you would implement the reject functionality
        // For now, we'll just show an alert
        alert('{% trans "Resource rejection functionality would be implemented here." %}');
    }
}
</script>
{% endblock %}
