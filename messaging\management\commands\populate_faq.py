from django.core.management.base import BaseCommand
from messaging.models import FAQ


class Command(BaseCommand):
    help = 'Populate FAQ data for the chatbot'

    def handle(self, *args, **options):
        faqs = [
            {
                'category': 'General',
                'question': 'What is this platform about?',
                'answer': 'This is an Ethiopian Psychological Consultation Platform that connects users with licensed psychologists for mental health support and counseling services.',
                'keywords': 'platform, about, what is, service, psychology, mental health'
            },
            {
                'category': 'Consultations',
                'question': 'How do I book a consultation?',
                'answer': 'To book a consultation: 1) Register and log in to your account, 2) Browse our verified psychologists, 3) Select a psychologist that fits your needs, 4) Choose an available time slot, 5) Complete the payment process.',
                'keywords': 'book, consultation, appointment, schedule, booking, how to book'
            },
            {
                'category': 'Consultations',
                'question': 'What types of consultations are available?',
                'answer': 'We offer various types of consultations including individual therapy, couples counseling, family therapy, anxiety and depression treatment, stress management, and career counseling.',
                'keywords': 'types, consultation types, therapy, counseling, individual, couples, family'
            },
            {
                'category': 'Payment',
                'question': 'What are your pricing options?',
                'answer': 'We offer both free and paid consultations. Pricing varies by psychologist and consultation type, typically ranging from 300-800 ETB per session. You can view specific pricing when selecting a psychologist.',
                'keywords': 'price, cost, payment, pricing, fee, money, birr, etb'
            },
            {
                'category': 'Payment',
                'question': 'What payment methods do you accept?',
                'answer': 'We accept various payment methods including bank transfers, mobile money (Telebirr, M-Birr), and online payment through our secure payment gateway.',
                'keywords': 'payment methods, bank transfer, mobile money, telebirr, m-birr, pay'
            },
            {
                'category': 'Psychologists',
                'question': 'How do I find the right psychologist?',
                'answer': 'You can browse psychologists by specialization, rating, availability, and language. Each psychologist has a detailed profile showing their experience, education, and client reviews to help you make the best choice.',
                'keywords': 'find psychologist, choose psychologist, therapist, counselor, specialist'
            },
            {
                'category': 'Psychologists',
                'question': 'Are the psychologists licensed?',
                'answer': 'Yes, all psychologists on our platform are licensed professionals who have been verified and approved by our admin team. They have proper credentials and experience in mental health services.',
                'keywords': 'licensed, verified, qualified, credentials, professional, approved'
            },
            {
                'category': 'Account',
                'question': 'How do I create an account?',
                'answer': 'Click on "Register" in the top menu, fill in your details including name, email, and password. You must be at least 14 years old to create an account. After registration, verify your email to activate your account.',
                'keywords': 'register, create account, sign up, registration, new account'
            },
            {
                'category': 'Account',
                'question': 'I forgot my password, what should I do?',
                'answer': 'Click on "Forgot Password" on the login page, enter your email address, and we will send you a password reset link. Follow the instructions in the email to create a new password.',
                'keywords': 'forgot password, reset password, password recovery, login problem'
            },
            {
                'category': 'Privacy',
                'question': 'Is my information confidential?',
                'answer': 'Yes, we take privacy very seriously. All consultations and personal information are kept strictly confidential. We follow professional ethics and data protection standards.',
                'keywords': 'privacy, confidential, secure, private, data protection, safety'
            },
            {
                'category': 'Technical',
                'question': 'What if I have technical problems?',
                'answer': 'If you experience technical issues, try refreshing the page or clearing your browser cache. For persistent problems, contact our support team through the contact form or email.',
                'keywords': 'technical problem, bug, error, not working, support, help'
            },
            {
                'category': 'Languages',
                'question': 'What languages are supported?',
                'answer': 'Our platform supports both English and Amharic (አማርኛ). You can switch languages using the language selector in the top navigation menu.',
                'keywords': 'language, amharic, english, አማርኛ, translate, switch language'
            },
            {
                'category': 'Resources',
                'question': 'What resources are available?',
                'answer': 'We provide various mental health resources including articles, videos, self-help guides, and educational materials created by our psychologists. These are available in the Resources section.',
                'keywords': 'resources, articles, videos, materials, self-help, guides, education'
            },
            {
                'category': 'Emergency',
                'question': 'What if I have a mental health emergency?',
                'answer': 'If you are experiencing a mental health emergency or having thoughts of self-harm, please contact emergency services immediately (991 in Ethiopia) or go to your nearest hospital emergency room.',
                'keywords': 'emergency, crisis, suicide, self-harm, urgent, help now, 991'
            }
        ]

        created_count = 0
        for faq_data in faqs:
            faq, created = FAQ.objects.get_or_create(
                question=faq_data['question'],
                defaults={
                    'category': faq_data['category'],
                    'answer': faq_data['answer'],
                    'keywords': faq_data['keywords'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                self.stdout.write(f"Created FAQ: {faq.question}")

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} FAQ entries')
        )
