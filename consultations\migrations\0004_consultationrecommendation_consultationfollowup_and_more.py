# Generated by Django 5.2.1 on 2025-07-20 21:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('consultations', '0003_consultation_completed_at_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsultationRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recommendation_type', models.CharField(choices=[('follow_up', 'Follow-up Consultation'), ('medication', 'Medication Referral'), ('specialist', 'Specialist Referral'), ('therapy', 'Therapy Sessions'), ('lifestyle', 'Lifestyle Changes'), ('resources', 'Educational Resources')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('urgency', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('referred_doctor', models.CharField(blank=True, help_text='Doctor or clinic name for medication referral', max_length=200)),
                ('doctor_contact', models.CharField(blank=True, help_text='Contact information', max_length=100)),
                ('suggested_date', models.DateTimeField(blank=True, null=True)),
                ('follow_up_notes', models.TextField(blank=True)),
                ('is_acknowledged', models.BooleanField(default=False, help_text='Client has seen the recommendation')),
                ('is_completed', models.BooleanField(default=False, help_text='Client has acted on the recommendation')),
                ('client_notes', models.TextField(blank=True, help_text="Client's notes or feedback")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_recommendations', to=settings.AUTH_USER_MODEL)),
                ('consultation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='consultations.consultation')),
                ('psychologist', models.ForeignKey(limit_choices_to={'role': 'psychologist'}, on_delete=django.db.models.deletion.CASCADE, related_name='given_recommendations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ConsultationFollowUp',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Why is follow-up needed?')),
                ('goals', models.TextField(help_text='What should be achieved in follow-up?')),
                ('suggested_frequency', models.CharField(blank=True, help_text="e.g., 'Weekly for 4 weeks'", max_length=100)),
                ('preferred_time_slots', models.TextField(blank=True, help_text='Preferred days/times')),
                ('is_urgent', models.BooleanField(default=False)),
                ('client_agreed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('original_consultation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='follow_ups', to='consultations.consultation')),
                ('scheduled_consultation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='follow_up_from', to='consultations.consultation')),
                ('recommendation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='follow_up_details', to='consultations.consultationrecommendation')),
            ],
        ),
        migrations.CreateModel(
            name='MedicationReferral',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('condition_diagnosed', models.CharField(max_length=200)),
                ('recommended_medication_type', models.CharField(blank=True, max_length=200)),
                ('dosage_notes', models.TextField(blank=True)),
                ('duration_notes', models.TextField(blank=True)),
                ('side_effects_warning', models.TextField(blank=True)),
                ('contraindications', models.TextField(blank=True)),
                ('hospital_clinic_name', models.CharField(max_length=200)),
                ('doctor_name', models.CharField(blank=True, max_length=100)),
                ('doctor_specialization', models.CharField(blank=True, max_length=100)),
                ('contact_phone', models.CharField(blank=True, max_length=20)),
                ('contact_email', models.EmailField(blank=True, max_length=254)),
                ('address', models.TextField(blank=True)),
                ('preferred_appointment_time', models.CharField(blank=True, max_length=100)),
                ('insurance_accepted', models.BooleanField(default=True)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('follow_up_required', models.BooleanField(default=True)),
                ('follow_up_period', models.CharField(blank=True, help_text="e.g., '2 weeks', '1 month'", max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('recommendation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='medication_details', to='consultations.consultationrecommendation')),
            ],
        ),
        migrations.CreateModel(
            name='PsychologistRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('total_ratings', models.IntegerField(default=0)),
                ('avg_professionalism', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('avg_communication', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('avg_helpfulness', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('avg_punctuality', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('five_star_count', models.IntegerField(default=0)),
                ('four_star_count', models.IntegerField(default=0)),
                ('three_star_count', models.IntegerField(default=0)),
                ('two_star_count', models.IntegerField(default=0)),
                ('one_star_count', models.IntegerField(default=0)),
                ('recommendation_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('return_client_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('psychologist', models.OneToOneField(limit_choices_to={'role': 'psychologist'}, on_delete=django.db.models.deletion.CASCADE, related_name='psychologist_rating', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ConsultationRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('professionalism', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('communication', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('helpfulness', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('punctuality', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('positive_feedback', models.TextField(blank=True, help_text='What did you like about this consultation?')),
                ('improvement_feedback', models.TextField(blank=True, help_text='What could be improved?')),
                ('additional_comments', models.TextField(blank=True)),
                ('would_recommend', models.BooleanField(blank=True, null=True)),
                ('would_book_again', models.BooleanField(blank=True, null=True)),
                ('is_anonymous', models.BooleanField(default=False, help_text='Hide your name in public reviews')),
                ('is_public', models.BooleanField(default=True, help_text='Show this review publicly')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='given_ratings', to=settings.AUTH_USER_MODEL)),
                ('consultation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='detailed_rating', to='consultations.consultation')),
                ('psychologist', models.ForeignKey(limit_choices_to={'role': 'psychologist'}, on_delete=django.db.models.deletion.CASCADE, related_name='received_ratings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('consultation', 'client')},
            },
        ),
    ]
