from django.urls import path
from . import views
from .views import psychologist_list, psychologist_detail

urlpatterns = [
    # Authentication
    path('register/', views.register_view, name='register'),
    path('register/psychologist/', views.psychologist_register_view, name='psychologist_register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # Email verification
    path('verify-email/<uuid:token>/', views.verify_email, name='verify_email'),

    # Password reset
    path('password-reset/', views.password_reset_request, name='password_reset_request'),
    path('password-reset/<uuid:token>/', views.password_reset_confirm, name='password_reset_confirm'),

    # Dashboards
    path('psychologist/dashboard/', views.psychologist_dashboard, name='psychologist_dashboard'),
    path('enduser/dashboard/', views.enduser_dashboard, name='enduser_dashboard'),

    # Psychologist listings
    path('psychologists/', psychologist_list, name='psychologist_list'),
    path('psychologists/<int:user_id>/', psychologist_detail, name='psychologist_detail'),

    # User reporting
    path('report-user/<int:user_id>/', views.report_user, name='report_user'),

    # Admin functions
    path('admin/psychologist-approvals/', views.admin_psychologist_approvals, name='admin_psychologist_approvals'),
    path('admin/user-reports/', views.admin_user_reports, name='admin_user_reports'),
]
