{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/discussion.css' %}">
<style>
    .post-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    .post-content {
        font-size: 1.05rem;
        line-height: 1.7;
        color: #333;
    }
    .reply-item {
        transition: background-color 0.2s;
        padding: 1rem;
        border-radius: 0.5rem;
    }
    .reply-item:hover {
        background-color: #f8f9fa;
    }
    .reply-form textarea {
        min-height: 120px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Back button -->
            <a href="{% url 'discussion_list' %}" class="btn btn-outline-secondary mb-4">
                <i class="fas fa-arrow-left me-2"></i> Back to Discussions
            </a>

            <!-- Main Post Card -->
            <div class="card post-card mb-4 border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="h4 mb-0">{{ post.title }}</h2>
                        <span class="badge bg-light text-primary">{{ post.category }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="fw-bold">{{ post.author.first_name }}</span>
                        <span class="text-muted ms-2">{{ post.created_at|date:"F j, Y H:i" }}</span>
                    </div>
                    
                    <div class="post-content mb-4">
                        {{ post.content|linebreaks }}
                    </div>
                    
                    <div class="post-meta d-flex text-muted">
                        <div class="me-3">
                            <i class="far fa-comment me-1"></i> {{ replies.count }} replies
                        </div>
                        <div>
                            <i class="far fa-bookmark me-1"></i> Save
                        </div>
                    </div>
                </div>
            </div>

            <!-- Replies Section -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light py-3">
                    <h3 class="h5 mb-0"><i class="fas fa-reply me-2"></i> Replies</h3>
                </div>
                
                <div class="card-body">
                    {% if replies %}
                        <div class="reply-list">
                            {% for reply in replies %}
                            <div class="reply-item mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="fw-bold">{{ reply.author.first_name }}</span>
                                    <small class="text-muted">{{ reply.created_at|timesince }} ago</small>
                                </div>
                                <p class="mb-0">{{ reply.content }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="far fa-comment-dots fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No replies yet. Be the first to respond!</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Reply Form -->
            {% if request.user.is_authenticated %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-3">
                    <h4 class="h5 mb-0"><i class="fas fa-edit me-2"></i> Add Your Reply</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.content.id_for_label }}" class="form-label">Your Reply</label>
                            {{ form.content }}
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i> Post Reply
                        </button>
                    </form>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                You must <a href="{% url 'login' %}?next={{ request.path }}" class="alert-link">log in</a> to reply.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}