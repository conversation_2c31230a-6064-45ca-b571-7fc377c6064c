/* Custom styles */
body {
    background-color: #f8f9fa;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.navbar-brand {
    font-size: 1.5rem;
}

.hero-section {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 2rem;
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #0d6efd;
}

/* Form styling */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Discussion cards */
.discussion-card {
    border-left: 4px solid #0d6efd;
}

/* Consultation status badges */
.status-badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    border-radius: 0.25rem;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.status-approved {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-completed {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-rejected {
    background-color: #f8d7da;
    color: #842029;
}