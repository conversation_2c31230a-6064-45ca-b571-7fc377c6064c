{% extends 'base.html' %}

{% block title %}AI Assistant - Psychology Platform{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fas fa-robot me-2"></i>Psychology Platform AI Assistant</h4>
                    <small>Ask me anything about our platform, consultations, or mental health resources!</small>
                </div>
                <div class="card-body">
                    <div id="chat-messages" class="chat-messages mb-3" style="height: 400px; overflow-y: auto; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;">
                        <div class="message bot-message mb-3">
                            <div class="d-flex">
                                <div class="avatar me-2">
                                    <i class="fas fa-robot text-primary"></i>
                                </div>
                                <div class="message-content">
                                    <strong>AI Assistant:</strong> Hello! I'm here to help you with questions about our psychology platform. How can I assist you today?
                                </div>
                            </div>
                        </div>
                        
                        {% for interaction in recent_interactions %}
                        <div class="message user-message mb-2">
                            <div class="d-flex justify-content-end">
                                <div class="message-content bg-light p-2 rounded">
                                    <strong>You:</strong> {{ interaction.user_message }}
                                </div>
                                <div class="avatar ms-2">
                                    <i class="fas fa-user text-secondary"></i>
                                </div>
                            </div>
                        </div>
                        <div class="message bot-message mb-3">
                            <div class="d-flex">
                                <div class="avatar me-2">
                                    <i class="fas fa-robot text-primary"></i>
                                </div>
                                <div class="message-content">
                                    <strong>AI Assistant:</strong> {{ interaction.bot_response }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="input-group">
                        <input type="text" id="user-input" class="form-control" placeholder="Type your message here..." maxlength="500">
                        <button class="btn btn-primary" type="button" id="send-button">
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Quick questions:</strong>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 quick-question" data-question="How do I book a consultation?">Book consultation</button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 quick-question" data-question="What are your pricing options?">Pricing</button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 quick-question" data-question="How do I find a psychologist?">Find psychologist</button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 quick-question" data-question="What resources are available?">Resources</button>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-messages {
    background-color: #f8f9fa;
}

.message {
    margin-bottom: 10px;
}

.bot-message .message-content {
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.user-message .message-content {
    background-color: #007bff;
    color: white;
    padding: 10px;
    border-radius: 10px;
}

.avatar {
    width: 30px;
    text-align: center;
}

.quick-question {
    cursor: pointer;
}
</style>

<script>
// Function to get CSRF token from cookies
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const quickQuestions = document.querySelectorAll('.quick-question');
    
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function addMessage(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'} mb-3`;
        
        if (isUser) {
            messageDiv.innerHTML = `
                <div class="d-flex justify-content-end">
                    <div class="message-content bg-primary text-white p-2 rounded">
                        <strong>You:</strong> ${message}
                    </div>
                    <div class="avatar ms-2">
                        <i class="fas fa-user text-secondary"></i>
                    </div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="d-flex">
                    <div class="avatar me-2">
                        <i class="fas fa-robot text-primary"></i>
                    </div>
                    <div class="message-content">
                        <strong>AI Assistant:</strong> ${message}
                    </div>
                </div>
            `;
        }
        
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }
    
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;
        
        addMessage(message, true);
        userInput.value = '';
        sendButton.disabled = true;
        
        // Show typing indicator
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message mb-3 typing-indicator';
        typingDiv.innerHTML = `
            <div class="d-flex">
                <div class="avatar me-2">
                    <i class="fas fa-robot text-primary"></i>
                </div>
                <div class="message-content">
                    <em>AI Assistant is typing...</em>
                </div>
            </div>
        `;
        chatMessages.appendChild(typingDiv);
        scrollToBottom();
        
        // Send to API
        fetch('{% url "chatbot_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            // Remove typing indicator
            chatMessages.removeChild(typingDiv);
            
            if (data.response) {
                addMessage(data.response);
            } else {
                addMessage('Sorry, I encountered an error. Please try again.');
            }
        })
        .catch(error => {
            // Remove typing indicator
            chatMessages.removeChild(typingDiv);
            addMessage('Sorry, I encountered an error. Please try again.');
        })
        .finally(() => {
            sendButton.disabled = false;
        });
    }
    
    sendButton.addEventListener('click', sendMessage);
    
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    quickQuestions.forEach(button => {
        button.addEventListener('click', function() {
            userInput.value = this.dataset.question;
            sendMessage();
        });
    });
    
    // Initial scroll to bottom
    scrollToBottom();
});
</script>
{% endblock %}
