{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Chat Assistant - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .chat-hero {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .chat-container {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        height: 600px;
        display: flex;
        flex-direction: column;
    }
    
    .chat-header {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: var(--gray-50);
    }
    
    .message {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }
    
    .message.user {
        justify-content: flex-end;
    }
    
    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        margin: 0 0.5rem;
        flex-shrink: 0;
    }
    
    .bot-avatar {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
    }
    
    .user-avatar {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
    }
    
    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: var(--radius-lg);
        position: relative;
    }
    
    .bot-message .message-content {
        background: var(--white);
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
    }
    
    .user-message .message-content {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
    }
    
    .chat-input {
        padding: 1rem;
        background: var(--white);
        border-top: 1px solid var(--gray-200);
    }
    
    .faq-section {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .faq-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .faq-card {
        background: var(--gray-50);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-md);
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
    }
    
    .faq-card:hover {
        background: var(--success);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .faq-card h6 {
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .faq-card p {
        margin: 0;
        font-size: 0.875rem;
        opacity: 0.8;
    }
    
    .quick-actions {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }
    
    .quick-action {
        background: var(--gray-100);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .quick-action:hover {
        background: var(--success);
        color: var(--white);
        border-color: var(--success);
    }
    
    .typing-indicator {
        display: none;
        align-items: center;
        gap: 0.5rem;
        color: var(--gray-600);
        font-style: italic;
    }
    
    .typing-dots {
        display: flex;
        gap: 0.25rem;
    }
    
    .typing-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: var(--gray-400);
        animation: typing 1.4s infinite ease-in-out;
    }
    
    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes typing {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }
    
    .welcome-message {
        background: linear-gradient(135deg, var(--info) 0%, #0ea5e9 100%);
        color: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 1rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="chat-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-comments me-3"></i>
                    {% trans "Chat Assistant" %}
                </h1>
                <p class="lead mb-0">{% trans "Get instant answers to your questions about mental health services and consultations" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Chat Interface -->
        <div class="col-lg-8">
            <div class="chat-container">
                <div class="chat-header">
                    <div class="d-flex align-items-center">
                        <div class="message-avatar bot-avatar me-3">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">{% trans "Chat Assistant" %}</h5>
                            <small class="opacity-75">{% trans "Online and ready to help" %}</small>
                        </div>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <!-- Welcome Message -->
                    <div class="welcome-message">
                        <h6 class="mb-2">👋 {% trans "Welcome to our Chat Assistant!" %}</h6>
                        <p class="mb-0">{% trans "I'm here to help you with questions about our mental health services. You can ask me anything or choose from the common questions below." %}</p>
                    </div>
                    
                    <!-- Initial Bot Message -->
                    <div class="message bot-message">
                        <div class="message-avatar bot-avatar">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="message-content">
                            {% trans "Hello! How can I assist you today? You can type your question or select from the common topics below." %}
                        </div>
                    </div>
                    
                    <!-- Typing Indicator -->
                    <div class="typing-indicator" id="typing-indicator">
                        <div class="message-avatar bot-avatar">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                        <span>{% trans "Assistant is typing..." %}</span>
                    </div>
                </div>
                
                <div class="chat-input">
                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <span class="quick-action" onclick="sendQuickMessage('{% trans "How do I book a consultation?" %}')">
                            📅 {% trans "Book Consultation" %}
                        </span>
                        <span class="quick-action" onclick="sendQuickMessage('{% trans "What are your pricing options?" %}')">
                            💰 {% trans "Pricing" %}
                        </span>
                        <span class="quick-action" onclick="sendQuickMessage('{% trans "How do I find the right psychologist?" %}')">
                            👨‍⚕️ {% trans "Find Psychologist" %}
                        </span>
                        <span class="quick-action" onclick="sendQuickMessage('{% trans "Is my information confidential?" %}')">
                            🔒 {% trans "Privacy" %}
                        </span>
                    </div>
                    
                    <div class="input-group">
                        <input type="text" id="user-input" class="form-control" 
                               placeholder="{% trans 'Type your message...' %}" 
                               autocomplete="off">
                        <button class="btn btn-success" type="button" id="send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- FAQ Sidebar -->
        <div class="col-lg-4">
            <div class="faq-section">
                <h5 class="text-success mb-3">
                    <i class="fas fa-question-circle me-2"></i>
                    {% trans "Common Questions" %}
                </h5>
                <p class="text-muted mb-3">{% trans "Click on any question to get an instant answer:" %}</p>
                
                <div class="faq-grid">
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "What is this platform about?" %}')">
                        <h6>🏥 {% trans "About Platform" %}</h6>
                        <p>{% trans "Learn about our services" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "How do I book a consultation?" %}')">
                        <h6>📅 {% trans "Book Consultation" %}</h6>
                        <p>{% trans "Step-by-step booking guide" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "What are your pricing options?" %}')">
                        <h6>💰 {% trans "Pricing" %}</h6>
                        <p>{% trans "View consultation fees" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "How do I find the right psychologist?" %}')">
                        <h6>👨‍⚕️ {% trans "Find Psychologist" %}</h6>
                        <p>{% trans "Choose the best match" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "Are the psychologists licensed?" %}')">
                        <h6>✅ {% trans "Credentials" %}</h6>
                        <p>{% trans "Professional verification" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "Is my information confidential?" %}')">
                        <h6>🔒 {% trans "Privacy" %}</h6>
                        <p>{% trans "Data protection info" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "What payment methods do you accept?" %}')">
                        <h6>💳 {% trans "Payment" %}</h6>
                        <p>{% trans "Available payment options" %}</p>
                    </div>
                    
                    <div class="faq-card" onclick="sendFAQQuestion('{% trans "What if I have a mental health emergency?" %}')">
                        <h6>🚨 {% trans "Emergency" %}</h6>
                        <p>{% trans "Crisis support information" %}</p>
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="faq-section">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-life-ring me-2"></i>
                    {% trans "Need More Help?" %}
                </h5>
                <p class="text-muted mb-3">{% trans "Can't find what you're looking for?" %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'contact' %}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                    </a>
                    <a href="{% url 'psychologist_list' %}" class="btn btn-outline-success">
                        <i class="fas fa-user-md me-2"></i>{% trans "Browse Psychologists" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Function to get CSRF token from cookies
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const typingIndicator = document.getElementById('typing-indicator');

    // Send message function
    function sendMessage(message) {
        if (!message.trim()) return;

        // Add user message to chat
        addMessage(message, 'user');

        // Clear input
        userInput.value = '';

        // Show typing indicator
        showTypingIndicator();

        // Send to API
        fetch('{% url "chatbot_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();
            if (data.response) {
                addMessage(data.response, 'bot');
            } else {
                addMessage('{% trans "Sorry, I encountered an error. Please try again." %}', 'bot');
            }
        })
        .catch(error => {
            hideTypingIndicator();
            console.error('Error:', error);
            addMessage('{% trans "Sorry, I encountered an error. Please try again." %}', 'bot');
        });
    }

    // Add message to chat
    function addMessage(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        if (sender === 'user') {
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${message}
                </div>
                <div class="message-avatar user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
            messageDiv.classList.add('user');
        } else {
            messageDiv.innerHTML = `
                <div class="message-avatar bot-avatar">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="message-content">
                    ${message}
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Show typing indicator
    function showTypingIndicator() {
        typingIndicator.style.display = 'flex';
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        typingIndicator.style.display = 'none';
    }

    // Event listeners
    sendBtn.addEventListener('click', function() {
        sendMessage(userInput.value);
    });

    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage(userInput.value);
        }
    });

    // Global functions for FAQ and quick actions
    window.sendFAQQuestion = function(question) {
        sendMessage(question);
    };

    window.sendQuickMessage = function(message) {
        sendMessage(message);
    };

    // Auto-focus input
    userInput.focus();
});
</script>
{% endblock %}
