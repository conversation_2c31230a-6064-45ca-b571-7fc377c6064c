from django.urls import path
from . import views

app_name = 'custom_admin'

urlpatterns = [
    path('', views.admin_dashboard, name='dashboard'),
    path('analytics-api/', views.analytics_api, name='analytics_api'),

    # Moderation
    path('moderate/discussions/', views.moderate_discussions, name='moderate_discussions'),
    path('moderate/resources/', views.moderate_resources, name='moderate_resources'),

    # User Management
    path('users/', views.manage_users, name='manage_users'),
    path('complaints/', views.handle_complaints, name='handle_complaints'),

    # Actions
    path('approve-psychologist/<int:user_id>/', views.approve_psychologist, name='approve_psychologist'),
    path('ban-user/<int:user_id>/', views.ban_user, name='ban_user'),
    path('approve-resource/<int:resource_id>/', views.approve_resource, name='approve_resource'),
    path('resolve-complaint/<int:report_id>/', views.resolve_complaint, name='resolve_complaint'),
]
