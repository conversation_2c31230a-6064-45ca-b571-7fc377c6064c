{% extends 'base.html' %}
{% load static %}

{% block title %}Private Chat - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: 80vh;
        display: flex;
        flex-direction: column;
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
    }
    
    .chat-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: between;
    }
    
    .chat-user-info {
        display: flex;
        align-items: center;
    }
    
    .chat-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
    }
    
    .user-status {
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        opacity: 0.9;
    }
    
    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }
    
    .status-online { background: var(--success); }
    .status-offline { background: var(--gray-400); }
    .status-typing { background: var(--warning); }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: var(--spacing-lg);
        background: var(--gray-50);
    }
    
    .message {
        margin-bottom: var(--spacing-lg);
        display: flex;
        animation: fadeInUp 0.3s ease;
    }
    
    .message.sent {
        justify-content: flex-end;
    }
    
    .message.received {
        justify-content: flex-start;
    }
    
    .message-content {
        max-width: 70%;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        position: relative;
    }
    
    .message.sent .message-content {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        border-bottom-right-radius: var(--radius-sm);
    }
    
    .message.received .message-content {
        background: var(--white);
        color: var(--gray-700);
        border: 1px solid var(--gray-200);
        border-bottom-left-radius: var(--radius-sm);
    }
    
    .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 0.25rem;
    }
    
    .message.sent .message-time {
        text-align: right;
        color: rgba(255, 255, 255, 0.8);
    }
    
    .message.received .message-time {
        color: var(--gray-500);
    }
    
    .typing-indicator {
        display: none;
        align-items: center;
        padding: var(--spacing-md);
        color: var(--gray-500);
        font-style: italic;
    }
    
    .typing-dots {
        display: inline-flex;
        gap: 0.25rem;
        margin-left: 0.5rem;
    }
    
    .typing-dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: var(--gray-400);
        animation: typingDot 1.4s infinite ease-in-out;
    }
    
    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    
    .chat-input-section {
        padding: var(--spacing-lg);
        background: var(--white);
        border-top: 1px solid var(--gray-200);
    }
    
    .chat-input-container {
        display: flex;
        gap: var(--spacing-md);
        align-items: flex-end;
    }
    
    .chat-input {
        flex: 1;
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        resize: none;
        max-height: 120px;
        transition: all 0.3s ease;
    }
    
    .chat-input:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .chat-send-btn {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        border: none;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .chat-send-btn:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
    }
    
    .chat-send-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .file-upload-btn {
        background: var(--gray-100);
        border: 1px solid var(--gray-300);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .file-upload-btn:hover {
        background: var(--gray-200);
        color: var(--primary-blue);
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes typingDot {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="chat-container">
                <!-- Chat Header -->
                <div class="chat-header">
                    <div class="chat-user-info">
                        <div class="chat-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">{{ other_user.get_full_name|default:other_user.username }}</h5>
                            <div class="user-status" id="userStatus">
                                <div class="status-dot status-offline" id="statusDot"></div>
                                <span id="statusText">Offline</span>
                            </div>
                        </div>
                    </div>
                    <div class="ms-auto">
                        <button class="btn btn-outline-light btn-sm" onclick="window.history.back()">
                            <i class="fas fa-arrow-left me-2"></i>Back
                        </button>
                    </div>
                </div>
                
                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% for message in messages %}
                    <div class="message {% if message.sender == request.user %}sent{% else %}received{% endif %}">
                        <div class="message-content">
                            <div>{{ message.content }}</div>
                            <div class="message-time">{{ message.created_at|date:"H:i" }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Typing Indicator -->
                    <div class="typing-indicator" id="typingIndicator">
                        <span id="typingUser"></span> is typing
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="chat-input-section">
                    <div class="chat-input-container">
                        <button class="file-upload-btn" type="button" title="Attach file">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <textarea 
                            class="chat-input" 
                            id="messageInput" 
                            placeholder="Type your message..."
                            rows="1"
                        ></textarea>
                        <button class="chat-send-btn" id="sendButton" type="button" title="Send message">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roomName = '{{ room_name }}';
    const currentUserId = {{ request.user.id }};
    const otherUserId = {{ other_user.id }};
    const otherUserName = '{{ other_user.get_full_name|default:other_user.username }}';
    
    // WebSocket connection
    const chatSocket = new WebSocket(
        'ws://' + window.location.host + '/ws/chat/' + roomName + '/'
    );
    
    const chatMessages = document.getElementById('chatMessages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const typingIndicator = document.getElementById('typingIndicator');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    let typingTimer;
    let isTyping = false;
    
    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
        
        // Handle typing indicator
        if (!isTyping) {
            isTyping = true;
            chatSocket.send(JSON.stringify({
                'type': 'typing',
                'is_typing': true
            }));
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            isTyping = false;
            chatSocket.send(JSON.stringify({
                'type': 'typing',
                'is_typing': false
            }));
        }, 1000);
    });
    
    // Send message on Enter (but allow Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    sendButton.addEventListener('click', sendMessage);
    
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
            chatSocket.send(JSON.stringify({
                'type': 'message',
                'message': message,
                'recipient_id': otherUserId
            }));
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // Stop typing indicator
            if (isTyping) {
                isTyping = false;
                chatSocket.send(JSON.stringify({
                    'type': 'typing',
                    'is_typing': false
                }));
            }
        }
    }
    
    chatSocket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        
        if (data.type === 'message') {
            addMessage(data);
        } else if (data.type === 'typing') {
            handleTypingIndicator(data);
        } else if (data.type === 'user_status') {
            handleUserStatus(data);
        }
    };
    
    function addMessage(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${data.sender_id === currentUserId ? 'sent' : 'received'}`;
        
        const time = new Date(data.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <div>${data.message}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        scrollToBottom();
    }
    
    function handleTypingIndicator(data) {
        if (data.user_id !== currentUserId) {
            const typingUser = document.getElementById('typingUser');
            typingUser.textContent = data.username;
            
            if (data.is_typing) {
                typingIndicator.style.display = 'flex';
            } else {
                typingIndicator.style.display = 'none';
            }
            scrollToBottom();
        }
    }
    
    function handleUserStatus(data) {
        if (data.user_id === otherUserId) {
            statusDot.className = `status-dot status-${data.status}`;
            statusText.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
        }
    }
    
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    chatSocket.onclose = function(e) {
        console.error('Chat socket closed unexpectedly');
    };
    
    // Initial scroll to bottom
    scrollToBottom();
});
</script>
{% endblock %}
