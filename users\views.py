from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, authenticate
from django.contrib import messages
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.mail import send_mail
from django.urls import reverse
from django.utils import timezone
from django.http import HttpResponse
from datetime import timedelta
import uuid

from .forms import (UserRegisterForm, PsychologistRegistrationForm,
                   CustomPasswordResetForm, UserReportForm, AvailabilityForm)
from .models import CustomUser, Specialty, UserReport, PsychologistAvailability

# ---------- Register ----------
def register_view(request):
    if request.method == 'POST':
        form = UserRegisterForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.is_active = False  # Deactivate until email verification
            user.save()

            # Send verification email
            send_verification_email(user, request)

            messages.success(request,
                "Registration successful! Please check your email to verify your account.")
            return redirect('login')
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = UserRegisterForm()
    return render(request, 'users/register.html', {'form': form})


def psychologist_register_view(request):
    """Separate registration for psychologists"""
    if request.method == 'POST':
        form = PsychologistRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            user = form.save(commit=False)
            user.is_active = False  # Deactivate until email verification
            user.save()

            # Send verification email
            send_verification_email(user, request)

            messages.success(request,
                "Registration successful! Please check your email to verify your account. "
                "Your psychologist credentials will be reviewed by our admin team.")
            return redirect('login')
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = PsychologistRegistrationForm()
    return render(request, 'users/psychologist_register.html', {'form': form})


def send_verification_email(user, request):
    """Send email verification"""
    verification_url = request.build_absolute_uri(
        reverse('verify_email', kwargs={'token': user.email_verification_token})
    )

    subject = 'Verify your Psychology Platform account'
    message = f"""
    Hi {user.first_name},

    Thank you for registering with our Psychology Platform!

    Please click the link below to verify your email address:
    {verification_url}

    This link will expire in 24 hours.

    If you didn't create this account, please ignore this email.

    Best regards,
    Psychology Platform Team
    """

    send_mail(subject, message, '<EMAIL>', [user.email])


def verify_email(request, token):
    """Verify user email"""
    try:
        user = CustomUser.objects.get(email_verification_token=token)
        if user.email_verified_at:
            messages.info(request, "Your email is already verified.")
        else:
            user.is_active = True
            user.email_verified_at = timezone.now()
            user.is_verified = True
            user.save()
            messages.success(request, "Email verified successfully! You can now log in.")
        return redirect('login')
    except CustomUser.DoesNotExist:
        messages.error(request, "Invalid verification link.")
        return redirect('register')

# ---------- Login ----------
def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()

            # Check if email is verified
            if not user.is_verified:
                messages.error(request, "Please verify your email before logging in.")
                return render(request, 'users/login.html', {'form': form})

            login(request, user)

            # Role-based redirect
            if user.role == 'psychologist':
                if not user.psychologist_approved:
                    messages.warning(request, "Your psychologist account is pending approval.")
                return redirect('psychologist_dashboard')
            elif user.role == 'end_user':
                return redirect('enduser_dashboard')
            elif user.role == 'admin':
                return redirect('/admin/')
            elif user.role == 'guest':
                return redirect('discussion_list')  # Guests can only access discussions
            else:
                return redirect('login')

        else:
            messages.error(request, "Invalid username or password.")
    else:
        form = AuthenticationForm()

    return render(request, 'users/login.html', {'form': form})


def password_reset_request(request):
    """Request password reset"""
    if request.method == 'POST':
        email = request.POST.get('email')
        try:
            user = CustomUser.objects.get(email=email, is_active=True)

            # Generate reset token
            user.password_reset_token = uuid.uuid4()
            user.password_reset_expires = timezone.now() + timedelta(hours=24)
            user.save()

            # Send reset email
            reset_url = request.build_absolute_uri(
                reverse('password_reset_confirm', kwargs={'token': user.password_reset_token})
            )

            subject = 'Password Reset - Psychology Platform'
            message = f"""
            Hi {user.first_name},

            You requested a password reset for your Psychology Platform account.

            Click the link below to reset your password:
            {reset_url}

            This link will expire in 24 hours.

            If you didn't request this reset, please ignore this email.

            Best regards,
            Psychology Platform Team
            """

            send_mail(subject, message, '<EMAIL>', [user.email])

            messages.success(request, "Password reset email sent! Check your inbox.")
            return redirect('login')

        except CustomUser.DoesNotExist:
            messages.error(request, "No account found with that email address.")

    return render(request, 'users/password_reset_request.html')


def password_reset_confirm(request, token):
    """Confirm password reset"""
    try:
        user = CustomUser.objects.get(
            password_reset_token=token,
            password_reset_expires__gt=timezone.now()
        )

        if request.method == 'POST':
            password1 = request.POST.get('password1')
            password2 = request.POST.get('password2')

            if password1 and password1 == password2:
                user.set_password(password1)
                user.password_reset_token = None
                user.password_reset_expires = None
                user.save()

                messages.success(request, "Password reset successful! You can now log in.")
                return redirect('login')
            else:
                messages.error(request, "Passwords don't match.")

        return render(request, 'users/password_reset_confirm.html', {'token': token})

    except CustomUser.DoesNotExist:
        messages.error(request, "Invalid or expired reset link.")
        return redirect('password_reset_request')

# ---------- Logout ----------
def logout_view(request):
    logout(request)
    return redirect('login')

# ---------- End User Dashboard ----------
@login_required
def enduser_dashboard(request):
    print("✅ Accessed enduser dashboard as:", request.user.role)  # 🔍 Debug print
    return render(request, 'users/enduser_dashboard.html')

# ---------- Psychologist Dashboard ----------
@login_required
def psychologist_dashboard(request):
    if request.user.role != 'psychologist':
        return redirect('login')

    context = {
        'user': request.user,
        'is_approved': request.user.psychologist_approved,
        'pending_consultations': request.user.assigned_consultations.filter(status='pending').count(),
        'total_consultations': request.user.assigned_consultations.count(),
    }
    return render(request, 'users/psychologist_dashboard.html', context)


# ---------- Admin Functions ----------
@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_psychologist_approvals(request):
    """Admin view to approve psychologist accounts"""
    pending_psychologists = CustomUser.objects.filter(
        role='psychologist',
        psychologist_approved=False,
        is_verified=True
    )

    if request.method == 'POST':
        psychologist_id = request.POST.get('psychologist_id')
        action = request.POST.get('action')

        try:
            psychologist = CustomUser.objects.get(id=psychologist_id, role='psychologist')

            if action == 'approve':
                psychologist.psychologist_approved = True
                psychologist.psychologist_approval_date = timezone.now()
                psychologist.save()

                # Send approval email
                send_mail(
                    'Psychologist Account Approved',
                    f'Hi {psychologist.first_name},\n\nYour psychologist account has been approved! You can now offer consultations on our platform.',
                    '<EMAIL>',
                    [psychologist.email]
                )

                messages.success(request, f"Approved {psychologist.get_full_name()}")

            elif action == 'reject':
                psychologist.delete()  # Or set inactive
                messages.success(request, f"Rejected {psychologist.get_full_name()}")

        except CustomUser.DoesNotExist:
            messages.error(request, "Psychologist not found.")

    return render(request, 'users/admin_psychologist_approvals.html', {
        'pending_psychologists': pending_psychologists
    })


@login_required
def report_user(request, user_id):
    """Report a user for inappropriate behavior"""
    reported_user = get_object_or_404(CustomUser, id=user_id)

    if request.method == 'POST':
        form = UserReportForm(request.POST)
        if form.is_valid():
            report = form.save(commit=False)
            report.reporter = request.user
            report.reported_user = reported_user
            report.save()

            messages.success(request, "Report submitted successfully. We'll review it shortly.")
            return redirect('user_profile', user_id=user_id)
    else:
        form = UserReportForm()

    return render(request, 'users/report_user.html', {
        'form': form,
        'reported_user': reported_user
    })


@user_passes_test(lambda u: u.is_staff or u.role == 'admin')
def admin_user_reports(request):
    """Admin view to manage user reports"""
    reports = UserReport.objects.filter(resolved=False).order_by('-created_at')

    if request.method == 'POST':
        report_id = request.POST.get('report_id')
        action = request.POST.get('action')

        try:
            report = UserReport.objects.get(id=report_id)

            if action == 'resolve':
                report.resolved = True
                report.resolved_by = request.user
                report.resolved_at = timezone.now()
                report.save()
                messages.success(request, "Report resolved.")

            elif action == 'ban_user':
                report.reported_user.is_active = False
                report.reported_user.save()
                report.resolved = True
                report.resolved_by = request.user
                report.resolved_at = timezone.now()
                report.save()
                messages.success(request, f"User {report.reported_user.username} has been banned.")

        except UserReport.DoesNotExist:
            messages.error(request, "Report not found.")

    return render(request, 'users/admin_user_reports.html', {'reports': reports})

def psychologist_list(request):
    specialty = request.GET.get('specialty')
    age_group = request.GET.get('age_group')
    min_experience = request.GET.get('min_experience')
    min_rating = request.GET.get('min_rating')

    psychologists = CustomUser.objects.filter(role='psychologist', is_verified=True)

    if specialty:
        psychologists = psychologists.filter(specialties__name=specialty)

    if age_group:
        psychologists = psychologists.filter(age_groups_served__icontains=age_group)

    if min_experience:
        psychologists = psychologists.filter(experience_years__gte=int(min_experience))

    if min_rating:
        psychologists = psychologists.filter(rating__gte=float(min_rating))

    specialties = Specialty.objects.all()
    age_groups = ['Children', 'Teens', 'Adults', 'Seniors']

    return render(request, 'users/psychologist_list.html', {
        'psychologists': psychologists,
        'specialties': specialties,
        'age_groups': age_groups,
    })

def psychologist_detail(request, user_id):
    psych = get_object_or_404(CustomUser, id=user_id, role='psychologist', is_verified=True)
    return render(request, 'users/psychologist_detail.html', {'psych': psych})
