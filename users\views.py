from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib import messages
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required
from .forms import UserRegisterForm
from users.models import CustomUser, Specialty  # If needed

# ---------- Register ----------
def register_view(request):
    if request.method == 'POST':
        form = UserRegisterForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, "Registration successful.")
            return redirect('login')
        else:
            messages.error(request, "Invalid information.")
    else:
        form = UserRegisterForm()
    return render(request, 'users/register.html', {'form': form})

# ---------- Login ----------
def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            print("✅ Login successful — Logged in as:", user.role)  # ✅ DEBUG

            login(request, user)

            # Role-based redirect
            if user.role == 'psychologist':
               return redirect('psychologist_dashboard')
            elif user.role == 'end_user':  # <-- fix this
               return redirect('enduser_dashboard')
            elif user.role == 'admin':
               return redirect('/admin/')
            else:
               return redirect('login')

        else:
            print("❌ Login failed:", form.errors)  # ❌ DEBUG
            messages.error(request, "Invalid username or password.")
    else:
        form = AuthenticationForm()

    return render(request, 'users/login.html', {'form': form})

# ---------- Logout ----------
def logout_view(request):
    logout(request)
    return redirect('login')

# ---------- End User Dashboard ----------
@login_required
def enduser_dashboard(request):
    print("✅ Accessed enduser dashboard as:", request.user.role)  # 🔍 Debug print
    return render(request, 'users/enduser_dashboard.html')

# ---------- Psychologist Dashboard ----------
@login_required
def psychologist_dashboard(request):
    if request.user.role != 'psychologist':
        return redirect('login')
    return redirect('psychologist_consultations')

def psychologist_list(request):
    specialty = request.GET.get('specialty')
    age_group = request.GET.get('age_group')
    min_experience = request.GET.get('min_experience')
    min_rating = request.GET.get('min_rating')

    psychologists = CustomUser.objects.filter(role='psychologist', is_verified=True)

    if specialty:
        psychologists = psychologists.filter(specialties__name=specialty)

    if age_group:
        psychologists = psychologists.filter(age_groups_served__icontains=age_group)

    if min_experience:
        psychologists = psychologists.filter(experience_years__gte=int(min_experience))

    if min_rating:
        psychologists = psychologists.filter(rating__gte=float(min_rating))

    specialties = Specialty.objects.all()
    age_groups = ['Children', 'Teens', 'Adults', 'Seniors']

    return render(request, 'users/psychologist_list.html', {
        'psychologists': psychologists,
        'specialties': specialties,
        'age_groups': age_groups,
    })

def psychologist_detail(request, user_id):
    psych = get_object_or_404(CustomUser, id=user_id, role='psychologist', is_verified=True)
    return render(request, 'users/psychologist_detail.html', {'psych': psych})
