from django.urls import path
from . import views

urlpatterns = [
    path('request/', views.request_consultation, name='request_consultation'),
    path('my/', views.my_consultations, name='my_consultations'),
    path('psychologist/', views.psychologist_consultations, name='psychologist_consultations'),
    path('psychologist/update/<int:consultation_id>/', views.update_consultation_status, name='update_consultation_status'),
    path('admin/view-all/', views.admin_consultation_list, name='admin_consultation_list'),
    path('admin/assign/<int:consultation_id>/', views.assign_psychologist, name='assign_psychologist'),
    path('rate/<int:consultation_id>/', views.rate_consultation, name='rate_consultation'),

    # New enhanced features
    path('detail/<int:consultation_id>/', views.consultation_detail, name='consultation_detail'),
    path('book/<int:psychologist_id>/', views.calendar_booking, name='calendar_booking'),
    path('api/slots/<int:psychologist_id>/', views.get_available_slots_api, name='get_available_slots_api'),
    path('psychologist/time-slots/', views.manage_time_slots, name='manage_time_slots'),





    # API endpoints for consultation booking
    path('api/psychologists/', views.api_psychologists, name='api_psychologists'),
]
