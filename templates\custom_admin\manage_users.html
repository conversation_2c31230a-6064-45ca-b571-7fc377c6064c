{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Users - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .admin-container {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .admin-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 2rem;
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--white);
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        text-align: center;
        border-left: 4px solid var(--primary-blue);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .user-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-md);
        border-left: 4px solid var(--primary-blue);
    }
    
    .user-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }
    
    .user-info {
        display: flex;
        align-items: center;
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
    }
    
    .user-actions {
        display: flex;
        gap: var(--spacing-sm);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-active { background: rgba(16, 185, 129, 0.1); color: var(--success); }
    .status-banned { background: rgba(239, 68, 68, 0.1); color: var(--danger); }
    .status-pending { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
    .status-approved { background: rgba(16, 185, 129, 0.1); color: var(--success); }
    
    .section-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }
    
    .section-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .section-content {
        padding: var(--spacing-lg);
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-users me-3"></i>
                        User Management
                    </h1>
                    <p class="mb-0 opacity-90">Manage users, approve psychologists, and handle user accounts</p>
                </div>
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_users }}</div>
                <div class="text-muted">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ active_users }}</div>
                <div class="text-muted">Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ banned_users }}</div>
                <div class="text-muted">Banned Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ pending_psychologists.count }}</div>
                <div class="text-muted">Pending Approvals</div>
            </div>
        </div>

        <!-- Pending Psychologist Approvals -->
        {% if pending_psychologists %}
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-user-md me-2 text-warning"></i>
                    Pending Psychologist Approvals
                </h4>
            </div>
            <div class="section-content">
                {% for psychologist in pending_psychologists %}
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ psychologist.get_full_name|default:psychologist.username }}</h6>
                                <p class="mb-1 text-muted">{{ psychologist.email }}</p>
                                <div class="d-flex gap-2">
                                    <span class="status-badge status-pending">Pending Approval</span>
                                    <small class="text-muted">Joined {{ psychologist.date_joined|date:"M d, Y" }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="user-actions">
                            <form method="post" action="{% url 'custom_admin:approve_psychologist' psychologist.id %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Approve this psychologist?')">
                                    <i class="fas fa-check me-1"></i>Approve
                                </button>
                            </form>
                            <form method="post" action="{% url 'custom_admin:ban_user' psychologist.id %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Ban this user?')">
                                    <i class="fas fa-ban me-1"></i>Ban
                                </button>
                            </form>
                        </div>
                    </div>
                    {% if psychologist.bio %}
                    <div class="mt-3">
                        <strong>Bio:</strong> {{ psychologist.bio|truncatewords:30 }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Approved Psychologists -->
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-user-check me-2 text-success"></i>
                    Recently Approved Psychologists
                </h4>
            </div>
            <div class="section-content">
                {% for psychologist in approved_psychologists %}
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user-md text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ psychologist.get_full_name|default:psychologist.username }}</h6>
                                <p class="mb-1 text-muted">{{ psychologist.email }}</p>
                                <div class="d-flex gap-2">
                                    <span class="status-badge status-approved">Approved</span>
                                    <small class="text-muted">Joined {{ psychologist.date_joined|date:"M d, Y" }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="user-actions">
                            <form method="post" action="{% url 'custom_admin:ban_user' psychologist.id %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Ban this user?')">
                                    <i class="fas fa-ban me-1"></i>Ban
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No approved psychologists yet.</p>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Users -->
        <div class="section-card">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-users me-2 text-info"></i>
                    Recent Users
                </h4>
            </div>
            <div class="section-content">
                {% for user in recent_users %}
                <div class="user-card">
                    <div class="user-header">
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                <p class="mb-1 text-muted">{{ user.email }}</p>
                                <div class="d-flex gap-2">
                                    <span class="status-badge {% if user.is_active %}status-active{% else %}status-banned{% endif %}">
                                        {% if user.is_active %}Active{% else %}Banned{% endif %}
                                    </span>
                                    <span class="badge bg-secondary">{{ user.get_role_display }}</span>
                                    <small class="text-muted">Joined {{ user.date_joined|date:"M d, Y" }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="user-actions">
                            <form method="post" action="{% url 'custom_admin:ban_user' user.id %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn {% if user.is_active %}btn-warning{% else %}btn-success{% endif %} btn-sm" 
                                        onclick="return confirm('{% if user.is_active %}Ban{% else %}Unban{% endif %} this user?')">
                                    <i class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %} me-1"></i>
                                    {% if user.is_active %}Ban{% else %}Unban{% endif %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
