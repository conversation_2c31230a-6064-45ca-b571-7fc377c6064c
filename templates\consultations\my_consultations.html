{% extends 'base.html' %}
{% block content %}

<div class="max-w-4xl mx-auto px-4 py-10">
  <h2 class="text-3xl font-bold mb-6 text-center">📋 My Consultations</h2>

  {% if consultations %}
    <ul class="space-y-6">
      {% for c in consultations %}
        <li class="bg-white rounded-lg shadow p-6 border border-gray-200">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-lg font-semibold text-indigo-700">{{ c.topic }}</h3>
              <p class="text-sm text-gray-600">{{ c.type|title }} Consultation</p>
              <p class="text-sm mt-1">Status: <span class="font-medium">{{ c.status|title }}</span></p>
              {% if c.scheduled_date %}
                <p class="text-sm">Scheduled: {{ c.scheduled_date }}</p>
              {% endif %}
            </div>

            <!-- Rating badge -->
            {% if c.status == 'completed' and not c.rating %}
              <a href="{% url 'rate_consultation' c.id %}" class="text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded-md font-medium">
                ⭐ Rate
              </a>
            {% elif c.rating %}
              <p class="text-sm text-yellow-600 font-semibold">
                ⭐ Rated: {{ c.rating }}/5
              </p>
            {% endif %}
          </div>

          {% if c.feedback %}
            <div class="mt-3 bg-gray-50 p-3 rounded-md text-sm text-gray-700">
              <strong>Feedback:</strong> {{ c.feedback }}
            </div>
          {% endif %}
        </li>
      {% endfor %}
    </ul>
  {% else %}
    <div class="text-center text-gray-600 mt-10">
      <p>No consultations submitted yet.</p>
      <a href="{% url 'request_consultation' %}" class="text-indigo-600 underline hover:text-indigo-800 mt-2 inline-block">Request your first consultation</a>
    </div>
  {% endif %}
</div>

{% endblock %}
