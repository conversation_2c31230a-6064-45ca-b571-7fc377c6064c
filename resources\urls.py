from django.urls import path
from . import views

urlpatterns = [
    # Resource browsing
    path('', views.resource_list, name='resource_list'),
    path('category/<int:category_id>/', views.resource_by_category, name='resource_by_category'),
    path('resource/<int:resource_id>/', views.resource_detail, name='resource_detail'),
    path('download/<int:resource_id>/', views.download_resource, name='download_resource'),
    
    # Resource management (psychologists)
    path('upload/', views.upload_resource, name='upload_resource'),
    path('my-resources/', views.my_resources, name='my_resources'),
    path('edit/<int:resource_id>/', views.edit_resource, name='edit_resource'),
    path('delete/<int:resource_id>/', views.delete_resource, name='delete_resource'),
    
    # User interactions
    path('bookmark/<int:resource_id>/', views.toggle_bookmark, name='toggle_bookmark'),
    path('rate/<int:resource_id>/', views.rate_resource, name='rate_resource'),
    path('bookmarks/', views.my_bookmarks, name='my_bookmarks'),
    
    # Admin
    path('admin/pending/', views.admin_pending_resources, name='admin_pending_resources'),
    path('admin/approve/<int:resource_id>/', views.admin_approve_resource, name='admin_approve_resource'),
]
