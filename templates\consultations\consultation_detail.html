{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Consultation Details" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .detail-hero {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .detail-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .detail-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(37, 99, 235, 0.1);
    }
    
    .card-header-custom {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .status-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .status-pending { 
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); 
        color: var(--white); 
    }
    .status-approved { 
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); 
        color: var(--white); 
    }
    .status-completed { 
        background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
        color: var(--white); 
    }
    .status-rejected { 
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); 
        color: var(--white); 
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .info-item {
        background: #f8fafc;
        padding: 1.5rem;
        border-radius: 16px;
        border-left: 4px solid #2563eb;
    }
    
    .info-label {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        color: #1e293b;
        font-size: 1.1rem;
        font-weight: 600;
    }
    
    .psychologist-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        border: 1px solid #e2e8f0;
    }
    
    .psychologist-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 2rem;
        margin: 0 auto 1rem;
    }
    
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e2e8f0;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 2rem;
        background: var(--white);
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -2.25rem;
        top: 1.5rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #2563eb;
        border: 3px solid var(--white);
        box-shadow: 0 0 0 3px #e2e8f0;
    }
    
    .timeline-date {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .timeline-title {
        color: #1e293b;
        font-weight: 600;
        margin: 0.5rem 0;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 2rem;
    }
    
    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: var(--white);
    }
    
    .btn-success-action {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: var(--white);
    }
    
    .btn-warning-action {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        color: var(--white);
    }
    
    .btn-danger-action {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: var(--white);
    }
    
    .description-box {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .meeting-info {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        border: 1px solid #3b82f6;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .meeting-link {
        background: var(--white);
        border: 1px solid #3b82f6;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        display: flex;
        align-items: center;
        justify-content: between;
        gap: 1rem;
    }
    
    .copy-btn {
        background: #3b82f6;
        color: var(--white);
        border: none;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .copy-btn:hover {
        background: #2563eb;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="detail-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row justify-content-between align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-6 fw-bold mb-3">
                        <i class="fas fa-calendar-check me-3"></i>
                        {% trans "Consultation Details" %}
                    </h1>
                    <p class="lead mb-0">{{ consultation.topic }}</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="status-badge status-{{ consultation.status }}">
                        <i class="fas fa-circle"></i>
                        {{ consultation.get_status_display }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Consultation Information -->
            <div class="detail-card">
                <div class="card-header-custom">
                    <h3 class="text-primary mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Session Information" %}
                    </h3>
                    <p class="text-muted mb-0">{% trans "Details about your consultation session" %}</p>
                </div>
                
                <div class="p-4">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">{% trans "Session Type" %}</div>
                            <div class="info-value">{{ consultation.get_type_display }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">{% trans "Topic" %}</div>
                            <div class="info-value">{{ consultation.topic }}</div>
                        </div>
                        
                        {% if consultation.scheduled_date %}
                        <div class="info-item">
                            <div class="info-label">{% trans "Scheduled Date" %}</div>
                            <div class="info-value">{{ consultation.scheduled_date|date:"F d, Y" }}</div>
                        </div>
                        {% endif %}
                        
                        {% if consultation.scheduled_time %}
                        <div class="info-item">
                            <div class="info-label">{% trans "Scheduled Time" %}</div>
                            <div class="info-value">{{ consultation.scheduled_time|time:"g:i A" }}</div>
                        </div>
                        {% endif %}
                        
                        <div class="info-item">
                            <div class="info-label">{% trans "Duration" %}</div>
                            <div class="info-value">{{ consultation.duration_minutes|default:"50" }} {% trans "minutes" %}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">{% trans "Created" %}</div>
                            <div class="info-value">{{ consultation.created_at|date:"F d, Y" }}</div>
                        </div>
                    </div>
                    
                    {% if consultation.description %}
                    <div class="description-box">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-comment-alt me-2"></i>
                            {% trans "Description" %}
                        </h6>
                        <p class="mb-0">{{ consultation.description }}</p>
                    </div>
                    {% endif %}
                    
                    {% if consultation.meeting_link and consultation.status == 'approved' %}
                    <div class="meeting-info">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-video me-2"></i>
                            {% trans "Meeting Information" %}
                        </h6>
                        <p class="mb-2">{% trans "Your consultation session is ready. Use the link below to join:" %}</p>
                        <div class="meeting-link">
                            <span class="flex-grow-1">{{ consultation.meeting_link }}</span>
                            <button class="copy-btn" onclick="copyToClipboard('{{ consultation.meeting_link }}')">
                                <i class="fas fa-copy me-1"></i>{% trans "Copy" %}
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Timeline -->
            {% if consultation.history.exists %}
            <div class="detail-card">
                <div class="card-header-custom">
                    <h3 class="text-primary mb-2">
                        <i class="fas fa-history me-2"></i>
                        {% trans "Consultation Timeline" %}
                    </h3>
                    <p class="text-muted mb-0">{% trans "Track the progress of your consultation" %}</p>
                </div>
                
                <div class="p-4">
                    <div class="timeline">
                        {% for history in consultation.history.all %}
                        <div class="timeline-item">
                            <div class="timeline-date">{{ history.changed_at|date:"F d, Y g:i A" }}</div>
                            <div class="timeline-title">
                                {% trans "Status changed from" %} {{ history.previous_status }} {% trans "to" %} {{ history.new_status }}
                            </div>
                            {% if history.changed_by %}
                            <small class="text-muted">{% trans "by" %} {{ history.changed_by.get_full_name|default:history.changed_by.username }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <div class="timeline-item">
                            <div class="timeline-date">{{ consultation.created_at|date:"F d, Y g:i A" }}</div>
                            <div class="timeline-title">{% trans "Consultation requested" %}</div>
                            <small class="text-muted">{% trans "by" %} {{ consultation.user.get_full_name|default:consultation.user.username }}</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Psychologist Information -->
            {% if consultation.psychologist %}
            <div class="detail-card">
                <div class="card-header-custom">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-user-md me-2"></i>
                        {% trans "Your Psychologist" %}
                    </h5>
                </div>
                
                <div class="p-4">
                    <div class="psychologist-card">
                        <div class="psychologist-avatar">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h5 class="mb-2">Dr. {{ consultation.psychologist.get_full_name|default:consultation.psychologist.username }}</h5>
                        {% if consultation.psychologist.profile.specialization %}
                        <p class="text-muted mb-3">{{ consultation.psychologist.profile.specialization }}</p>
                        {% endif %}
                        <a href="{% url 'psychologist_profile' consultation.psychologist.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>{% trans "View Profile" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Actions -->
            <div class="detail-card">
                <div class="card-header-custom">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        {% trans "Actions" %}
                    </h5>
                </div>
                
                <div class="p-4">
                    <div class="action-buttons">
                        {% if consultation.status == 'pending' %}
                        <button class="btn btn-action btn-warning-action" onclick="cancelConsultation()">
                            <i class="fas fa-times"></i>
                            {% trans "Cancel" %}
                        </button>
                        {% endif %}
                        
                        {% if consultation.status == 'approved' and consultation.meeting_link %}
                        <a href="{{ consultation.meeting_link }}" class="btn btn-action btn-success-action" target="_blank">
                            <i class="fas fa-video"></i>
                            {% trans "Join Session" %}
                        </a>
                        {% endif %}
                        
                        {% if consultation.status == 'completed' %}
                        <a href="{% url 'rate_consultation' consultation.id %}" class="btn btn-action btn-primary-action">
                            <i class="fas fa-star"></i>
                            {% trans "Rate Session" %}
                        </a>
                        {% endif %}
                        
                        <a href="{% url 'my_consultations' %}" class="btn btn-action btn-primary-action">
                            <i class="fas fa-arrow-left"></i>
                            {% trans "Back to List" %}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Help -->
            <div class="detail-card">
                <div class="card-header-custom">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        {% trans "Need Help?" %}
                    </h5>
                </div>
                
                <div class="p-4">
                    <div class="d-grid gap-2">
                        <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-primary">
                            <i class="fas fa-comments me-2"></i>{% trans "Chat Assistant" %}
                        </a>
                        <a href="{% url 'contact' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('{% trans "Meeting link copied to clipboard!" %}');
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}

function cancelConsultation() {
    if (confirm('{% trans "Are you sure you want to cancel this consultation?" %}')) {
        // Implementation would go here
        alert('{% trans "Consultation cancellation functionality would be implemented here" %}');
    }
}
</script>
{% endblock %}
