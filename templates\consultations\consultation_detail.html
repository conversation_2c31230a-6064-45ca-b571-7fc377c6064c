{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Consultation Details" %} - Ethiopian Psychological Consultation Platform{% endblock %}

{% block extra_css %}
<style>
    .consultation-hero {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .consultation-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .consultation-header {
        background: var(--gray-50);
        padding: 2rem;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .consultation-body {
        padding: 2rem;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: var(--radius-full);
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .status-pending { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
    .status-confirmed { background: rgba(59, 130, 246, 0.1); color: var(--info); }
    .status-completed { background: rgba(16, 185, 129, 0.1); color: var(--success); }
    .status-cancelled { background: rgba(239, 68, 68, 0.1); color: var(--danger); }
    
    .participant-card {
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--primary-blue);
    }
    
    .participant-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin-right: 1rem;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .info-item {
        background: var(--gray-50);
        padding: 1rem;
        border-radius: var(--radius-md);
        border-left: 3px solid var(--primary-blue);
    }
    
    .info-icon {
        font-size: 1.2rem;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .timeline-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .timeline-item {
        display: flex;
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .timeline-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-blue);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .timeline-content {
        flex: 1;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .notes-section {
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-top: 2rem;
        border-left: 4px solid var(--info);
    }
    
    .payment-info {
        background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
        color: var(--white);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="consultation-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'my_consultations' %}" class="text-white-50">{% trans "My Consultations" %}</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{% trans "Consultation Details" %}</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold mb-3">{% trans "Consultation Details" %}</h1>
                <p class="lead mb-0">{{ consultation.date|date:"F d, Y" }} at {{ consultation.time|time:"H:i" }}</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <span class="status-badge status-{{ consultation.status|lower }}">
                    {% if consultation.status == 'pending' %}
                        <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                    {% elif consultation.status == 'confirmed' %}
                        <i class="fas fa-check me-1"></i>{% trans "Confirmed" %}
                    {% elif consultation.status == 'completed' %}
                        <i class="fas fa-check-circle me-1"></i>{% trans "Completed" %}
                    {% elif consultation.status == 'cancelled' %}
                        <i class="fas fa-times-circle me-1"></i>{% trans "Cancelled" %}
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Consultation Overview -->
            <div class="consultation-card">
                <div class="consultation-header">
                    <h3 class="text-primary mb-3">
                        <i class="fas fa-calendar-check me-2"></i>
                        {% trans "Consultation Overview" %}
                    </h3>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div>
                                <strong>{% trans "Date & Time" %}</strong><br>
                                <span class="text-muted">{{ consultation.date|date:"F d, Y" }} at {{ consultation.time|time:"H:i" }}</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <strong>{% trans "Duration" %}</strong><br>
                                <span class="text-muted">50 {% trans "minutes" %}</span>
                            </div>
                        </div>
                        
                        {% if consultation.topic %}
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div>
                                <strong>{% trans "Topic" %}</strong><br>
                                <span class="text-muted">{{ consultation.topic.name }}</span>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div>
                                <strong>{% trans "Session Type" %}</strong><br>
                                <span class="text-muted">{% trans "Video Call" %}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="consultation-body">
                    {% if consultation.description %}
                    <div class="mb-4">
                        <h5 class="text-primary mb-3">{% trans "Description" %}</h5>
                        <p class="text-muted">{{ consultation.description|linebreaks }}</p>
                    </div>
                    {% endif %}
                    
                    {% if consultation.meeting_notes %}
                    <div class="notes-section">
                        <h5 class="text-info mb-3">
                            <i class="fas fa-sticky-note me-2"></i>
                            {% trans "Session Notes" %}
                        </h5>
                        <p class="mb-0">{{ consultation.meeting_notes|linebreaks }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Timeline -->
            {% if history %}
            <div class="timeline-card">
                <h3 class="text-primary mb-4">
                    <i class="fas fa-history me-2"></i>
                    {% trans "Consultation Timeline" %}
                </h3>
                
                {% for event in history %}
                <div class="timeline-item">
                    <div class="timeline-icon">
                        {% if event.status == 'pending' %}
                            <i class="fas fa-clock"></i>
                        {% elif event.status == 'confirmed' %}
                            <i class="fas fa-check"></i>
                        {% elif event.status == 'completed' %}
                            <i class="fas fa-check-circle"></i>
                        {% elif event.status == 'cancelled' %}
                            <i class="fas fa-times"></i>
                        {% else %}
                            <i class="fas fa-circle"></i>
                        {% endif %}
                    </div>
                    <div class="timeline-content">
                        <h6 class="mb-1">{{ event.get_status_display }}</h6>
                        <p class="text-muted mb-1">{{ event.notes|default:"Status updated" }}</p>
                        <small class="text-muted">{{ event.created_at|date:"M d, Y H:i" }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Participants -->
            <div class="participant-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-user-md me-2"></i>
                    {% trans "Psychologist" %}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="participant-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Dr. {{ consultation.psychologist.get_full_name|default:consultation.psychologist.username }}</h6>
                        <p class="text-muted mb-1">{% trans "Licensed Psychologist" %}</p>
                        {% if consultation.psychologist.specializations.all %}
                        <small class="text-muted">{{ consultation.psychologist.specializations.all.0.name }}</small>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'psychologist_detail' consultation.psychologist.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>{% trans "View Profile" %}
                    </a>
                    <a href="{% url 'start_conversation' consultation.psychologist.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-comments me-1"></i>{% trans "Message" %}
                    </a>
                </div>
            </div>
            
            <div class="participant-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-user me-2"></i>
                    {% trans "Client" %}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="participant-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">{{ consultation.user.get_full_name|default:consultation.user.username }}</h6>
                        <p class="text-muted mb-1">{% trans "Client" %}</p>
                        <small class="text-muted">{% trans "Member since" %} {{ consultation.user.date_joined|date:"Y" }}</small>
                    </div>
                </div>
            </div>
            
            <!-- Payment Information -->
            {% if consultation.payment_set.all %}
            {% with payment=consultation.payment_set.first %}
            <div class="payment-info">
                <h5 class="mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    {% trans "Payment Information" %}
                </h5>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{% trans "Amount:" %}</span>
                    <strong>{{ payment.amount }} ETB</strong>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{% trans "Status:" %}</span>
                    <span class="badge bg-light text-dark">{{ payment.get_status_display }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>{% trans "Method:" %}</span>
                    <span>{{ payment.get_payment_method_display|default:"Online" }}</span>
                </div>
                <div class="mt-3">
                    <a href="{% url 'payment_receipt' payment.payment_id %}" class="btn btn-light btn-sm">
                        <i class="fas fa-receipt me-1"></i>{% trans "View Receipt" %}
                    </a>
                </div>
            </div>
            {% endwith %}
            {% endif %}
            
            <!-- Actions -->
            <div class="action-buttons">
                {% if consultation.status == 'pending' and user == consultation.user %}
                <button class="btn btn-warning btn-action" onclick="cancelConsultation()">
                    <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                </button>
                {% endif %}
                
                {% if consultation.status == 'completed' and user == consultation.user %}
                <a href="{% url 'rate_consultation' consultation.id %}" class="btn btn-success btn-action">
                    <i class="fas fa-star me-2"></i>{% trans "Rate Session" %}
                </a>
                {% endif %}
                
                {% if consultation.status == 'confirmed' %}
                <button class="btn btn-primary btn-action" onclick="joinSession()">
                    <i class="fas fa-video me-2"></i>{% trans "Join Session" %}
                </button>
                {% endif %}
                
                <a href="{% url 'my_consultations' %}" class="btn btn-outline-secondary btn-action">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to List" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelConsultation() {
    if (confirm('{% trans "Are you sure you want to cancel this consultation?" %}')) {
        // Implementation would go here
        alert('{% trans "Consultation cancellation functionality would be implemented here" %}');
    }
}

function joinSession() {
    // This would open the video call interface
    alert('{% trans "Video session functionality would be implemented here" %}');
}

// Auto-refresh for pending consultations
{% if consultation.status == 'pending' or consultation.status == 'confirmed' %}
setInterval(function() {
    // Check for status updates
    fetch(window.location.href, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Update status if changed
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newStatus = doc.querySelector('.status-badge');
        const currentStatus = document.querySelector('.status-badge');
        
        if (newStatus && currentStatus && newStatus.textContent !== currentStatus.textContent) {
            location.reload();
        }
    })
    .catch(error => console.log('Status check failed:', error));
}, 30000); // Check every 30 seconds
{% endif %}
</script>
{% endblock %}
